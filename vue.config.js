// const path = require('path');
const vConsolePlugin = require('vconsole-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const TerserPlugin = require("terser-webpack-plugin");
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

const JavaScriptObfuscator = require('webpack-obfuscator');
const obfuscatorConfig = require('./obfuscator.config');

// 导入代理配置生成函数
const getProxyConfig = require('./proxyDev');

module.exports = {
  transpileDependencies: true,
  // publicPath: './',
  outputDir: 'dist',
  css: {
    loaderOptions: {
      sass: { // 只能使用这种方式导入scss全局变量
        additionalData: `@import "@/assets/scss/common.scss";`
      }
    }
  },
  configureWebpack: config => {
    // config.plugins.push(
    //   new BundleAnalyzerPlugin()
    // )
    config.devtool = process.env.VUE_APP_ENV === 'production' ? false : 'source-map';
    if (process.env.VUE_APP_CONSOLE === 'true') {
      config.plugins.push(
        new vConsolePlugin({
          enable: true
        })
      )
    }
    if (process.env.VUE_APP_GZIP === 'true') {
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css)$/, // 匹配文件名
          threshold: 10240, // 对超过10k的数据压缩
          minRatio: 0.8 // 压缩比
        })
      )
    }
    // 只在生产环境混淆，在测试环境混淆会导致无法运行
    // 只针对新包混淆，原有nc包量太大，会影响运行性能
    const notObfuscator = ['newcredit', 'palmcredit', 'xcrosscash', 'xcash'];
    if (process.env.NODE_ENV === 'production' && process.env.VUE_APP_PRODUCT !== '' && !notObfuscator.includes(process.env.VUE_APP_PRODUCT)) {
      config.plugins.push(
        // 第二个数组参数代表忽略混淆的js数组
        new JavaScriptObfuscator(obfuscatorConfig, [])
      )
    }
    config.optimization = {
      splitChunks: {
        cacheGroups: {
          libs: { // 基础类库(不改动，优先打包)
            name: 'basics-libs',
            test:  /[\\/]node_modules[\\/](vue|vue-router|vuex|axios|core-js|vant)[\\/]/,
            priority: -1,
            chunks: 'initial'
          },
          // bussLibs: { // 业务类库(尽量引入业务相关的，尽可能保持不变。)
          //   name: 'buss-libs',
          //   test:  /[\\/]node_modules[\\/](jsencrypt)[\\/]/,
          //   priority: -5,
          //   chunks: 'all'
          // },
          newAddLibs: { // 剩下的库(新增的保持在这里，不变更以上两个库。)
            name: 'new-add-libs',
            test:  /[\\/]node_modules[\\/]/,
            priority: -7,
            chunks: 'all'
          },
          common2: {
            name: 'chunk-common2',
            minChunks: 5,
            priority: -15,
            chunks: 'all',
            reuseExistingChunk: true
          },
          common: {
            name: 'chunk-common',
            minChunks: 2,
            priority: -20,
            chunks: 'all',
            reuseExistingChunk: true
          },
        }
      }
    }
    // 以下配置用于获取引用包的包名，用于上面的优化。
    // config.optimization = {
    //   runtimeChunk: 'single',
    //   splitChunks: {
    //     chunks: 'all',
    //     maxInitialRequests: Infinity,
    //     minSize: 20000,
    //     cacheGroups: {
    //       vendor: {
    //         test: /[\\/]node_modules[\\/]/,
    //         name (module) {
    //           // get the name. E.g. node_modules/packageName/not/this/part.js
    //           // or node_modules/packageName
    //          if (module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)) {
    //           const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
    //           // npm package names are URL-safe, but some servers don't like @ symbols
    //           return `npm.${packageName.replace('@', '')}`
    //          };
    //         }
    //       },
    //     }
    //   }
    // };
    // 只在生产删除log.
    if (process.env.VUE_APP_CONSOLE === 'false') {
      config.optimization.minimizer = [
        new TerserPlugin({
          parallel: 4,
          terserOptions: {
            compress: {
              drop_console: true
            }
          }
        })
      ]
    }
  },
  chainWebpack: config => {
    // config.plugins.delete('preload') // 删除默认的preload
    // config.plugins.delete('prefetch') // 删除默认的prefetch
    if (process.env.NODE_ENV === 'stage') { // 测试环境也需要添加hash，避免频繁清除缓存
      config.output.filename('js/[name].[contenthash:8].js').end();
      config.output.chunkFilename('js/[name].[contenthash:8].js').end();
    }
  },
  // pages: {
  //   repayment: { //提现
  //     // page 的入口
  //     entry: 'src/views/repayment/index.js',
  //     // 模板来源
  //     template: 'public/index.html',
  //     // 在 dist/index.html 的输出
  //     filename: 'repayment.html',
  //     // 当使用 title 选项时，
  //     // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
  //     title: 'repayment Page',
  //     // 在这个页面中包含的块，默认情况下会包含
  //     // 提取出来的通用 chunk 和 vendor chunk。
  //     chunks: ['repayment']
  //   },
  //   withdraw: { // 还款
  //     // page 的入口
  //     entry: 'src/views/withdraw/index.js',
  //     // 模板来源
  //     template: 'public/index.html',
  //     // 在 dist/index.html 的输出
  //     filename: 'withdraw.html',
  //     // 当使用 title 选项时，
  //     // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
  //     title: 'withdraw Page',
  //     // 在这个页面中包含的块，默认情况下会包含
  //     // 提取出来的通用 chunk 和 vendor chunk。
  //     chunks: ['withdraw']
  //   }
  // },
  productionSourceMap: false,
  devServer: {
    // before: require(''),
    // host: 'localhost',
    proxy: getProxyConfig()
  }
}


// const { defineConfig } = require('@vue/cli-service')
// module.exports = defineConfig({
//   transpileDependencies: true
// })
