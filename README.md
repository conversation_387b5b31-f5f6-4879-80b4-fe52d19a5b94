# PalmCredit的H5提现与还款功能


## 开发补充说明

- 使用手机登陆NC系列的测试包（一般使用newcredit）后，使用Android Studio链接手机后，通过logcat面板获取token，uid等信息复制到'src/assets/js/request.js'中，即可开始调试开发。提现有部分功能使用了原生的模拟数据，所以有部分需要使用uid去替换。

## 2025/04 重构方案
https://idzersv4on.feishu.cn/wiki/T457wWoijih786krkqBc1PLMnMd

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run dev
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).


### 功能描述

- 因目前需要依赖客户端的token,在本地联调时，需要使用Android Studio连接手机测试包获取对应的token在本地调试。后续可实现本地登陆的相关逻辑
