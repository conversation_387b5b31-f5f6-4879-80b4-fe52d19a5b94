<template>
  <div v-if="value" class="popup">
    <div class="popup-content">
      <div class="title">
        <div class="tips">Add additional information can increase your <span>credit limit by up to 50%</span></div>
        <img src="@/assets/images/increaseLimit/money.png" />
      </div>
      <!-- 只是调整显示名称Type 1与Type 2.逻辑不变更 -->
      <div class="type">Type 1: Must be uploaded</div>
      <ul class="list">
        <li v-for="(type, key) in type2Obj" :key="key">
          <div class="photo">
            <div class="show-img" :style="{'backgroundImage': `url('${type.value ? type.value.img : type.bgImage}')`}">
              <img src="@/assets/images/increaseLimit/choose.png" alt="" srcset="" @click="goToUpload(type.type)">
            </div>
            <div class="flag" :class="{'uploaded': type.value}" v-text="type.value ? 'Uploaded' : 'Not uploaded'"></div>
          </div>
          <div class="des" v-text="type.des"></div>
        </li>
      </ul>
      <div class="type">Type 2: Select at least one of the following to upload</div>
      <ul class="list">
        <li v-for="(type, key) in type1Obj" :key="key">
          <div class="photo">
            <div class="show-img" :style="{'backgroundImage': `url('${type.value ? type.value.img : type.bgImage}')`}">
              <img src="@/assets/images/increaseLimit/choose.png" alt="" srcset="" @click="goToUpload(type.type)">
            </div>
            <div class="flag" :class="{'uploaded': type.value}" v-text="type.value ? 'Uploaded' : 'Not uploaded'"></div>
          </div>
          <div class="des" v-text="type.des"></div>
        </li>
      </ul>
      <CButton @buttonClick="hidePopup('confirm')" :class="{'submit': canSubmit}" name="Confirm"></CButton>
    </div>
    <img class="close" src="@/assets/images/common/close06.png" @click="hidePopup('close')" alt="">
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CButton from '@/components/c-button.vue';
import bankAccountExampleThumbnail from '@/assets/images/increaseLimit/bank-account-example-thumbnail.png';
import photoOfWorkbadgeExampleThumbnail from '@/assets/images/increaseLimit/photo-of-workbadge-example-thumbnail.png';
import salaryPayslipExampleThumbnail from '@/assets/images/increaseLimit/salary-payslip-example-thumbnail.png';
export default {
   props: {
    value: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  computed: {
    ...mapState(['uploadDocuments']),
    type1Obj() {
      const uploadDocuments = this.uploadDocuments;
      return {
        salaryPayslip: {
          type: 'salaryPayslip',
          value: uploadDocuments.salaryPayslip[0],
          des: 'Salary Payslip',
          bgImage: salaryPayslipExampleThumbnail
        }, // 工资单
        salaryFlow: {
          type: 'salaryFlow',
          value: uploadDocuments.salaryFlow[0],
          des: 'Salary payment flow (last three months)',
          bgImage: ''
        }, // 近三个月银行账户工资发放流水
        photoOfWorkBadge: {
          type: 'photoOfWorkBadge',
          value: uploadDocuments.photoOfWorkBadge[0],
          des: 'photo of work badge',
          bgImage: photoOfWorkbadgeExampleThumbnail
        } // 工作证明
      }
    },
    type2Obj() {
      const uploadDocuments = this.uploadDocuments;
      return {
        bankAccountBalance: {
          type: 'bankAccountBalance',
          value: uploadDocuments.bankAccountBalance[0],
          des: 'Bank account balance',
          bgImage: bankAccountExampleThumbnail
        }, // 银行账户余额
      }
    },
    canSubmit() {
      let canSubmit = false;
      const type1Obj = this.type1Obj;
      // type1Obj至少选择其中之一进行上传, 且type2Obj必须上传。
      if ((type1Obj.salaryPayslip.value || type1Obj.salaryFlow.value || type1Obj.photoOfWorkBadge.value) && this.type2Obj.bankAccountBalance.value) {
        canSubmit = true;
      }
      return canSubmit;
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      if (type === 'close') {
        this.$emit('input', false);
      } else {
        if (this.canSubmit) {
          this.$emit('hidePopup', 'confirm');
        }
      }
    },
    goToUpload(type) {
      this.$router.push({
        path: '/uploadDocuments',
        query: {
          documentType: type
        }
      })
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .close {
    width: 16px;
    height: 16px;
    margin: 0 auto;
    margin-top: 20px;
  }
  .popup-content {
    width: 320px;
    border-radius: 24px;
    background: linear-gradient(180deg, $themeColor 0%,#FFF 53.5%);
    box-shadow: 0px 2px 20px 0px rgba(0, 54, 101, 0.06);
    box-sizing: border-box;
    padding: 13px 18px 20px 18px;
    .title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tips{
        color: #FFF;
        font-family: Avenir;
        font-size: 16px;
        font-style: normal;
        font-weight: 900;
        line-height: normal;
        width: 190px;
        text-align: left;
        span{
          color: #FFF701;
          font-size: 20px;
        }
      }
      img{
        width: 81px;
        height: 70px;
      }
    }
    .type{
      color: #000;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 800;
      line-height: normal;
      text-align: left;
      margin-top: 10px;
    }
    .list{
      margin-top: 10px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      li{
        margin-right: 7px;
        .photo{
          .show-img{
            width: 90px;
            height: 70px;
            background: rgba(0, 0, 0, 0.30);
            display: flex;
            justify-content: center;
            align-items: center;
            background-size: 100% 100%;
            img{
              width: 26px;
              height: 26px;
            }
          }
          .flag{
            width: 90px;
            height: 24px;
            color: #FFF;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-family: Avenir;
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            background: #7E7E7E;
            &.uploaded{
              background: $themeColor;
            }
          }
        }
        .des{
          width: 90px;
          color: #000;
          font-family: Avenir;
          font-size: 9px;
          font-style: normal;
          font-weight: 800;
          line-height: normal;
          text-align: left;
          margin-top: 6px;
        }
      }
    }
  }
  .c-button{
    width: 240px;
    height: 42px;
    border-radius: 8px;
    font-family: Avenir;
    font-size: 18px;
    font-weight: 800;
    line-height: 42px;
    letter-spacing: 0px;
    text-align: center;
    margin-top: 13px;
    color: #FFF;
    background: #C5C5C5;
    &.submit{
      background: $themeColor;
    }
  }

  .close-button {
    margin-top: 10px;
  }
}
</style>
