<template>
    <div class="c-money" :class="className">
        <span class="monetary-unit px-10">₦</span>
        <span class="currency-num" v-text="currencyNum"></span>
    </div>
</template>

<script>
export default {
    name: 'PalmcreditH5Money',
    props: {
        className: [String, Array],
        currencyNum: [String, Number]
    },
    data() {
        return {
            
        };
    },

    mounted() {
        
    },

    methods: {
        
    },
};
</script>

<style lang="scss" scoped>
    .c-money {
        text-align: left;
        .monetary-unit{
            font-family: Montserrat, Montserrat-Bold;
            font-weight: 700;
            text-align: left;
            color: #c4cad5;
            display: inline-block;
        }
        .currency-num{
            font-size: 18px;
            font-family: DIN Alternate, DIN Alternate-Bold;
            text-align: left;
            color: #c4cad5;
            margin-left: 5px;
        }
    }
</style>