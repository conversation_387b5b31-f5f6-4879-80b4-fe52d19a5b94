<template>
  <div v-if="show" class="popup">
    <div class="popup-content">
      <div class="title" v-text="title"></div>
      <div class="des" v-text="content"></div>
      <div class="star">
        <img :key="index" v-for="(star, index) in stars" @click="selecStar(index)" :src="star.check ? checkStar : unCheckStar" alt="">
      </div>
      <div class="tips">Tap the star to rate</div>
      <img class="bg-star" src="@/assets/images/wayacredit/bg-star.png" alt="">
    </div>
    <CButton @buttonClick="hidePopup('confirm')" name="Confirm"></CButton>
    <img class="close" src="@/assets/images/wayacredit/pop_close.png" @click="hidePopup('close')" alt="">
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
import checkStar from '@/assets/images/check-star.png';
import unCheckStar from '@/assets/images/uncheck-star.png';
export default {
  name: 'reviewPopupWayacredit',
   props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      checkStar,
      unCheckStar,
      stars: [
        {
          check: false
        },
        {
          check: false
        },
        {
          check: false
        },
        {
          check: false
        },
        {
          check: false
        }
        ]
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      let score = 0;
      this.stars.forEach((item, index) => {
        if (item.check) {
          score = index + 1;
        }
      });
      if (type === 'confirm' && score === 0) {
        this.$toast('Please tap the star to rate');
        return;
      }
      this.$emit('hidePopup', {
        type: type,
        score: score
      });
    },
    selecStar(index) {
      if (this.stars[index].check) {
        this.stars.forEach((item, itemIndex) => {
          if (itemIndex > index) {
            item.check = false;
          }
        })
      } else{
        this.stars.forEach((item, itemIndex) => {
          if (itemIndex <= index) {
            item.check = true;
          }
        })
      }
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 129px;
  .close {
    width: 23px;
    height: 23px;
    margin: 0 auto;
    margin-top: 20px;
    position: absolute;
    right: 20px;
    top: 20px;
  }
  .popup-content {
    border-radius: 21px;
    background: #26282D;
    margin-left: 30px;
    margin-right: 30px;
    padding: 0 18px;
    .title{
      color: #FFE81A;
      text-align: center;
      font-family: Noto Sans;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      padding-top: 32px;
    }
    .des{
      color: #FFC835;
      text-align: center;
      font-family: Noto Sans;
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      padding-top: 2px;
    }
    .star{
      margin-top: 11px;
      img{
        width: 39px;
        height: 36px;
        margin-right: 3px;
      }
    }
    .tips{
      color: #FFC835;
      text-align: center;
      font-family: Noto Sans;
      font-size: 10px;
      font-style: normal;
      font-weight: 700;
      transform: scale(.84);
    }
    .bg-star {
      width: 100%;
    }
  }
  .c-button{
    width: 240px;
    height: 44px;
    border-radius: 10px;
    background: linear-gradient(173deg, #FFF19C 5.37%, #B59111 94.63%);
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.25);
    color: #694C00;
    text-align: center;
    font-family: Noto Sans;
    font-size: 16px;
    font-style: normal;
    font-weight: 800;
    margin-top: 13px;
  }

  .close-button {
    margin-top: 10px;
  }
}
</style>
