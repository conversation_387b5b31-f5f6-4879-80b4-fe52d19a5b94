<template>
  <div v-if="show" class="popup">
    <div class="popup-content" :style="{ 'background-image': `url(${require(`@/assets/images/review-bg.png`)})`}">
      <div class="title" v-text="title"></div>
      <div class="des" v-text="content"></div>
      <div class="star">
        <img :key="index" v-for="(star, index) in stars" @click="selecStar(index)" :src="star.check ? checkStar : unCheckStar" alt="">
      </div>
      <div class="tips">Tap the star to rate</div>
    </div>
    <CButton @buttonClick="hidePopup('confirm')" name="Confirm"></CButton>
    <img class="close" src="@/assets/images/review-btn-popup-close.png" @click="hidePopup('close')" alt="">
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
import checkStar from '@/assets/images/check-star.png';
import unCheckStar from '@/assets/images/uncheck-star.png';
export default {
   props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      checkStar,
      unCheckStar,
      stars: [
        {
          check: false
        },
        {
          check: false
        },
        {
          check: false
        },
        {
          check: false
        },
        {
          check: false
        }
        ]
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      let score = 0;
      this.stars.forEach((item, index) => {
        if (item.check) {
          score = index + 1;
        }
      });
      if (type === 'confirm' && score === 0) {
        this.$toast('Please tap the star to rate');
        return;
      }
      this.$emit('hidePopup', {
        type: type,
        score: score
      });
    },
    selecStar(index) {
      if (this.stars[index].check) {
        this.stars.forEach((item, itemIndex) => {
          if (itemIndex > index) {
            item.check = false;
          }
        })
      } else{
        this.stars.forEach((item, itemIndex) => {
          if (itemIndex <= index) {
            item.check = true;
          }
        })
      }
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 129px;
  .close {
    width: 30px;
    height: 30px;
    margin: 0 auto;
    margin-top: 20px;
  }
  .popup-content {
    width: 360px;
    height: 279px;
    background-size: 100% 100%;
    margin: 0 auto;
    .title{
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: center;
      color: #6C4E00;
      width: 225px;
      margin: 0 auto;
      padding-top: 20px;
    }
    .des{
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: 400;
      line-height: 15px;
      letter-spacing: 0px;
      text-align: center;
      transform: scale(.84);
      color: #A17A13;
      width: 250px;
      margin: 0 auto;
      padding-top: 2px;
    }
    .star{
      margin-top: 3px;
      img{
        width: 39px;
        height: 36px;
        margin-right: 3px;
      }
    }
    .tips{
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: 700;
      line-height: 13px;
      letter-spacing: 0px;
      text-align: center;
      color: #A17A13;
      transform: scale(.84);
    }
  }
  .c-button{
    width: 240px;
    height: 44px;
    border-radius: 8px;
    background: linear-gradient(113.26deg, #FFEF94 -2.33%, #D8BA4D 92.78%);
    font-family: Noto Sans;
    font-size: 16px;
    font-weight: 800;
    line-height: 44px;
    letter-spacing: 0px;
    text-align: center;
    margin-top: 13px;
    color: #986D00;
  }

  .close-button {
    margin-top: 10px;
  }
}
</style>
