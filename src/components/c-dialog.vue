<template>
  <div class="dialog" :class="className">
    <van-dialog v-model="showDialog" className="dialog" :closeOnClickOverlay="false" :showConfirmButton="false"
      :showCancelButton="false">
      <div class="control">
          <img :src="tips" />
          <div class="wrap">
              <div class="title" v-text="title"></div>
              <slot name="right">
                <div class="content" v-text="content"></div>
              </slot>
              <div class="dia-button">
                <div v-if="mainButton" class="confirm" @click="mainAction()" v-text="mainButton"></div>
                <div v-if="sideButton" class="cancel" @click="sideAction()" v-text="sideButton"></div>
              </div>
          </div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import tips from '@/assets/images/tips.png';
export default {
  name: 'CDialog',
  props: {
    className: [String, Array],
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    mainButton: {
      type: String,
      default: ''
    },
    sideButton: {
      type: String,
      default: ''
    },
    show: Boolean
  },
  data() {
    return {
      tips,
      showDialog: false
    };
  },

  methods: {
    mainAction() {
      this.$emit('mainAction');
    },
    sideAction() {
      this.$emit('close', 'close');
    },
    close() {
      this.$emit('close');
    }
  },
  watch: {
    show: function(value) {
      this.showDialog = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog {
  background: none;
  .control{
    position: relative;
    height: auto;
    padding-top: 28px;

      img {
          width: 175px;
          height: 94px;
          position: absolute;
          top: 2px;
          right: 0;
      }
      .wrap{
          border-radius: 16px;
          background-color: #ffffff;
          height: 100%;
          padding-bottom: 20px;
          .title{
              font-size: 24px;
              font-family: Avenir, Avenir-Heavy;
              font-weight: 800;
              text-align: left;
              color: #1b3155;
              line-height: 33px;
              padding-top: 36px;
              padding-left: 24px;
          }
          .content{
              font-size: 16px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              text-align: left;
              color: #536887;
              line-height: 23px;
              padding-left: 24px;
              padding-right: 12px;
          }
          .confirm{
              width: 272px;
              height: 42px;
              background: $themeColor;
              border-radius: 8px;
              box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 18px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              text-align: center;
              color: #ffffff;
              line-height: 25px;
              margin: 0 auto;
              margin-top: 23px;
          }
          .dia-button {
            margin-top: 10px;

            .confirm {
              height: 42px;
              background: $themeColor;
              border-radius: 8px;
              line-height: 42px;
              color: #ffffff;
              margin-top: 25px;
              font-size: 18px;
              font-weight: 500;
              margin-bottom: 14px;
            }

            .cancel {
              height: 20px;
              line-height: 20px;
              background: #ffffff;
              color: $themeColor;
              border-radius: 5px;
              font-size: 14px;
            }
          }
      }
  }
}
</style>
