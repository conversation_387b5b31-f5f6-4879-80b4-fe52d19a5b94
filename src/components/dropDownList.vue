<template>
  <div class="select_box" :class="{'cancleTipClass': selectValue}">
    <div class="select_label" v-if="label" :class="[value ? 'hasValue' : '']">
      <span v-if="isRequired" class="label_required">*</span>{{label}}
    </div>
    <div @click="openSelect" class="select_inpt">
      <div class="input-wrapper">
        <input
          type="text"
          :class="label ? '' : 'center'"
          :placeholder="placeholder"
          disabled="disabled"
          :value="value"
        />
        <div class="input-overlay" id="overlay"></div>
      </div>
      
      <!--下拉箭头-->
      <img src="@/assets/images/common/ic_up.png" v-if="arrowStatus" class="arrowImg" />
      <img src="@/assets/images/common/ic_up.png" v-else class="arrowImg" />
    </div>
    <!--下拉弹出框  start-->
    <van-popup v-model="poupShow" position="bottom" :style="{ height: '500px' }" @close="closeMask">
      <!-- <i class="popup_cancel" @click="closePopup"></i> -->
      <div class="poup_wrap">
        <ul class="popup_ul">
          <li
            v-for="(item, index) in dataList"
            class="optionalClass"
            :key="index"
            :data-index="index"
            @click="selectItem(item,index)"
          >
            <!--选项文字-->
            <div class="option_word clearfix" :class="{'selected':activeFlag===index}">
              <span class="option_word_day optionSingle">{{item.value}}</span>
            </div>
            <!--选中打勾图标-->
            <div class="popup_ul_select_img" v-show="activeFlag===index">
              <img :src="icolSel" />
            </div>
          </li>
        </ul>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: "DropDownList",
  data() {
    return {
      //选中项的下标
      activeIndex: 0,
      //控制弹框的显示
      poupShow: false,
      //控制箭头的替换
      arrowStatus: false,
      //选中时显示“打勾”
      activeFlag: -1,
      //slect选中的值
      selectValue: "",
      //防止重复点击 (默认开始是可以点击的)
      clickStatus: true,
      // 勾选图标
      icolSel: require(`@/assets/images/common/select.png`)
    };
  },
  props: {
    value: {
      type: String,
      default: ""
    },
    dataList: {
      type: Array,
      default() {
        return [{ name: "选项一" }, { name: "选项二" }];
      }
    },
    labelProperty: {
      type: String,
      default() {
        return "name";
      }
    },
    label: String,
    placeholder: String,
    isRequired: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 滚动到可视区域内
    scrollIntoView() {
      if (this.$attrs.id) {
        const $dom = document.getElementById(this.$attrs.id);
        $dom.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    },
    //打开下拉弹出框
    openSelect() {
      this.clickStatus = true; //打开弹出框时，恢复选项的可点击状态
      //点击打开下拉菜单时，需要先循环数组，与后台返回的字符串对比，判断出index，显示当前选中高亮状态
       //根据返回的数据，判断对应在数组中的下标
      this.activeFlag = this.dataList.findIndex(
        item => item.name === this.value
      ); //控制选中项，打勾箭头的显示
      this.poupShow = true;
      this.arrowStatus = true;
      this.$emit("click", this.selectValue)
      this.$nextTick(() => {
        this.scrollIntoView()
      })
    },
    //点击遮罩层时，替换下拉箭头
    closeMask() {
      this.arrowStatus = false;
      this.poupShow = false;
    },
    //点击取消，关闭
    closePopup() {
      this.arrowStatus = false;
      this.poupShow = false;
    },
    selectItem(item, index) {
      if (this.clickStatus) {
        this.clickStatus = false; //点击后，使用布尔值控制不可重复点击
        this.selectValue = item.name; //选定赋值
        this.activeFlag = index; //控制选中项，打勾箭头的显示
        this.arrowStatus = false; //点击，改变状态值
        this.poupShow = false; //收起菜单
        this.activeIndex = index;
        this.$emit("change", {
          index: index,
          value: this.dataList[index]
        });
      }
    }
  },
  created() {
    this.productSource = localStorage.getItem('productSource')
  },
  mounted() {
    this.selectValue = this.value
  }
};
</script>

<style scoped lang="scss">
.select_box {
  display: inline-block;
  width: 100%;
  position: relative;
  .van-overlay {
    opacity: 0.5;
    background: #000;
  }
  .van-popup--bottom {
    overflow-y: visible;
    background-color: #fff;
  }
  .select_label {
    position: absolute;
    top: calc(50% - 12px);
    left: 10px;
    color: #919DB3;
    z-index: 10;
    line-height: 1.5;
    transition: all .15s ease-in-out;
    pointer-events: none;
    font-size: 16px;
    &.hasValue {
      transform: scale(0.99);
      transform-origin: left;
      left: 10px;
      top: 0px;
      color: #536887;
      font-family: Avenir;
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
    }
    .label_required {
      color: #FF454F;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      margin-right: 3px;
    }
  }
  .select_inpt {
    display: block;
    position: relative;
    color: #1B3155;
    font-family: Avenir;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    text-align: left;
    z-index: 1;
    .input-wrapper {
      position: relative;
    }
    .input-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1; /* 放在禁用输入元素之上 */
      background: transparent; /* 透明背景 */
    }
    input {
      width: 100%;
      height: 50px;
      border: none;
      box-sizing: border-box;
      line-height: 10px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #C4CAD5;
      padding-left: 12px;
      padding-top: 10px;
      color: #1b3155;
      font-size: 16px;
      font-weight: 500;
      &.center {
        padding-top: 0;
        line-height: 22px;
        font-size:16px;
      }
    }

    input:disabled {
      background:#fff;
    }
    input::placeholder {
      color: #919DB3 !important;
      font-family: Avenir;
      font-size: 16px !important;
      font-style: normal;
      font-weight: 500;
    }
    .arrowImg {
      width: 20px;
      height: 20px;
      position: absolute;
      right: 12px;
      top: 15px;
    }
    .select_error_tip {
      width: 7px;
      height: 7px;
      position: absolute;
      right: 20px;
      top: 5px;
      display: none;
    }
  }
  .popup_cancel {
    display: block;
    position: absolute;
    width: 40px;
    height: 40px;
    left: 18px;
    top: -40px;
    background: url('../assets/images/common/cancel.png') no-repeat;
    background-size: contain;
  }
  .poup_wrap {
    width: 100%;
    position: relative;
    height: 100%;
    overflow-y: scroll;
    .popup_ul {
      width: 100%;
      box-sizing: border-box;
      padding-left: 18px;
      background: #ffffff;
      li {
        width: 100%;
        height: 50px;
        border-bottom: 1px solid #e3e5e9;
        line-height: 50px;
        display: block;
        .option_word {
          float: left;
          .option_word_day {
            display: inline-block;
            text-align: left;
          }
          .optionSingle {
            width: auto;
          }
          .option_word_interest {
            display: inline-block;
          }
          span {
            float: left;
          }
        }
        .selected {
          font-size: 14px;
          color: #099bfa;
        }
        .popup_ul_select_img {
          width: 18px;
          height: 18px;
          float: right;
          margin-top: 18px;
          margin-right: 18px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            vertical-align: top;
          }
        }
      }
      .optionalClass {
        font-size: 14px;
        color: #1b3155;
      }
      .notOptionalClass {
        font-size: 14px;
        color: #c4cad5;
      }
      li:last-child {
        border-bottom: 0;
      }
    }
  }
}
</style>
