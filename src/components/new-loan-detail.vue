<template>
  <div class="new-loan-detail">
    <ul class="loan-detail" v-if="showDetail">
      <li class="li" v-if="loanDetail.pltfFeeAmt">
        <div class="item">Plaftform Fee</div>
        <div v-if="loanDetail.caculateStatus" class="content">
          <CMoney :currencyNum="loanDetail.pltfFeeAmt"></CMoney>
        </div>
        <div v-else class="cal">Calculating</div>
      </li>
      <li class="li" v-if="loanDetail.showInsurance">
        <div class="acc-pro-ser">
          <img class="icon" src="@/assets/images/protection-service.png" alt="">
          <div class="item">Credit Protection Service</div>
          <CPopover :tipContent="'In the event of death or disability, we will waive your loan balance'">
            <template v-slot:reference>
              <img class="question" :src="require(`@/assets/images/${productSource}/question.png`)" alt="">
            </template>
          </CPopover>
        </div>
        <div class="right">
          <div class="after check-acc" v-if="loanDetail.caculateStatus">
            <CMoney :currencyNum="loanDetail.insuranceFee"></CMoney>
            <img class="check" :src="loanDetail.checkShow ? require(`@/assets/images/${productSource}/index-paid.png`) : require('@/assets/images/un-paid.png')" 
            alt="" @click="checkInsurance()">
          </div>
          <div class="after" v-else>
            <span class="cal">Calculating</span>
          </div>
        </div>
      </li>
      <li class="li">
        <div class="item">Received</div>
        <div v-if="loanDetail.caculateStatus" class="content">
          <CMoney :currencyNum="loanDetail.received"></CMoney>
        </div>
        <div v-else class="cal">Calculating</div>
      </li>
      <li class="li">
        <div class="plan">Repayment Plan</div>
      </li>
      <template v-for="plan in loanDetail.plans">
        <li class="li" :key="plan.dueDate">
          <div class="item">
            <span>Due Date</span>
            <span class="date" v-text="plan.dueDate"></span>
          </div>
          <div v-if="loanDetail.caculateStatus" class="content">
            <CMoney :currencyNum="plan.termAmount"></CMoney>
          </div>
          <div v-else class="cal">Calculating</div>
        </li>
      </template>
    </ul>
    <slot name="newItem"></slot>
    <div class="more-details" @click="changeStatus">
      View more details
      <img src="@/assets/images/details-arrow.png" :class="{'up': showDetail}" alt="">
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CMoney from '@/components/c-money.vue';
import CPopover from '@/components/c-popover.vue';

export default {
  name: 'loanDetail',
  components: {
    CMoney,
    CPopover
  },
  computed: {
    ...mapState(['productSource']),
  },
  props: {
    loanDetail: {
      type: Object,
      default: function() {
        return {
          type: '', // 产品类型
          pltfFeeAmt: '',
          insuranceFee: '',
          caculateStatus: '',
          checkShow: '',
          received: '',
          showInsurance: '',
          showDetail: '',
          plans: []
        }
      }
    },
    initShowDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDetail: false
    };
  },

  mounted() {
    this.showDetail = this.initShowDetail;
  },

  methods: {
    changeStatus() {
      this.showDetail = !this.showDetail;
    },
    checkInsurance() {
      const vm = this;
      vm.$emit('checkInsurance', {
        type: vm.loanDetail.type
      })
    }
  },
  watch: {
    'loanDetail': {
      deep: true,
      handler: function(value) {
        console.log('value.showDetail', value.showDetail);
        if (value.showDetail) {
          this.showDetail = value.showDetail;
        }
        console.log('loanDetail', value, this.showDetail);
    }
    }
  }
};
</script>

<style lang="scss" scoped>
.new-loan-detail{
  position: relative;
  font-family: Avenir-Medium;
  font-size: 14px;
  color: #536887;
  .loan-detail{
    margin-top: 9px;

    .li{
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      position: relative;
      .item{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #536887;
        .date{
          font-family: DINAlternate-Bold;
          font-size: 12px;
          color: #97A2B7;
          font-weight: 700;
          transform: scale(.84);
          display: inline-block;
        }
      }
      .plan{
        width: 125px;
        height: 24px;
        background-image: linear-gradient(270deg, #D9D9D9 0%, #B0B2B7 100%);
        font-size: 12px;
        color: #FFFFFF;
        text-align: center;
        font-weight: 500;
        line-height: 24px;
        position: absolute;
        left: -18px;
      }
      .acc-pro-ser{
        display: flex;
        align-items: center;
        .icon{
          width: 14px;
          height: 14px;
          margin-top: -2px;
          margin-right: 2px;
        }
        .question{
          width: 12px;
          height: 12px;
        }
        ::v-deep .c-popover{
          margin-left: 2px;
          .wrap{
            width: 14px;
            height: 14px;
            display: flex;
            align-items: center;
          }
        }
      }
      .check-acc{
          display: flex;
          align-items: center;
          img{
            width: 18px;
            height: 18px;
            margin-left: 5px;
          }
        }
      .content{
        font-family: DINAlternate-Bold;
        font-size: 14px;
        color: #1B3155;
        text-align: right;
      }
      .cal{
        font-size: 14px;
        color: #c4cad5;
        font-weight: 500;
      }
      ::v-deep .c-money{
        display: flex;
        align-items: flex-end;
        .monetary-unit{
          font-family: DINAlternate-Bold;
          font-size: 12px;
          color: #1B3155;
          font-weight: 600;
        }
        .currency-num{
          font-family: DINAlternate-Bold;
          font-size: 14px;
          color: #1B3155;
          font-weight: 600;
          margin-left: 1px;
        }
      }
    }
  }
  .more-details{
    height: 40px;
    font-family: Avenir-Medium;
    font-size: 12px;
    color: #1B3155;
    text-align: center;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 14px;
      margin-left: 4px;
      &.up{
        transform: rotate(180deg);
      }
    }
  }
}
</style>