<template>
  <div class="add-account" @click="routeRedirect">
    <img :src="cardType === 'account' ? require(`@/assets/images/common/add-card-banner.png`) : require(`@/assets/images/common/add-account-banner.png`)" alt="">
  </div>
</template>

<script>
import {mapState} from "vuex";
import api from "@/api/interface";
export default {
  name: 'addAccount',
  computed: {
    ...mapState([ 'productSource']),
  },
  props: {
    cardType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      
    };
  },

  mounted() {
    
  },

  methods: {
    routeRedirect () {
      let obj = {};
      // banner跳转的绑定cardType为当前状态的类型相反。
      const cardType = this.cardType === 'account' ? '' : 'account';
      this.$emit('bindCard', cardType)
      if (this.productSource === 'palmcredit') {
        obj.businessChannel = 'palmcreditnew';
      }
      return  new Promise((resolve, reject) => {
        this.$http(api.commonBlc.routeRedirect, {
          data:{
            scene: '12',
            cardType: cardType,
            ...obj
          } // 目前是写死的
        }).then(res => {
          // 跳转绑卡中台的地址, 跳转到绑账户页面
          location.href = res.redirectUrl;
          resolve();
        }).catch(e => {
          reject(e);
        })
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.add-account{
  width: 313px;
  height: 86px;
  margin-left: 25px;
  margin-right: 25px;
  img{
    width: 313px;
    height: 86px;
  }
}
</style>