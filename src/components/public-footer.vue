<template>
  <footer class="footer">
    <div class="bottom_security">
      <svg xmlns="http://www.w3.org/2000/svg" width="30" height="34" viewBox="0 0 30 34" fill="none">
        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="34" viewBox="0 0 30 34" fill="none">
          <path class="stroke" d="M14.7473 2.04498L2.3949 6.73822C2.15796 6.82819 2 7.05311 2 7.30801V16.7695C2 23.5319 6.64399 29.0948 13.4362 31.5839C14.9842 32.1387 15.0158 32.1387 16.5638 31.5839C23.3718 29.1098 28 23.5319 28 16.7695V7.29302C28 7.03811 27.842 6.82819 27.6051 6.72323L15.2211 2.04498C15.0632 1.98501 14.9052 1.98501 14.7473 2.04498Z" stroke="#CD0050" stroke-width="3"/>
          <path class="fill" fill-rule="evenodd" clip-rule="evenodd" d="M8 16.2821L9.42105 14.7041L13.1931 17.4548L21.2064 11L22.9938 12.6277L13.1931 22.4355L8 16.2821Z" fill="#CD0050"/>
        </svg>
      </svg>
      <p v-text="`${channel} security guarantee`"></p>
    </div>
  </footer>
</template>
<script>
import { mapState } from 'vuex';
export default {
  components: {

  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(['channel', 'productSource'])
  },
  data() {
    return {
    };
  },

  created() {

  }
};
</script>
<style lang="scss" scoped>
.footer {
  margin-top: 12px;
  .bottom {
    &_security {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      line-height: 8px;
      color: #919db3;
      svg {
        width: 13px;
        height: 15px;
        margin-right: 5px;
        .fill{
          fill: $themeColor;
        }
        .stroke{
          stroke: $themeColor;
        }
      }
      p {
        color: #919db3;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}
</style>
