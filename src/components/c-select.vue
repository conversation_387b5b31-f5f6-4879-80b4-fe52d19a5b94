<template>
  <div class="select" v-if="showSelect">
    <van-popup v-model="showSelect" position="bottom" :close-on-click-overlay="false">
      <ul class="list">
        <li class="cancel" v-text="title" @click="close()"></li>
        <li class="van-hairline--bottom" v-for="(item, index) in list" :key="item.name" @click="select(index)">
          <span :class="{'choosed': index === active}" v-text="item.value"></span>
          <img v-if="index === active" src="@/assets/images/pick.png" alt="">
        </li>
      </ul>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'CSelect',
  props: {
    title: String,
    list: Array,
    value: String,
    show: Boolean
  },
  data() {
    return {
      showSelect: false,
      active: -1
    };
  },

  watch: {
    'show': function(v) {
      this.showSelect = v;
      if (this.list.length > 0) {
        let active = -1;
        this.list.forEach((item, index) => {
          if (item.value === this.value) {
            active = index;
          }
        });
        this.active = active;
      }
    }
  },

  mounted() {
    
  },

  methods: {
    select(index) {
      this.active = index;
      this.$emit('close', {
        ...this.list[index]
      });
    },
    close() {
      this.$emit('close');
    }
  },
};
</script>

<style lang="scss" scoped>
.van-popup{
  background: rgba(120,120,120,0);
  .list{
    margin: 0;
    padding: 0;
    list-style: none;
    height: 428px;
    background: #ffffff;
    overflow-y: scroll;
    overflow-x: hidden;
    li {
        height: 50px;
        text-align: left;
        line-height: 50px;
        padding: 0 15px;
        font-size: 14px;
        color: #000000;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.cancel{
          background: #ebebeb;
        }
        &.van-hairline--bottom::after{
          left: -44%;
        }
        img{
          width: 20px;
          height : 20px;
        }
        .choosed{
          color: #129ffa;
        }
    }
  }
  .close{
    text-align: left;
    img{
      width: 38px;
      height: 38px;
      margin-left: 16px;
      margin-bottom: 3px;
    }
  }
}
</style>