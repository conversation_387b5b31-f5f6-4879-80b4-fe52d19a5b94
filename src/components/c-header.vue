<template>
  <div class="c-header" :class="{'waya-back': styType === 'b', 'common1': styType === 'c'}">
    <div class="left" @click="backFun()">
      <template v-if="styType === 'b' || styType === 'c'">
         <template v-if="styType === 'b'">
          <img class="back-img" src="@/assets/images/wayacredit/back.png" alt=""  />
          <div class="waya-title" v-text="titleName"></div>
        </template>
        <template v-if="styType === 'c'">
          <img class="back-img" src="@/assets/images/palmcreditpro/back.png" alt=""  />
          <div class="title-c" v-text="titleName"></div>
        </template>
      </template>
      <template v-else>
        <img class="back-img" :class="{'ac-back': deviceType === 'AC'}" :src="deviceType !== 'AC' ? back : acBack" alt=""  />
        <div class="title" v-text="titleName"></div>
      </template>
    </div>
    <div class="right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
import back from '@/assets/images/back.png';
import acBack from '@/assets/images/ac-back.png';
import { mapState } from 'vuex';
import { nativeMethods } from "@/assets/js/native";


export default {
  name: 'CHeader',
  props: {
    titleName: {
      type: String,
      default: ''
    },
    backFun: {
      type: Function,
      default: function() {
        this.$router.back(-1);
      }
    },
    /**
     * 设置样式类型
     * a：默认样式
     * b: wayacredit 样式
     * c: common1 样式
     */
    styType: {
      type: String,
      default: 'a'
    },
  },
  computed: {
    ...mapState(['deviceType'])
  },
  data() {
    return {
      back,
      acBack
    };
  },

  methods: {

  },

  created() {
    const vm = this;
    console.log('header--------nativeMethods', nativeMethods)
    console.log('header--------nativeMethods.nativeBackCallback', nativeMethods.nativeBackCallback)
    window[nativeMethods.nativeBackCallback] = function() {
      console.log('nativeBackCallback');
      vm.backFun();
      // 1 原生不处理返回键  0 原生处理返回键
      return 1
    }
  },

  mounted() {

  }
};
</script>

<style lang="scss" scoped>
.c-header{
  display: flex;
  justify-content: space-between;
  height: 56px;
  align-items: center;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
  background: #ffffff;
  .left{
    display: flex;
    padding-left: 10px;
    align-items: center;
    .back-img{
      width: 24px;
      height: 24px;
      &.ac-back{
        width: 12px;
        height: 12px;
        margin-left: 7px;
        margin-right: 40px;
      }
    }
    .title{
      font-size: 18px;
      font-family: Avenir, Avenir-Heavy;
      font-weight: 800;
      text-align: left;
      color: #1b3155;
      line-height: 25px;
    }
  }
}
.waya-back {
  background: #1E1E20;
  .waya-title {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: HarmonyOS Sans SC;
    font-size: 18px;
  }
  .back-img {
    width: 35px;
    height: 35px;
    margin-right: 12px;
  }
}
.common1 {
  background: $color;
  .title-c {
    color: #FFF;
    font-family: HarmonyOS Sans SC;
    font-size: 19px;
    font-style: normal;
    font-weight: 700;
  }
}
</style>