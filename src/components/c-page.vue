<!-- 
    作者：yangjf
    时间：2022/6/24
    描述：页面公共模板
 -->
<template>
    <div class="c-page" :class="className">
        <slot></slot>
        <!-- 嵌入的页面 -->
    </div>
</template>

<script>
export default {
    name: 'CPage',
    props: {
        className: [String, Array]
    },
    data() {
        return {
            
        };
    },

    mounted() {
        
    },

    methods: {
        
    },
};
</script>

<style lang="scss" scoped>
    .c-page{
        background: #fcfcfd;
        background: linear-gradient(#fcfcfd, #e3e5ef);
        padding-left: 12px;
        padding-right: 12px;
        margin-top: 56px;
        min-height: 100%;
    }
</style>