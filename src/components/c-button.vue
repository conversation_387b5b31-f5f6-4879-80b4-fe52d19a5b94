<template>
    <button v-preventReClick="preventReClickTime" class="c-button" @click="action" :class="className" v-text="name"></button>
</template>

<script>
export default {
    name: 'c-button',
    props: {
        name: String,
        className: [String, Array],
        preventReClickTime: {
            type: Number,
            default: 3000
        }
    },
    data() {
        return {

        };
    },

    mounted() {

    },

    methods: {
        action() {
            this.$emit('buttonClick');
        }
    },
};
</script>

<style lang="scss" scoped>
    .c-button{
        width: 328px;
        height: 42px;
        background: $themeColor;
        border-radius: 9px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 18px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        left: calc((100% - 328px)/2);
        margin: 0 auto;
        border: none;
    }
</style>
