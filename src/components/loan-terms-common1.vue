<template>
  <div class="loan-terms">
    <div class="select-loan-terms" @click="selectTerm()">
      <div class="left">
        <div class="title">Select Loan Terms</div>
        <div class="days" v-text="indexProduct.type"></div>
        <div class="detail" v-if="indexProduct.tips" v-text="indexProduct.tips"></div>
      </div>
      <div class="right">
        <img :src="require(`@/assets/images/${productSource}/term-right.png`)" alt="">
      </div>
      <div v-if="tag14and2User" class="click-tips" :style="{ 'background-image': `url(${require(`@/assets/images/click-more.svg`)})`}">
        <div>Click for More Offers</div>
      </div>
    </div>
    <!-- 产品列表 -->
    <van-popup v-model="showProductList" class="product-list-pop" :close-on-click-overlay="false" position="bottom" :style="{'height': '80%'}">
      <div class="product-list">
        <div class="title">
          <img :src="require(`@/assets/images/${productSource}/back1.png`)" alt="" @click="closeProduct()" />
          <div class="text">Select Loan Terms</div>
        </div>
        <ul class="list">
          <li class="product" v-for="(item, index) in productList" :key="item.productId"  @click="chooseType(index, item.type)" :class="{'last-mul': caluLastMul(index)}">
            <div class="term-title" v-if="showTitle(index)">
              <div class="sub-title" v-text="item.tips ? 'Multiple Terms' : 'One Term'"></div>
              <div class="sub-tips" v-text="item.tips ? 'Higher credit limit, lower daily interest rate' : 'One-time payment of principal and interest'"></div>
              <div class="amount-add" v-if="index === 0 && item.tips && !(productList[productList.length - 1] && productList[productList.length - 1].tips)">
                <div class="wrap">
                  <div class="num">Recommend</div>
                  <div class="arrow"></div>
                </div>
              </div>
            </div>
            <div class="product-detail" :class="{mul: item.tips, single: !item.tips}">
              <div class="left">
                <div class="item">
                  <div class="day" :class="{'lock': item.islock}" v-text="item.type"></div>
                  <img v-if="index === 0" src="@/assets/images/recommend.png" alt="" />
                </div>
                <div class="detail" v-if="item.tips" v-text="item.tips"></div>
                <div class="recommend" v-if="index === 0">
                  <div>Limited 3 days interest rate cut!</div>
                </div>
              </div>
              <div class="right">
                <div>
                  <div v-if="!item.islock" class="rate" :class="{active: index == active}" v-text="`${Number(item.rate).toFixed(2)}%/day`"></div>
                  <div v-if="!item.islock && index === 0" class="rate before" :class="{active: index == active}" v-text="`${Number(item.rate + 2).toFixed(2)}%/day`"></div>
                </div>
                <img v-if="!item.islock" class="choose" :src="index === active ? require(`@/assets/images/${productSource}/ic_circle_active2.png`) : require(`@/assets/images/${productSource}/ic_circle_inactive2.png`)" alt="">
                <img v-else class="lock" src="@/assets/images/locky-ic.png" alt="">
              </div>
            </div>
          </li>
        </ul>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'loanTermsCommon1',
  model: {
    prop: 'active',
  },
  props: {
    active: {
      type: Number,
      default: -1
    },
    proIndex: {
      type: Number,
      default: 0
    },
    productList: {
      type: Array,
      default: function() {
        return []
      }
    },
    productSource: {
      type: String,
      default: ''
    },
    customType: { // 自定义类型
      type: String,
      default: ''
    },
    tag14and2User: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    indexProduct() {
      let vm = this;
      let product = {};
      if (vm.active > -1) {
        product = vm.productList[vm.active];
      }
      console.log('product', product, vm.productList, vm.active);
      return product;
    }
  },
  data() {
    return {
      showProductList: false
    };
  },

  mounted() {

  },

  methods: {
    // 显示产品选择
    selectTerm() {
      this.showProductList = true;
    },
    // 关闭产品选择
    closeProduct() {
      this.showProductList = false;
    },
    // 选择产品
    chooseType(index, type) {
      const vm = this;
      if (vm.productList[index].islock) {
        vm.$toast('Withdraw now and this loan term can be unlocked next time.');
        return;
      }
      vm.closeProduct();
      vm.$emit('chooseType', {
        index: index,
        type: type,
        customType: vm.customType
      });
    },
    // 控制标题显示
    showTitle(index) {
      let vm = this;
      let productList = vm.productList;
      console.log('productList', productList);
      // 开始标题计算
      if (index === 0) {
        return true;
      } else if (index > 0) {
        if (productList[index - 1] && productList[index - 1].tips && !productList[index].tips) {
          // tips为多期标志, 这里为真实用户的多期数据
          return true;
        } else if (productList[index - 1] && !productList[index - 1].tips && productList[index].tips) {
          // tips为多期标志, 这里为单期时，前端构造的用户的多期数据
          return true;
        } else {
          return false;
        }
      }
    },
    // 间隔控制
    caluLastMul(index) {
      let productList = this.productList;
      if (index > 0) {
        // tips为多期标志
        if (productList[index].tips && productList[index + 1] && !productList[index + 1].tips) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    }
  },
};
</script>

<style lang="scss" scoped>

.loan-terms{

  .select-loan-terms{
    border-radius: 16px;
    background: #F3F5FF;
    display: flex;
    position: relative;
    padding-top: 12px;
    padding-bottom: 12px;
    .left {
      width: calc(100% - 50px);
      text-align: left;
      padding-left: 20px;
      .title{
        color: #231815;
        font-family: HarmonyOS Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
      }
      .days{
        color: $color;
        font-family: DIN;
        font-size: 26px;
        font-style: normal;
        font-weight: 700;
        margin-bottom: 8px;
      }
      .detail{
        color: #8E8E8E;
        font-family: HarmonyOS Sans SC;
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
      }
    }
    .right{
      display: flex;
      align-items: center;
      justify-content: center;
      img{
        width: 16px;
        height: 16px;
      }
    }
    .click-tips{
      position: absolute;
      left: 120px;
      top: 11px;
      width: 110px;
      height: 15px;
      font-family: Avenir;
      font-size: 12px;
      font-weight: 500;
      line-height: 15px;
      text-align: left;
      color: #999;
      background-size: 100%;
      div{
        transform: scale(.84);
        height: 15px;
        width: 110px;
        width: 128px;
        margin-left: -5px;
      }
    }
  }

  .product-list-pop{
    border-radius: 23px 23px 0px 0px;
    background: #fff;
    overflow: hidden;
  }
  .product-list{
    height: 100%;
    .title{
      display: flex;
      align-items: center;
      padding: 16px 24px;
      img{
        width: 35px;
        height: 35px;
        margin-right: 12px;
      }
      .text {
        color: #231815;
        font-family: HarmonyOS Sans SC;
        font-size: 19px;
        font-style: normal;
        font-weight: 700;
      }
    }
    .list{
      overflow-y: scroll;
      height: calc(100% - 67px);
      .product{
        position: relative;
        padding-left: 24px;
        padding-right: 24px;
        &::after{
          position: absolute;
          box-sizing: border-box;
          content: " ";
          pointer-events: none;
          right: 15px;
          bottom: -1px;
          left: 24px;
          border-bottom: 1px solid #dddddd;
          transform: scaleY(0.5);
          width: calc(100% - 48px);
        }

        &.last-mul::after{
          left: -31px;
          border-bottom: 1px solid #F5F5F5;
          transform: scaleY(5);
          width: 109%;
        }

        .term-title{
          position: relative;
          margin-top: 15px;
          padding-bottom: 7px;
          border-bottom: 1px solid #dddddd;
          .sub-title{
            color: #231815;
            font-family: HarmonyOS Sans SC;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            font-weight: 500;
            text-align: left;
          }
          .sub-tips{
            color: #231815;
            font-family: HarmonyOS Sans SC;
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            text-align: left;
            transform: scale(0.9);
            margin-left: -16px;
          }
          .amount-add{
            position: absolute;
            left: 146px;
            top: -5px;
            transform: scale(.85);
            .wrap{
              position: relative;
              .num{
                background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
                color: #ffffff;
                width: auto;
                white-space: nowrap;
                padding: 2px 4px;
                font-size: 12px;
              }
              .arrow{
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 5px 0 5px 7px;
                border-color: transparent transparent transparent #C78900;
                position: absolute;
                left: 0px;
                top: 12px;
              }
            }
          }
        }

        .product-detail{

          display: flex;
          justify-content: space-between;
          align-items: center;

          &.mul{
            height: 90px;
            .day{
              font-size: 21px;
            }
          }

          &.single{
            height: 60px;
            .day{
              font-size: 18px;
            }
          }
          .left{
            text-align: left;
            .item {
              margin-bottom: 3px;
              display: flex;
              align-items: flex-end;
              .day{
                color: #231815;
                font-family: HarmonyOS Sans SC;
                font-size: 20px;
                font-style: normal;
                font-weight: 700;
                &.lock{
                  color: #999;
                }
              }
              img{
                width: 16px;
                height: 15px;
                margin-bottom: 6px;
                margin-left: 5px;
              }
            }
            .detail{
              color: $background;
              font-family: HarmonyOS Sans SC;
              font-size: 10px;
              font-style: normal;
              font-weight: 400;
              margin-bottom: 2px;
              width: 174px;
              transform: scale(0.84);
              margin-left: -14px;
            }
          }
          .right {
            display: flex;
            justify-content: space-between;
            .rate{
              color: $background;
              font-family: DIN;
              font-size: 16px;
              font-style: normal;
              font-weight: 700;
              transform: scale(0.9);
              margin-right: 7px;
              &.active{
                color: $background;
              }
              &.before{
                color: #231815;
                text-decoration:line-through;
              }
            }
          }
        }
        .recommend{
          width: 205px;
          height: 25px;
          background: #FDEFDA;
          border-radius: 3px;
          color: #FF8D00;
          display: flex;
          align-items: center;
          transform: scale(0.84);
          margin-left: -16px;
          font-size: 12px;
          div{
            margin-left: 5px;
          }
          img{
            width: 10px;
            height: 10px;
            margin-left: 5px;
            margin-right: 5px;
          }
        }
        img{
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>