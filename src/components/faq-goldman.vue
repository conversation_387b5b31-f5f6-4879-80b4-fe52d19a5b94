<template>
  <Popup v-model="showFaq" position="bottom">
    <div class="top">
      <img class="close" src="@/assets/images/common/close.png" @click="closeFaq('close')" alt="png" />
    </div>
    <div class="faq">
      <div class="title" v-text="setting.title"></div>
      <div class="des">
        Your loan will be disbursed to your <span class="em">Goldman Bank
        Account<span v-text="`${setting.account}`" v-if="setting.account"></span></span> in <span class="em">one or more installments</span> after approved
      </div>
      <ul class="step">
        <li>
          <div class="left">
            <div class="circle"></div>
            <div class="item">Step 1</div>
          </div>
          <div class="right">
            <div class="content">After the loan disbursed, you can go to the added <span  class="em">Balance page</span> on <span v-text="channel"></span> and check the amount you've received. (The Balance page is only visible after successful disbursement)</div>
            <div class="show-img">
              <img class="one" src="@/assets/images/faqGoldman/step1.png" alt="">
            </div>
          </div>
        </li>
        <li>
          <div class="left">
            <div class="circle"></div>
            <div class="item">Step 2</div>
          </div>
          <div class="right">
            <div class="content">Click "Transfer" on the Balance page to withdraw</div>
            <div class="show-img">
              <img class="two" src="@/assets/images/faqGoldman/step2.png" alt="">
            </div>
          </div>
        </li>
        <li>
          <div class="left">
            <div class="circle"></div>
            <div class="item">Step 3</div>
          </div>
          <div class="right">
            <div class="content">Fill in the information on the Transfer page and verify the OTP, and then you can withdraw successfully.</div>
            <div class="show-img">
              <img class="three" src="@/assets/images/faqGoldman/step3.png" alt="">
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="ok">
      <c-button :name="setting.button" @buttonClick="closeFaq('confirm')"></c-button>
    </div>
  </Popup>
</template>

<script>
import { Popup } from 'vant';
import { mapState } from 'vuex';
export default {
  name: 'faqPopup',
  computed: {
    ...mapState(['productSource', 'channel']),
  },
  props: {
    setting: {
      type: Object,
      default: function () {
        return {
          title: '',
          account: '',
          button: ''
        }
      }
    }
  },
  data() {
    return {
      showFaq: false,
    }
  },
  components: {
    Popup
  },
  mounted() {
  },
  methods: {
    openFaq() {
      this.showFaq = true;
    },
    closeFaq(type) {
      this.showFaq = false;
      this.$emit('close', type);
    },
  }
};
</script>

<style lang="scss" scoped>
.van-popup{
  background-color: transparent;
  .em{
    color: #314CFE;
  }
  .top{
    text-align: right;
    .close{
      height: 30px;
      width: 30px;
    }
  }
  .faq {
    position: relative;
    text-align: left;
    height: 440px;
    overflow: scroll;
    padding: 0 20px;
    background: #ffffff;
    .title{
      color: #000;
      text-align: center;
      font-family: Roboto;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      margin-top: 25px;
    }
    .des{
      color: #1B3155;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin-top: 12px;
    }
    .step{
      margin-top: 18px;
      li{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 14px;
        .left{
          color: #1B3155;
          font-family: Roboto;
          font-size: 14px;
          font-style: italic;
          font-weight: 600;
          line-height: normal;
          display: flex;
          align-items: center;
          width: 60px;
          .circle{
            width: 14px;
            height: 14px;
            background: rgba(49, 76, 254, 0.40);
            border-radius: 14px;
          }
          .item{
            margin-left: -5px;
          }
        }
        .right{
          color: #536887;
          font-family: Roboto;
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: 19px;
          width: 258px;
          .content{
            margin-bottom: 14px;
          }
          .show-img{
            .one{
              width: 181px;
              height: 163px;
            }
            .two{
              width: 181px;
              height: 125px;
            }
            .three{
              width: 233px;
              height: 214px;
            }
          }
        }
      }
    }
  }
  .ok{
    padding-bottom: 10px;
    padding-top: 5px;
    background: #ffffff;
    .c-button{
      border-radius: 8px;
      background: #314CFE;
    }
  }
}
</style>
