<template>
  <div class="loan-terms">
    <div class="select-loan-terms" @click="selectTerm()">
      <div class="left">
        <div class="title">Select Loan Terms</div>
        <div class="days" v-text="indexProduct.type"></div>
        <div class="detail" v-if="indexProduct.tips" v-text="indexProduct.tips"></div>
      </div>
      <div class="right">
        <img :src="customType === 'withdraw' ? arrowDown2 : arrowDown" alt="">
      </div>
      <div v-if="tag14and2User" class="click-tips" :style="{ 'background-image': `url(${require(`@/assets/images/click-more.svg`)})`}">
        <div>Click for More Offers</div>
      </div>
    </div>
    <!-- 产品列表 -->
    <van-popup v-model="showProductList" class="product-list-pop" position="bottom" :close-on-click-overlay="false">
      <div class="close" @click="closeProduct()">
        <img :src="closeImg" alt="" />
      </div>
      <div class="product-list">
        <div class="title">Select Loan Terms</div>
        <ul class="list">
          <li class="product" v-for="(item, index) in productList" :key="item.productId"  @click="chooseType(index, 'click')" :class="{'last-mul': caluLastMul(index)}">
            <div class="term-title" v-if="showTitle(index)">
              <div class="sub-title" v-text="item.tips ? 'Multiple Terms' : 'One Term'"></div>
              <div class="sub-tips" v-text="item.tips ? 'Higher credit limit, lower daily interest rate' : 'One-time payment of principal and interest'"></div>
              <div class="amount-add" v-if="index === 0 && item.tips && !(productList[productList.length - 1] && productList[productList.length - 1].tips)">
                <div class="wrap">
                  <div class="num">Recommend</div>
                  <div class="arrow"></div>
                </div>
              </div>
            </div>
            <div class="product-detail" :class="{mul: item.tips, single: !item.tips}">
              <div class="left">
                <div class="item">
                  <div class="day" :class="{'lock': item.islock}" v-text="item.type"></div>
                  <img v-if="index === 0" src="@/assets/images/recommend.png" alt="" />
                </div>
                <div class="detail" v-if="item.tips" v-text="item.tips"></div>
                <!-- <div class="rate" v-text="`Daily interest rate: ${Number(item.rate).toFixed(2)}%`"></div> -->
                <div class="recommend" v-if="index === 0">
                  <div>Limited 3 days interest rate cut!</div>
                </div>
              </div>
              <div class="right">
                <div>
                  <div v-if="!item.islock" class="rate" :class="{active: index == active}" v-text="`${Number(item.rate).toFixed(2)}%/day`"></div>
                  <div v-if="!item.islock && index === 0" class="rate before" :class="{active: index == active}" v-text="`${Number(item.rate + 2).toFixed(2)}%/day`"></div>
                </div>
                <img v-if="!item.islock" class="choose" :src="index === active ? require(`@/assets/images/${productSource}/index-paid.png`) : unPaid" alt="">
                <img v-else class="lock" src="@/assets/images/locky-ic.png" alt="">
              </div>
            </div>
          </li>
        </ul>
      </div>
    </van-popup>
  </div>
</template>

<script>
import arrowDown from '@/assets/images/arrow-down.png';
import arrowDown2 from '@/assets/images/arrow-down2.png';
import closeImg from '@/assets/images/cancel.png';
import unPaid from '@/assets/images/un-paid.png';
export default {
  name: 'loanTerms',
  model: {
    prop: 'active',
  },
  props: {
    active: {
      type: Number,
      default: -1
    },
    proIndex: {
      type: Number,
      default: 0
    },
    productList: {
      type: Array,
      default: function() {
        return []
      }
    },
    productSource: {
      type: String,
      default: ''
    },
    customType: { // 自定义类型
      type: String,
      default: ''
    },
    tag14and2User: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    indexProduct() {
      let vm = this;
      let product = {};
      if (vm.active > -1) {
        product = vm.productList[vm.active];
      }
      console.log('product', product, vm.productList, vm.active);
      return product;
    }
  },
  data() {
    return {
      arrowDown,
      closeImg,
      unPaid,
      showProductList: false,
      arrowDown2
    };
  },

  mounted() {

  },

  methods: {
    // 显示产品选择
    selectTerm() {
      this.showProductList = true;
      this.report('select_term_click');
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: vm.customType === 'withdraw' ? 'index' : 'repaymentIndex',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    // 关闭产品选择
    closeProduct() {
      this.showProductList = false;
    },
    // 选择产品
    chooseType(index, type) {
      const vm = this;
      if (vm.productList[index].islock) {
        vm.$toast('Withdraw now and this loan term can be unlocked next time.');
        return;
      }
      vm.closeProduct();
      vm.$emit('chooseType', {
        index: index,
        type: type,
        customType: vm.customType
      });
    },
    // 控制标题显示
    showTitle(index) {
      let vm = this;
      let productList = vm.productList;
      console.log('productList', productList);
      // 开始标题计算
      if (index === 0) {
        return true;
      } else if (index > 0) {
        if (productList[index - 1] && productList[index - 1].tips && !productList[index].tips) {
          // tips为多期标志, 这里为真实用户的多期数据
          return true;
        } else if (productList[index - 1] && !productList[index - 1].tips && productList[index].tips) {
          // tips为多期标志, 这里为单期时，前端构造的用户的多期数据
          return true;
        } else {
          return false;
        }
      }
    },
    // 间隔控制
    caluLastMul(index) {
      let productList = this.productList;
      if (index > 0) {
        // tips为多期标志
        if (productList[index].tips && productList[index + 1] && !productList[index + 1].tips) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    }
  },
};
</script>

<style lang="scss" scoped>

.loan-terms{

  .select-loan-terms{
    background: #FFFFFF;
    box-shadow: 0 2px 20px 0 rgb(0 54 101 / 6%);
    border-radius: 8px;
    height: 94px;
    display: flex;
    position: relative;
    .left {
      width: calc(100% - 50px);
      text-align: left;
      padding-top: 9px;
      padding-bottom: 13px;
      padding-left: 18px;
      .title{
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #1B3155;
        margin-bottom: 7px;
      }
      .days{
        font-family: Avenir-Heavy;
        font-size: 18px;
        color: $themeColor;
        margin-bottom: 8px;
      }
      .detail{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #919DB3;
        letter-spacing: 0;
        transform: scale(.84);
        margin-left: -17px;
      }
    }
    .right{
      width: 50px;
      background: $themeColor;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      img{
        width: 18px;
        height: 18px;
      }
    }
    .click-tips{
      position: absolute;
      left: 120px;
      top: 11px;
      width: 110px;
      height: 15px;
      font-family: Avenir;
      font-size: 12px;
      font-weight: 500;
      line-height: 15px;
      text-align: left;
      color: #FFFFFF;
      background-size: 100%;
      div{
        transform: scale(.84);
        height: 15px;
        width: 110px;
        width: 128px;
        margin-left: -5px;
      }
    }
  }

  .product-list-pop{
    background-color: inherit;
    overflow: hidden;
  }
  .close{
    text-align: left;
    img{
      width: 38px;
      height: 38px;
      margin-left: 16px;
      margin-bottom: -8px;
    }
  }
  .product-list{
    padding: 25px 0 0 0px;
    background: #ffffff;
    height: 480px;
    padding: 0px 0 0 0px;
    .title{
      padding-left: 30px;
      height: 45px;
      font-size: 18px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: #1b3155;
      background: #F5F5F5;
      display: flex;
      align-items: center;
    }
    .list{
      overflow-y: scroll;
      height: 440px;
      .product{
        padding-right: 19px;
        margin-left: 30px;
        position: relative;

        &::after{
          position: absolute;
          box-sizing: border-box;
          content: " ";
          pointer-events: none;
          right: 15px;
          bottom: -1px;
          left: 0px;
          border-bottom: 1px solid #dddddd;
          transform: scaleY(0.5);
          width: 100%;
        }

        &.last-mul::after{
          left: -31px;
          border-bottom: 1px solid #F5F5F5;
          transform: scaleY(5);
          width: 109%;
        }

        .term-title{
          position: relative;
          margin-top: 15px;
          padding-bottom: 7px;

          &::after{
            position: absolute;
            box-sizing: border-box;
            content: " ";
            pointer-events: none;
            right: 15px;
            bottom: 0px;
            left: 0px;
            border-bottom: 1px solid #dddddd;
            transform: scaleY(0.5);
            width: 106%;
          }
          .sub-title{
            font-family: Avenir-Medium;
            font-size: 14px;
            color: #536887;
            letter-spacing: 0;
            font-weight: 500;
            text-align: left;
          }
          .sub-tips{
            font-family: Avenir-Medium;
            font-size: 12px;
            color: #919DB3;
            letter-spacing: 0;
            font-weight: 500;
            text-align: left;
            transform: scale(0.9);
            margin-left: -16px;
          }
          .amount-add{
            position: absolute;
            left: 96px;
            top: -5px;
            transform: scale(.85);
            .wrap{
              position: relative;
              .num{
                background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
                color: #ffffff;
                width: auto;
                white-space: nowrap;
                padding: 2px 4px;
                font-size: 12px;
              }
              .arrow{
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 5px 0 5px 7px;
                border-color: transparent transparent transparent #C78900;
                position: absolute;
                left: 0px;
                top: 12px;
              }
            }
          }
        }

        .product-detail{

          display: flex;
          justify-content: space-between;
          align-items: center;

          &.mul{
            height: 90px;
            .day{
              font-size: 21px;
            }
          }

          &.single{
            height: 60px;
            .day{
              font-size: 18px;
            }
          }
          .left{
            text-align: left;
            .item {
              margin-bottom: 3px;
              display: flex;
              align-items: flex-end;
              .day{
                font-family: DINAlternate-Bold;
                color: #1B3155;
                letter-spacing: 0;
                &.lock{
                  color: #919DB3;
                }
              }
              img{
                width: 16px;
                height: 15px;
                margin-bottom: 6px;
                margin-left: 5px;
              }
            }
            .detail{
              font-family: Avenir-Medium;
              font-size: 12px;
              color: #919DB3;
              letter-spacing: 0;
              margin-bottom: 2px;
              width: 174px;
              transform: scale(0.84);
              margin-left: -14px;
            }
          }
          .right {
            display: flex;
            justify-content: space-between;
            .rate{
              font-family: Avenir-Medium;
              font-size: 12px;
              color: #919DB3;
              letter-spacing: 0;
              transform: scale(0.9);
              margin-right: 7px;
              &.active{
                color: $themeColor;
              }
              &.before{
                color: #1B3155;
                text-decoration:line-through;
              }
            }
          }
        }
        .recommend{
          width: 205px;
          height: 25px;
          background: #FDEFDA;
          border-radius: 3px;
          color: #FF8D00;
          display: flex;
          align-items: center;
          transform: scale(0.84);
          margin-left: -16px;
          font-size: 12px;
          div{
            margin-left: 5px;
          }
          img{
            width: 10px;
            height: 10px;
            margin-left: 5px;
            margin-right: 5px;
          }
        }
        img{
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
