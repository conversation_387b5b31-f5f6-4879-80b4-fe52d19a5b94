<template>
  <div :class="[
    type === 'textarea' ? 'palm-textarea' : 'palm-input',
    {
      'is-disabled': inputDisabled,
      'is-exceed': inputExceed,
      'palm-input-group': $slots.prepend || $slots.append || label,
      'palm-input-group--append': $slots.append,
      'palm-input-group--prepend': $slots.prepend,
      'palm-input--prefix': $slots.prefix,
      'palm-input--suffix': $slots.suffix || showPassword
    }
    ]"
  >
    <div class="palm-input-group__label" v-if="label" :class="[((value || focused || $attrs.placeholder)) ? 'palm-input-group__label-focus' : '']" :style="$attrs.placeholder && !focused ? 'color: #c4cad5' : ''" @click="focus">
      <span v-if="isRequired" class="label_required">*</span>{{label}}
    </div>
    <template v-if="type !== 'textarea'">
      <!-- 前置元素 -->
      <div class="palm-input-group__prepend" v-if="$slots.prepend">
        <slot name="prepend"></slot>
      </div>
      <input
        :tabindex="tabindex"
        v-if="type !== 'textarea'"
        class="palm-input__inner"
        v-bind="$attrs"
        :type="showPassword ? (passwordVisible ? 'text': 'password') : type"
        :disabled="inputDisabled"
        :readonly="readonly"
        :autocomplete="autocomplete"
        ref="input"
        @compositionstart="handleCompositionStart"
        @compositionupdate="handleCompositionUpdate"
        @compositionend="handleCompositionEnd"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChange"
        @paste="handlePaste"
        @keyup="handleKeyup"
        :aria-label="label"
      >
      <!-- 前置内容 -->
      <span class="palm-input__prefix" v-if="$slots.prefix">
        <slot name="prefix"></slot>
      </span>
      <!-- 后置内容 -->
      <span
        class="palm-input__suffix"
        v-if="getSuffixVisible()">
        <span class="palm-input__suffix-inner">
          <template v-if="!showPwdVisible || !isWordLimitVisible">
            <slot name="suffix"></slot>
          </template>
          <i v-if="showPwdVisible"
            class="palm-input__icon palm-icon-view palm-input__clear"
            @click="handlePasswordVisible"
          ></i>
          <span v-if="isWordLimitVisible" class="palm-input__count">
            <span class="palm-input__count-inner" :class="focused ? 'focused' : ''">
              {{ textLength }}/{{ upperLimit }}
            </span>
          </span>
        </span>
      </span>
      <!-- 后置元素 -->
      <div class="palm-input-group__append" v-if="$slots.append">
        <slot name="append"></slot>
      </div>
    </template>
    <textarea
      v-else
      :tabindex="tabindex"
      class="palm-textarea__inner"
      @compositionstart="handleCompositionStart"
      @compositionupdate="handleCompositionUpdate"
      @compositionend="handleCompositionEnd"
      @input="handleInput"
      ref="textarea"
      v-bind="$attrs"
      :disabled="inputDisabled"
      :readonly="readonly"
      :autocomplete="autocomplete"
      :style="textareaStyle"
      @focus="handleFocus"
      @blur="handleBlur"
      @change="handleChange"
      :aria-label="label"
    >
    </textarea>
    <span v-if="isWordLimitVisible && type === 'textarea'" class="palm-input__count" :class="focused ? 'focused' : ''">{{ textLength }}/{{ upperLimit }}</span>
  </div>
</template>

<script>
import calcTextareaHeight from '../utils/calcTextareaHeight';
export default {
  name: 'PalmInput',
  componentName: 'PalmInput',
  inheritAttrs: false,
  data() {
    return {
      textareaCalcStyle: {},
      focused: false,
      isComposing: false,
      passwordVisible: false
    };
  },
  props: {
    value: [String, Number],
    resize: String,
    form: String,
    disabled: Boolean,
    readonly: Boolean,
    type: {
      type: String,
      default: 'text'
    },
    autosize: {
      type: [Boolean, Object],
      default: false
    },
    autocomplete: {
      type: String,
      default: 'off'
    },
    label: String,
    showPassword: {
      type: Boolean,
      default: false
    },
    showWordLimit: {
      type: Boolean,
      default: false
    },
    tabindex: String,
    isRequired: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    nativeInputValue() {
      return this.value === null || this.value === undefined ? '' : String(this.value);
    },
    inputDisabled() {
        return this.disabled;
    },
    showPwdVisible() {
      return this.showPassword &&
        !this.inputDisabled &&
        !this.readonly &&
        (!!this.nativeInputValue || this.focused);
    },
    isWordLimitVisible() {
      return this.showWordLimit &&
        this.$attrs.maxlength &&
        (this.type === 'text' || this.type === 'tel' || this.type === 'textarea') &&
        !this.inputDisabled &&
        !this.readonly &&
        !this.showPassword;
    },
    textareaStyle() {
      const merge = function(target) {
        for (let i = 1, j = arguments.length; i < j; i++) {
          const source = arguments[i] || {};
          for (const prop in source) {
            if (Object.prototype.hasOwnProperty.call(source, prop)) {
              const value = source[prop];
              if (value !== undefined) {
                target[prop] = value;
              }
            }
          }
        }

        return target;
      };
      return merge({}, this.textareaCalcStyle, { resize: this.resize });
    },
    upperLimit() {
      return this.$attrs.maxlength;
    },
    textLength() {
      if (typeof this.value === 'number') {
        return String(this.value).length;
      }
      return (this.value || '').length;
    },
    inputExceed() {
      // show exceed style if length of initial value greater then maxlength
      return this.isWordLimitVisible &&
        (this.textLength > this.upperLimit);
    }
  },
  watch: {
    value() {
      this.$nextTick(this.resizeTextarea);
    },
    nativeInputValue() {
      this.setNativeInputValue();
    },
    type() {
      this.$nextTick(() => {
        this.setNativeInputValue();
        this.resizeTextarea();
      });
    }
  },
  methods: {
    focus() {
      this.getInput().focus();
    },
    blur() {
      this.getInput().blur();
    },
    handleBlur(event) {
      this.focused = false;
      this.$emit('blur', event);
    },
    select() {
      this.getInput().select();
    },
    resizeTextarea() {
      if (this.$isServer) return;
      const { autosize, type } = this;
      if (type !== 'textarea') return;
      if (!autosize) {
        this.textareaCalcStyle = {
          minHeight: calcTextareaHeight(this.$refs.textarea).minHeight
        };
        return;
      }
      const minRows = autosize.minRows;
      const maxRows = autosize.maxRows;
      this.textareaCalcStyle = calcTextareaHeight(this.$refs.textarea, minRows, maxRows);
    },
    setNativeInputValue() {
      const input = this.getInput();
      if (!input) return;
      if (input.value === this.nativeInputValue) return;
      input.value = this.nativeInputValue;
    },
    // 滚动到可视区域内
    scrollIntoView() {
      if (this.$attrs.id) {
        const $dom = document.getElementById(this.$attrs.id);
        $dom.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    },
    handleFocus(event) {
      this.focused = true;
      this.$emit('focus', event);
      this.$nextTick(() => {
        this.scrollIntoView()
      })
    },
    handleCompositionStart() {
      this.isComposing = true;
    },
    handleCompositionUpdate() {
      this.isComposing = true;
    },
    handleCompositionEnd(event) {
      if (this.isComposing) {
        this.isComposing = false;
        this.handleInput(event);
      }
    },
    handleKeyup(event) {
      this.$emit('keyup', event.target.value);
      this.$nextTick(this.setNativeInputValue);
    },
    handleInput(event) {
      // should not emit input during composition
      // see: https://github.com/ElemeFE/element/issues/10516
      if (this.isComposing) return;
      // hack for https://github.com/ElemeFE/element/issues/8548
      // should remove the following line when we don't support IE
      if (event.target.value === this.nativeInputValue) return;
      this.$emit('input', event.target.value);
      // ensure native input value is controlled
      // see: https://github.com/ElemeFE/element/issues/12850
      this.$nextTick(this.setNativeInputValue);
    },
    handleChange(event) {
      this.$emit('change', event.target.value);
    },
    handlePaste(event) {
      this.$emit('paste', event.target.value);
    },
    handlePasswordVisible() {
      this.passwordVisible = !this.passwordVisible;
      this.focus();
    },
    getInput() {
      return this.$refs.input || this.$refs.textarea;
    },
    getSuffixVisible() {
      return this.$slots.suffix ||
        this.showPassword ||
        this.isWordLimitVisible ||
        (this.validateState && this.needStatusIcon);
    }
  },
  created() {
      this.$on('inputSelect', this.select);
    },
  mounted() {
    this.setNativeInputValue();
    this.resizeTextarea();
  }
}
</script>

<style lang="scss" scoped>
.palm-textarea {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: bottom;
  font-size: 16px;
}

.palm-textarea__inner {
  display: block;
  resize: vertical;
  padding: 8px 6px 0 5px;
  line-height: 13px;
  caret-color: #099bfa;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #1b3155;
  background-color: #fff;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
  &::placeholder {
    color: #C0C4CC;
  }
  &:hover {
    // border-color: #C0C4CC;
  }
  &:focus {
    outline: 0;
    border-color: #099bfa;
    background-color: #fff;
  }
}

.palm-textarea .palm-input__count {
  color: #536887;
  background: #f3f5f6;
  position: absolute;
  font-size: 10px;
  bottom: 2px;
  right: 5px;
  &focused {
    background: #fff;
  }
}

.palm-textarea.is-disabled .palm-textarea__inner {
  background-color: #f3f5f6;
  border-color: transparent;
  color: #1b3155;
  cursor: not-allowed;
  &::placeholder {
    color: #C0C4CC;
  }
}

.palm-textarea.is-exceed .palm-textarea__inner {
  border-color: #F56C6C;
  font-size: 16px;
}

.palm-textarea.is-exceed .palm-input__count {
  color: #F56C6C;
  font-size: 16px;
}

.palm-input {
  position: relative;
  font-size: 16px;
  display: inline-block;
  width: 100%;
}

.palm-input::-webkit-scrollbar {
  z-index: 11;
  width: 3px
}

.palm-input::-webkit-scrollbar:horizontal {
  height: 3px
}

.palm-input::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 3px;
  background: #b4bccc
}

.palm-input::-webkit-scrollbar-corner {
  background: #fff
}

.palm-input::-webkit-scrollbar-track {
  background: #fff
}

.palm-input::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 3px
}

.palm-input .palm-input__clear {
  color: #C0C4CC;
  font-size: 7px;
  cursor: pointer;
  transition: color .2s cubic-bezier(.645, .045, .355, 1)
}

.palm-input .palm-input__clear:hover {
  color: #909399
}

.palm-input .palm-input__count {
  height: 100%;
  display: inline-flex;
  align-items: center;
  color: #536887;
  font-size: 10px
}

.palm-input .palm-input__count .palm-input__count-inner {
  background: #f3f5f6;
  line-height: initial;
  display: inline-block;
  padding: 0 2px;
  &.focused {
    background: #fff;
  }
}

.palm-input__inner {
  -webkit-appearance: none;
  box-sizing: border-box;
  color: #1b3155;
  display: inline-block;
  font-size: inherit;
  caret-color: #099bfa;
  height: 50px;
  line-height: 15px;
  outline: 0;
  padding: 12px 6px 0 12px;
  transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
  width: 100%;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #C4CAD5;
  &::placeholder {
    color: #C0C4CC;
    font-size: 16px;
  }
  &:hover {
    // border-color: #C0C4CC;
  }
}

.palm-input__prefix,
.palm-input__suffix {
  position: absolute;
  top: 0;
  -webkit-transition: all .3s;
  text-align: center;
  height: 100%;
  color: #C0C4CC
}

.palm-input.is-active .palm-input__inner,
.palm-input__inner:focus {
  background-color: #fff;
  outline: 0;
  border: 1px solid $color;
}

.palm-input__suffix {
  right: 2px;
  transition: all .3s;
  z-index: 50;
  pointer-events: none
}

.palm-input__suffix-inner {
  pointer-events: all
}

.palm-input__prefix {
  left: 2px;
  transition: all .3s
}

.palm-input__icon {
  height: 100%;
  width: 12px;
  text-align: center;
  transition: all .3s;
  line-height: 20px
}

.palm-input__icon:after {
  content: '';
  height: 100%;
  width: 0;
  display: inline-block;
  vertical-align: middle
}

.palm-input__validateIcon {
  pointer-events: none
}

.palm-input.is-disabled .palm-input__inner {
  background-color: #f3f5f6;
  border-color: transparent;
  cursor: not-allowed;
  &::placeholder {
    color: #C0C4CC;
  }
}

.palm-input.is-disabled .palm-input__icon {
  cursor: not-allowed
}

.palm-input.is-exceed .palm-input__inner {
  border-color: #F56C6C;
  font-size: 16px;
}

.palm-input.is-exceed .palm-input__suffix .palm-input__count {
  color: #F56C6C;
  font-size: 16px;
}

.palm-input--suffix .palm-input__inner {
  padding-right: 15px
}

.palm-input--prefix .palm-input__inner {
  padding-left: 15px
}

.palm-input-group {
  line-height: normal;
  display: inline-table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0
}

.palm-input-group>.palm-input__inner {
  vertical-align: middle;
  display: table-cell
}

.palm-input-group__append,
.palm-input-group__prepend {
  background-color: #F5F7FA;
  color: #909399;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #DCDFE6;
  border-radius: 2px;
  padding: 0 10px;
  width: 1px;
  white-space: nowrap
}

.palm-input-group--prepend .palm-input__inner,
.palm-input-group__append {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.palm-input-group--append .palm-input__inner,
.palm-input-group__prepend {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.palm-input-group__append:focus,
.palm-input-group__prepend:focus {
  outline: 0
}

.palm-input-group__append .palm-button,
.palm-input-group__append .palm-select,
.palm-input-group__prepend .palm-button,
.palm-input-group__prepend .palm-select {
  display: inline-block;
  margin: -5px -10px;
}

.palm-input-group__append button.palm-button,
.palm-input-group__append div.palm-select .palm-input__inner,
.palm-input-group__append div.palm-select:hover .palm-input__inner,
.palm-input-group__prepend button.palm-button,
.palm-input-group__prepend div.palm-select .palm-input__inner,
.palm-input-group__prepend div.palm-select:hover .palm-input__inner {
  border-color: transparent;
  background-color: transparent;
  color: inherit;
  border-top: 0;
  border-bottom: 0
}

.palm-input-group__append .palm-button,
.palm-input-group__append .palm-input,
.palm-input-group__prepend .palm-button,
.palm-input-group__prepend .palm-input {
  font-size: inherit
}

.palm-input-group__prepend {
  border-right: 0
}

.palm-input-group__append {
  border-left: 0
}

.palm-input-group--append .palm-select .palm-input.is-focus .palm-input__inner,
.palm-input-group--prepend .palm-select .palm-input.is-focus .palm-input__inner {
  border-color: transparent
}

.palm-input__inner::-ms-clear {
  display: none;
  width: 0;
  height: 0
}

.palm-input-group__label {
  position: absolute;
  top: calc(50% - 12px);
  left: 12px;
  color: #919DB3;
  line-height: 1.5;
  transition: all .15s ease-in-out;
  pointer-events: none;
  .label_required {
    color: #FF454F;
    font-family: Roboto;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-right: 3px;
  }
  &-focus {
    transform: scale(0.65);
    transform-origin: left;
    left: 12px;
    top: 2px;
    color: #536887;
  }
}
</style>
