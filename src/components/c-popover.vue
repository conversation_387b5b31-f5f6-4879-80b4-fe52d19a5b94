<template>
  <div class="c-popover">
    <div class="wrap" @click="showPopoverTips">
      <slot name="reference"></slot>
    </div>
    <div class="popover-tips" v-show="isShowPopoverTips">
      <div class="detail" v-text="tipContent"></div>
      <div class="triangle"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CPopover',
  props: {
    tipContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isShowPopoverTips: false
    };
  },

  mounted() {
    document.addEventListener('touchstart', this.outerTouchStart)
  },

  destroyed() {
    document.removeEventListener('touchstart', this.outerTouchStart)
  },

  methods: {
    showPopoverTips() {
      console.log('111')
      this.isShowPopoverTips = true;
    },
    outerTouchStart() {
      this.isShowPopoverTips = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.c-popover{
  .popover-tips{
    padding: 5px;
    font-size: 12px;
    position: absolute;
    width: 128px;
    right: 20px;
    top: -42px;
    background-color: #4a4a4a;
    color: #fff;
    border-radius: 8px;
    height: 44px;
    .triangle{
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 12px 6px 0 6px;
      border-color: #4a4a4a transparent transparent transparent;
      border-width: 6px 6px 0 6px;
      position: absolute;
      bottom: -5px;
      left: 23px;
    }
  }
}
</style>