// step1,step2,step3中，点击next step表单校验，报错锚点（锚点到第一个报错位置）
//import { jumpIndex } from '@/assets/js/anchorPoint.js'
//调用该方法，需要传入index(数值)
let timer

export function jumpIndex(index) {
    clearTimeout(timer)
    // 用 id="d_jump_1" 形式
    const className = 'd_jump_' + index;
    var element = document.getElementById(className);
    timer = setTimeout(() => {
        element.scrollIntoView({
          block: "center",
          behavior: "smooth"
        });
    }, 300);
}