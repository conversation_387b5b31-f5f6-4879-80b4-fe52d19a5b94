// import { getQueryString } from "@/assets/js/common";

import sdkConfig from './sdkFun' // 新的统一入口
import { requestSwitch, responseSwitch } from '@/api/paramSwitch'

import { Toast } from "vant";
import request from '@/assets/js/request.js';
import { decode, encode } from 'js-base64';

const quickSave = window.quickSave || window.webkit
const flutterApp = window.flutter_inappwebview;

// 原生方法挂载的对象
let nativeObj = '';
// 调用原生的其他方法封装名字
let nativeFun = '';
// 调用代理请求的封装名字
let nativeRequest = '';
// 引入所有方法的对象
let nativeMethods = '';
// 原生对象类型
let nativeObjType = ''; // android | flutter | ios 默认

// 使用统一配置替代条件判断
nativeObj = window[sdkConfig.nativeObj];
nativeFun = sdkConfig.commonRequst;
nativeRequest = sdkConfig.httpsRequest;
nativeMethods = sdkConfig;
nativeObjType = sdkConfig.nativeObjType;

// 默认情况处理
if (!nativeObj || !nativeFun) {
  nativeObj = quickSave;
  nativeRequest = 'httpsRequest';
  nativeMethods = {
    nativeBackCallback: 'nativeBackCallback'
  }
  if (quickSave && quickSave.messageHandlers) {
    nativeObjType = 'ios'
  } else {
    nativeObjType = 'android'
  }
}
// 打印变量值
console.log('native.js--------------nativeObj:', nativeObj);
console.log('native.js--------------nativeFun:', nativeFun);
console.log('native.js--------------nativeRequest:', nativeRequest);
console.log('native.js--------------nativeMethods:', nativeMethods);
console.log('native.js--------------nativeObjType:', nativeObjType);

const loading = Toast.loading;

export const showToast = (str, pos) => {
  Toast({
    message: str,
    position: pos || 'middle'
  })
}

export const iosNativeMethod = async (method) => {
  const callback = `${method}${Math.random().toString(16).replace(/\./, '')}cb`;
  quickSave.messageHandlers[method].postMessage(JSON.stringify([callback]))
  let value = '';
  await new Promise((resolve) => {
    window[callback] = (res) => {
      try {
        value = JSON.parse(decode(res))
        console.log('ios-原生方法：', method, '值：', value)
      } catch (err) {
        value = decode(res)
        console.log('ios-原生方法异常：', method, '值：', value)
      }
      resolve();
    }
  });
  return value;
}

const isNewFlutter = nativeObj && nativeFun && nativeObjType === 'flutter';

//获取加密pin
async function getAesString(data) {
  if (quickSave && quickSave.getAesString) { // 原有旧版本安卓原生能力
    console.log("getAesString");
    return quickSave.getAesString(data);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') { // 新版安卓原生能力
    console.log("nativeFungetAesString");
    return nativeObj[nativeFun](nativeMethods['getAesString'], JSON.stringify({
      pwd: data
    }), '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetAesString");
    if (isNewFlutter) { // 新版flutter原生能力
      await flutterApp.callHandler(nativeFun, nativeMethods['getAesString'], data, '').then(function (res)  {
        console.log('getAesString', res);
        value = res;
      });
    } else { // 旧版flutter原生能力
      await flutterApp.callHandler('getAesString', data).then(function (res)  {
        console.log('getAesString', res);
        value = res;
      });
    }
    return value;
    // eslint-disable-next-line no-dupe-else-if
  } else if (quickSave && quickSave.messageHandlers) { // IOS原生能力
    quickSave.messageHandlers.getAesString.postMessage(JSON.stringify(['getAesString',JSON.stringify({ pin: data})]))
    let value = '';
    await new Promise((resolve) => {
      window.getAesString = (res) => {
        try {
          value = JSON.parse(decode(res))
          console.log('ios-原生方法：', 'getAesString', '值：', value)
        } catch (err) {
          value = decode(res)
          console.log('ios-原生方法异常：', 'getAesString', '值：', value)
        }
        resolve();
      }
    });
    return value;
  } else  {
    Toast('Failed to call');
    console.log('调用 getAesString 失败')
    return 'DC3FBA79CA47F4331DCD6C9AB8DB81DF'; // 1234(根据用户盐值加密)
  }
}

//pin是否存在
async function existedPin() {
  if (quickSave && quickSave.existedPin) {
    console.log("existedPin");
    return quickSave.existedPin();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunexistedPin");
    return nativeObj[nativeFun](nativeMethods['existedPin'], '', '') === 'true' ? true : false;
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterExistedPin");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['existedPin'], '', '').then(function (res)  {
        console.log('existedPin', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('existedPin').then(function (res)  {
        console.log('existedPin', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('existedPin').then((res) =>  {
      value = res;
    });
    return value;
  } else  {
    // 用于前端调试
    // console.log('调用 existedPin 失败')
    return true;
  }
}

//标记已设置pin
function setExistedPin(data) {
  if (quickSave && quickSave.setExistedPin) {
    console.log("setExistedPin");
    quickSave.setExistedPin(data);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunsetExistedPin");
    nativeObj[nativeFun](nativeMethods['setExistedPin'], JSON.stringify({
      existed: data
    }), '');
  } else if (flutterApp || isNewFlutter) {
    console.log("flutterSetExistedPin");
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['setExistedPin'], data, '');
    } else {
      flutterApp.callHandler('setExistedPin', data);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    quickSave.messageHandlers.setExistedPin.postMessage(JSON.stringify(["setExistedPin", JSON.stringify({ existed: data })]))
  } else {
    // Toast('Failed to call');
    console.log('调用 setExistedPin 失败')
  }
}

// 获取CustId(实际上是bvn)
async function getCustId() {
  console.log("getCustIdNative");
  if (quickSave && quickSave.getCustId) {
    console.log("quickSavegetCustId");
    return quickSave.getCustId();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetCustId");
    return nativeObj[nativeFun](nativeMethods['getCustId'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterAppCustId");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getCustId'], '', '').then(function (res)  {
        console.log('getCustId', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getCustId').then(function (res)  {
        console.log('getCustId', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getCustId').then((res) =>  {
      if (res) {
        value = res.toString();
      } else {
        value = res;
      }
    });
    return value;
  } else {
    // Toast('Failed to call');
    console.log('调用 getCustId 失败');
    return '22199400803';
  }
}

// 获取CustId(实际上是bvn，加密版本，内部系统尽量使用getCustId，明文交互)
async function getEnCustId() {
  console.log("getEnCustId");
  if (quickSave && quickSave.getEnCustId) {
    console.log("quickSavegetEnCustId");
    return quickSave.getEnCustId();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetEnCustId");
    return nativeObj[nativeFun](nativeMethods['getEnCustId'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterAppgetEnCustId");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getEnCustId'], '', '').then(function (res)  {
        console.log('getEnCustId', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getEnCustId').then(function (res)  {
        console.log('getEnCustId', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getEnCustId').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    // Toast('Failed to call');
    console.log('调用 getEnCustId 失败');
    return '';
  }
}

// 只有pcx有
function getChannelId() {
  if (quickSave && quickSave.getChannelId) {
    console.log("getChannelId")
    return quickSave.getChannelId();
  } else {
    Toast('Failed to call');
    console.log('调用 getChannelId 失败');
    return 'xcross';
  }
}

// 获取地址
function getGpsInfo(data) {
  console.log("getGpsInfo")
  if (quickSave && quickSave.getGpsInfo) {
    quickSave.getGpsInfo(data);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetGpsInfo");
    nativeObj[nativeFun](nativeMethods['getGpsInfo'], JSON.stringify({ callback: data }), '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['getGpsInfo'], '', data);
    } else {
      flutterApp.callHandler('getGpsInfo', data);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    quickSave.messageHandlers.getGpsInfo.postMessage(JSON.stringify([data]))
  } else {
    Toast('Failed to call');
    console.log('调用 getGpsInfo 失败');
    return {longitude: 123, latitude: 111}
  }
}

// AC获取位置信息。
function getLocation (data) {
  if (window.dopplerLib && window.dopplerLib.getGpsInfo) {
    console.log('getGpsInfo');
    return window.dopplerLib.getGpsInfo(data);
  } else if (window.dopplerLib && window.dopplerLib.getLocation) {
    console.log('getLocation');
    return window.dopplerLib.getLocation();
  } else {
    Toast('getLocation error');
    return {longitude: 123, latitude: 111}
  }
}

// 获取wifi
async function getWifiList() {
  if (quickSave && quickSave.getWifiList) {
    console.log("getWifiList");
    return quickSave.getWifiList();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetWifiList");
    return nativeObj[nativeFun](nativeMethods['getWifiList'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetWifiList");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getWifiList'], '', '').then(function (res)  {
        console.log('getWifiList', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getWifiList').then(function (res)  {
        console.log('getWifiList', res);
        value = res;
      });
    }
    return value;
  } else if (window.dopplerLib && window.dopplerLib.getWifiList) {
    return window.dopplerLib.getWifiList();
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getWifiList').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    // Toast('Failed to call');
    console.log('调用 getWifiList 失败');
    return '';
  }
}

// 获取设备id
async function getDeviceId() {
  if (quickSave && quickSave.getDeviceId) {
    console.log("getDeviceId");
    return quickSave.getDeviceId();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetDeviceId");
    return nativeObj[nativeFun](nativeMethods['getDeviceId'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetDeviceId");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getDeviceId'], '', '').then(function (res)  {
        console.log('getDeviceId', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getDeviceId').then(function (res)  {
        console.log('getDeviceId', res);
        value = res;
      });
    }
    return value;
  } else if (window.dopplerLib && window.dopplerLib.getDeviceInfo) {
    console.log("getDeviceInfo");
    let deviceInfo = JSON.parse(window.dopplerLib.getDeviceInfo());
    return  deviceInfo.deviceId;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getDeviceId').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to getDeviceId');
    console.log('调用 getDeviceId 失败')
    if (process.env.NODE_ENV === 'development') {
      return '51dd14d39ad460f8bbe71b09a247b4be'
    }
  }
}

// 跳转借款列表页
function gotoLoanList() {
  console.log("gotoLoanList")
  if (quickSave && quickSave.gotoLoanList) {
    quickSave.gotoLoanList();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungotoLoanList");
    nativeObj[nativeFun](nativeMethods['gotoLoanList'], '', '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['gotoLoanList'], '', '');
    } else {
      flutterApp.callHandler('gotoLoanList');
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod('gotoLoanList')
  } else {
    Toast('Failed to gotoLoanList');
    console.log('调用 gotoLoanList 失败')
  }
}

// 返回首页, pageName为当前退出的页面
function gotoHomeActivity(pageName) {
  if (quickSave && quickSave.gotoHomeActivity) {
    if (pageName) {
      endPage(pageName);
    }
    quickSave.gotoHomeActivity();
    finish();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    if (pageName) {
      endPage(pageName);
    }
    console.log("nativeFungotoHomeActivity");
    nativeObj[nativeFun](nativeMethods['gotoHomeActivity'], '', '');
    finish();
  } else if (flutterApp || isNewFlutter) {
    console.log("flutterAppgotoHomeActivity")
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['gotoHomeActivity'], '', '');
    } else {
      flutterApp.callHandler('gotoHomeActivity');
    }
  } else if (window.dopplerLib && window.dopplerLib.finishAndroidWebPage) {
    return window.dopplerLib.finishAndroidWebPage();
  } else if (quickSave && quickSave.messageHandlers) {
    quickSave.messageHandlers.gotoHomeActivity.postMessage(JSON.stringify(['gotoHomeActivity']))
    window.gotoHomeActivity= (res) => {
      console.log(res, '页面关闭')
    }
  } else  {
    Toast('Failed to call');
    console.log('调用 gotoHomeActivity 失败')
  }
}

// 销毁webviewer
function finish() {
  if (quickSave && quickSave.finish) {
    quickSave.finish();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    nativeObj[nativeFun](nativeMethods['finish'], '', '');
  } else if (flutterApp || isNewFlutter) {
    console.log("flutterfinish")
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['finish'], '', '');
    } else {
      flutterApp.callHandler('finish');
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod('finish')
  } else {
    console.log('调用 finish 失败')
  }
}

// trade大数据注册
function activateTradeBigdata(batchNo, custId, activateTradeBigdataCb) {
  if (quickSave && quickSave.activateTradeBigdata) {
    console.log("activateTradeBigdata")
    quickSave.activateTradeBigdata(batchNo, custId, activateTradeBigdataCb);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunactivateTradeBigdata");
    nativeObj[nativeFun](nativeMethods['activateTradeBigdata'], JSON.stringify({batchNo, custId}), activateTradeBigdataCb);
  } else if (flutterApp || isNewFlutter) {
    console.log("flutterAppActivateTradeBigdata")
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['activateTradeBigdata'], JSON.stringify({batchNo, custId}), activateTradeBigdataCb);
    } else {
      flutterApp.callHandler('activateTradeBigdata', batchNo, custId, activateTradeBigdataCb);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    const obj = {
      batchNo: batchNo,
      custId: custId
    }
    quickSave.messageHandlers.activateTradeBigdata.postMessage(JSON.stringify([activateTradeBigdataCb, JSON.stringify(obj)]))
  } else {
    Toast('Failed to call');
    console.log('调用 activateTradeBigdata 失败');
  }
}

// trade大数据注册(ac)
function activateTradeBigdataAC(batchNo, custId) {
  if (window.dopplerLib && window.dopplerLib.activateTradeBigdata) {
    return window.dopplerLib.activateTradeBigdata(batchNo, custId)
  } else {
    Toast('Failed to activateTradeBigdata');
  }
}

// 获取网络名称
async function getNetwork() {
  console.log("getNetwork")
  if (quickSave && quickSave.getNetwork) {
    return quickSave.getNetwork();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetNetwork");
    return nativeObj[nativeFun](nativeMethods['getNetwork'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetNetwork");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getNetwork'], '', '').then(function (res)  {
        console.log('getNetwork', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getNetwork').then(function (res)  {
        console.log('getNetwork', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getNetwork').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getNetwork 失败');
  }
}

async function getOsName() {
  if (quickSave && quickSave.getOsName) {
    console.log("getOsName");
    return quickSave.getOsName();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetOsName");
    return nativeObj[nativeFun](nativeMethods['getOsName'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetOsName");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getOsName'], '', '').then(function (res)  {
        console.log('getOsName', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getOsName').then(function (res)  {
        console.log('getOsName', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getOsName').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getOsName 失败');
  }
}

async function getDefaultTimezone() {
  if (quickSave && quickSave.getDefaultTimezone) {
    console.log("getDefaultTimezone");
    return  quickSave.getDefaultTimezone();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetDefaultTimezone");
    return nativeObj[nativeFun](nativeMethods['getDefaultTimezone'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetDefaultTimezone");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getDefaultTimezone'], '', '').then(function (res)  {
        console.log('getDefaultTimezone', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getDefaultTimezone').then(function (res)  {
        console.log('getDefaultTimezone', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getDefaultTimezone').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getDefaultTimezone 失败');
  }
}

async function getOsVersion() {
  if (quickSave && quickSave.getOsVersion) {
    console.log("getOsVersion");
    return quickSave.getOsVersion();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetOsVersion");
    return nativeObj[nativeFun](nativeMethods['getOsVersion'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetOsVersion");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getOsVersion'], '', '').then(function (res)  {
        console.log('getOsVersion', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getOsVersion').then(function (res)  {
        console.log('getOsVersion', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getOsVersion').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getOsVersion 失败');
  }
}

async function getCurrentAppVersionName() {
  if (quickSave && quickSave.getCurrentAppVersionName) {
    console.log("getCurrentAppVersionName");
    return quickSave.getCurrentAppVersionName();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetCurrentAppVersionName");
    return nativeObj[nativeFun](nativeMethods['getCurrentAppVersionName'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetCurrentAppVersionName");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getCurrentAppVersionName'], '', '').then(function (res)  {
        console.log('getCurrentAppVersionName', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getCurrentAppVersionName').then(function (res)  {
        console.log('getCurrentAppVersionName', res);
        value = res;
      });
    }
    return value;
  } else if (window.dopplerLib && window.dopplerLib.getCurrentAppVersionName) {
    return window.dopplerLib.getCurrentAppVersionName();
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getCurrentAppVersionName').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getCurrentAppVersionName 失败');
  }
}

async function getCurrentAppVersion() {
  if (quickSave && quickSave.getCurrentAppVersion) {
    console.log("getCurrentAppVersion");
    return quickSave.getCurrentAppVersion();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetCurrentAppVersion");
    return nativeObj[nativeFun](nativeMethods['getCurrentAppVersion'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("flutterGetCurrentAppVersion");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getCurrentAppVersion'], '', '').then(function (res)  {
        console.log('getCurrentAppVersion', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getCurrentAppVersion').then(function (res)  {
        console.log('getCurrentAppVersion', res);
        value = res;
      });
    }
    return value;
  } else if (window.dopplerLib && window.dopplerLib.getCurrentAppVersion) {
    return window.dopplerLib.getCurrentAppVersion();
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getCurrentAppVersion').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getCurrentAppVersion 失败');
  }
}

// 获取用户uid
async function getUid() {
  if (quickSave && quickSave.getUid) {
    console.log("getUid");
    return quickSave.getUid();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetUid");
    return nativeObj[nativeFun](nativeMethods['getUid'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log('flutterAppGetUid');
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getUid'], '', '').then(function (res)  {
        console.log('nativeFun-getUid', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getUid').then(function (res)  {
        console.log('getUid', res);
        value = res;
      });
    }
    return value;
  } else if (quickSave && quickSave.messageHandlers) {
    // eslint-disable-next-line no-unused-vars
    let value = '';
    await iosNativeMethod('getUid').then((res) => {
      value = res;
    })
    return value;
  } else {
    Toast('Failed to call');
    console.log('调用 getUid 失败');
    return '2628212';
  }
}

// 广告回传
function applyAppsflyerData() {
  console.log("applyAppsflyerData")
  if (quickSave && quickSave.applyAppsflyerData) {
    quickSave.applyAppsflyerData();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunapplyAppsflyerData");
    nativeObj[nativeFun](nativeMethods['applyAppsflyerData'], '', '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['applyAppsflyerData'], '', '');
    } else {
      flutterApp.callHandler('applyAppsflyerData');
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod('applyAppsflyerData')
  } else {
    Toast('Failed to call');
    console.log('调用 applyAppsflyerData 失败');
  }
}

// 广告回传：可设定evertkey
function addAppsflyerData(eventKey) {
  console.log("addAppsflyerData")
  if (quickSave && quickSave.addAppsflyerData) {
    quickSave.addAppsflyerData(eventKey);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunaddAppsflyerData");
    nativeObj[nativeFun](nativeMethods['addAppsflyerData'], JSON.stringify({eventKey}), '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['addAppsflyerData'], eventKey, '');
    } else {
      flutterApp.callHandler('addAppsflyerData', eventKey);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod('addAppsflyerData')
  } else {
    // Toast('Failed to call');
    console.log('调用 addAppsflyerData 失败');
  }
}

// 绑账户
function gotoBindBankAccount(callback) {
  console.log("gotoBindBankAccount")
  if (quickSave && quickSave.gotoBindBankAccount) {
    quickSave.gotoBindBankAccount(callback);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungotoBindBankAccount");
    nativeObj[nativeFun](nativeMethods['gotoBindBankAccount'], '', callback);
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['gotoBindBankAccount'], callback, '');
    } else {
      flutterApp.callHandler('gotoBindBankAccount', callback);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod(callback)
  } else {
    Toast('Failed to call');
    console.log('调用 gotoBindBankAccount 失败');
    return {result: 'success'}
  }
}

// 绑卡
function gotoBindBankCard(callback) {
  console.log("gotoBindBankCard")
  if (quickSave && quickSave.gotoBindBankCard) {
    quickSave.gotoBindBankCard(callback);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungotoBindBankCard");
    nativeObj[nativeFun](nativeMethods['gotoBindBankCard'], '', callback);
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['gotoBindBankCard'], callback, '');
    } else {
      flutterApp.callHandler('gotoBindBankCard', callback);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod(callback)
  } else {
    Toast('Failed to call');
    console.log('调用 gotoBindBankCard 失败');
    return {result: 'success'}
  }
}

// 拨打电话(flutterApp中测试)
function callPhone(phoneNum) {
  console.log("callPhone");
  if (quickSave && quickSave.callPhone) {
    quickSave.callPhone(phoneNum)
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFuncallPhone");
    nativeObj[nativeFun](nativeMethods['callPhone'], JSON.stringify({phoneNum}), '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['callPhone'], phoneNum, '');
    } else {
      flutterApp.callHandler('callPhone', phoneNum);
    }
  } else if (quickSave && quickSave.messageHandlers) {
    iosNativeMethod('callPhone')
  } else {
    Toast('Failed to call');
  }
}

// 进入页面时调用此接口，pageName为页面名称(IOS暂时不支持)
function startPage(pageName) {
  console.log("startPage");
  if (quickSave && quickSave.startPage) {
    quickSave.startPage(pageName)
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunstartPage");
    nativeObj[nativeFun](nativeMethods['startPage'], JSON.stringify({pageName}), '');
  } else if (isNewFlutter) {
    flutterApp.callHandler(nativeFun, nativeMethods['startPage'], pageName, '');
  }
}

// 退出此页面时调用此接口，pageName为页面名称(IOS暂时不支持)
function endPage(pageName) {
  console.log("endPage");
  if (quickSave && quickSave.endPage) {
    quickSave.endPage(pageName)
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunendPage");
    nativeObj[nativeFun](nativeMethods['endPage'], JSON.stringify({pageName}), '');
  } else if (isNewFlutter) {
    flutterApp.callHandler(nativeFun, nativeMethods['endPage'], pageName, '');
  }
}

// 点击事件接口，pageName为页面名称，eventName为事件名称(IOS暂时不支持)
function logClickEvent(pageName, eventName) {
  console.log("logClickEvent");
  if (quickSave && quickSave.logClickEvent) {
    quickSave.logClickEvent(pageName, eventName)
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunlogClickEvent");
    nativeObj[nativeFun](nativeMethods['logClickEvent'], JSON.stringify({pageName, eventName}), '');
  } else if (isNewFlutter) {
    flutterApp.callHandler(nativeFun, nativeMethods['logClickEvent'], pageName, '');
  }
}

// 曝光接口，pageName为页面名称，eventName为事件名称(IOS暂时不支持)
function logViewEvent(pageName, eventName) {
  console.log("logViewEvent");
  if (quickSave && quickSave.logViewEvent) {
    quickSave.logViewEvent(pageName, eventName)
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunlogViewEvent");
    nativeObj[nativeFun](nativeMethods['logViewEvent'], JSON.stringify({pageName, eventName}), '');
  } else if (isNewFlutter) {
    flutterApp.callHandler(nativeFun, nativeMethods['logViewEvent'], pageName, '');
  }
}

function isPhone (){
  if (window.dopplerLib && window.dopplerLib.isPhone) {
    return window.dopplerLib.isPhone()
  } else {
    console.log('调用isPhone失败')
  }
}

function getBatchNo() {
  if (window.dopplerLib && window.dopplerLib.getBatchNo) {
    return window.dopplerLib.getBatchNo()
  } else {
    Toast('Failed to getBatchNo');
  }
}

/**
 * 设置日历提醒
 * @param nodeCode 节点码
 * @param loanDays 借据天数
 * @param LoanId 借据loanId
*/
function setCalendar(nodeCode, loanDays, loanId) {
  if (quickSave && quickSave.setCalendar) {
    console.log("setCalendar", nodeCode, loanDays, loanId)
    quickSave.setCalendar(nodeCode, loanDays, loanId);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunsetCalendar");
    nativeObj[nativeFun](nativeMethods['setCalendar'], JSON.stringify({nodeCode, loanDays, loanId}), '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['setCalendar'], JSON.stringify({nodeCode, loanDays, loanId}), '');
    } else {
      flutterApp.callHandler('setCalendar', nodeCode, loanDays, loanId);
    }
  } else {
    console.log('调用 setCalendar 失败');
  }
}

/**
 * 跳转GooglePlay
 * @param packageName 包名
*/
function gotoGooglePlay(packageName) {
  if (quickSave && quickSave.gotoGooglePlay) {
    quickSave.gotoGooglePlay(packageName);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungotoGooglePlay");
    nativeObj[nativeFun](nativeMethods['gotoGooglePlay'], JSON.stringify({packageName}), '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['gotoAppStore'], packageName, '');
    } else {
      flutterApp.callHandler('gotoAppStore', packageName);
    }
  } else {
    console.log('调用 gotoGooglePlay 失败');
  }
}

// 打开对应包的IOS应用商店
function openAppStore() {
  if (quickSave && quickSave.openAppStore) {
    quickSave.openAppStore()
  } else if (flutterApp || isNewFlutter) {
    console.log("openAppStore flutter")
    if (isNewFlutter) {
      console.log("openAppStoreisNewFlutter");
      flutterApp.callHandler(nativeFun, nativeMethods['openAppStore'], '', '');
    } else {
      console.log("openAppStoreNormal");
      flutterApp.callHandler('openAppStore');
    }
  } else if (quickSave && quickSave.messageHandlers) {
    console.log('openAppStore messageHandlers')
    quickSave.messageHandlers.openAppStore.postMessage('')
  }  else {
    console.log("调openAppStore失败");
  }
}

/**
 * 获取手机号
*/
function getPhone() {
  if (quickSave && quickSave.getPhone) {
    return quickSave.getPhone()
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFungetPhone");
    return nativeObj[nativeFun](nativeMethods['getPhone'], '', '');
  } else if (flutterApp) { // 暂时不支持
    return ''
  } else if (quickSave && quickSave.messageHandlers) { // 暂时不支持
    iosNativeMethod('getPhone')
  } else {
    if (process.env.NODE_ENV === 'development') {
      return '7012345678'
    }
    return ''
  }
}

function h5Loading(arg) {
  let baseArg = {
    loadingType: 'spinner',
    duration: 30000,
    forbidClick: true,
    ...arg
  }
  loading(baseArg)
}
function h5HideLoading() {
  loading().clear()
}

/**
 * 打开加载
*/
function showLoading(arg) {
  if (quickSave && quickSave.showLoading) {
    quickSave.showLoading();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunshowLoading");
    nativeObj[nativeFun](nativeMethods['showLoading'], '', '');
  } else {
    let baseArg = {
      loadingType: 'spinner',
      duration: 30000,
      forbidClick: true,
      ...arg
    }
    loading(baseArg);
  }
}

/**
 * 关闭加载
 * @param packageName 包名
*/
function hideJsLoading() {
  if (quickSave && quickSave.hideJsLoading) {
    quickSave.hideJsLoading();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunhideJsLoading");
    nativeObj[nativeFun](nativeMethods['hideJsLoading'], '', '');
  } else {
    loading().clear()
  }
}

/**
 * IOS归因埋点上报
 * @param enentType 事件类型
*/
function uploadAdsEvent(enentType) {
  if (quickSave && quickSave.uploadAdsEvent) {
    quickSave.uploadAdsEvent(enentType);
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) { // 暂时不提供
      // flutterApp.callHandler(nativeFun, nativeMethods['uploadAdsEvent'], enentType);
    } else {
      flutterApp.callHandler('uploadAdsEvent', enentType);
    }
  } else {
    console.log('调用 uploadAdsEvent 失败');
  }
}

/**
 * 客户端log方法，提交输入日志，可通过客户端日志查看用户操作
 * @param tag 标签， 自定义
 * @param msg 日志信息， string类型
 *
*/
function log(tag, msg) {
  if (quickSave && quickSave.log) {
    quickSave.log(tag, msg);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log('nativeFunlog');
    nativeObj[nativeFun](nativeMethods['log'], JSON.stringify({tag, msg}), '');
  }else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['log'], JSON.stringify({tag, msg}), '');
    } else {
      flutterApp.callHandler('log', tag, msg);
    }
  } else {
    console.log('调用 log 失败');
  }
}

/**文件上传，viewsType可查看类型，fileType可选择类型，是否多选isMore */
function openFeedBackFile(callBack, canSelectedTypes, viewsType, isMore) {
  if (quickSave && quickSave.openFeedBackFile) {
    console.log("openFeedBackFile 成功")
    return quickSave.openFeedBackFile(callBack, canSelectedTypes, viewsType, isMore);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("nativeFunopenFeedBackFile");
    nativeObj[nativeFun](nativeMethods['openFeedBackFile'], JSON.stringify({
      callback: callBack,
      viewsType: viewsType,
      canSelectedTypes: canSelectedTypes,
      allowMultipe: isMore
    }), '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['openFeedBackFile'], JSON.stringify({
        callback: callBack,
        viewsType: viewsType,
        canSelectedTypes: canSelectedTypes,
        allowMultipe: isMore
      }), '')
    } else {
      flutterApp.callHandler("openFeedBackFile")
    }
  } else {
    console.log("openFeedBackFile 失败")
    return ''
  }
}

// 获取系统版本
function getOsVersionCode() {
  if (quickSave && quickSave.getOsVersionCode) {
    console.log("getOsVersionCode 成功")
    return quickSave.getOsVersionCode()
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("getOsVersionCode");
    return nativeObj[nativeFun](nativeMethods['getOsVersionCode'], '', '');
  } else if (flutterApp) {
    // return flutterApp.callHandler("getOsVersionCode")
    // IOS没有，屏蔽
    return 20
  } else {
    console.log("getOsVersionCode 失败")
    if (process.env.NODE_ENV === 'development') {
      return 29
    }
    return 20
  }
}

// 跳转银行balance(客户端必须支持银行balance)
function gotoBalanceTab() {
  if (quickSave && quickSave.gotoBalanceTab) {
    console.log("gotoBalanceTab 成功")
    quickSave.gotoBalanceTab();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("gotoBalanceTab");
    nativeObj[nativeFun](nativeMethods['gotoBalanceTab'], '', '');
  } else if (flutterApp || isNewFlutter) {
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['gotoBalanceTab'], '', '')
    } else {
      flutterApp.callHandler("gotoBalanceTab")
    }
  } else {
    console.log("gotoBalanceTab 失败")
  }
}

// 获取客户端功能支持列表，没有返回对应的key就是没有值
async function getSupportedFeatures() {
  if (quickSave && quickSave.getSupportedFeatures) {
    console.log("getSupportedFeatures");
    return quickSave.getSupportedFeatures();
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') {
    console.log("getSupportedFeatures");
    return nativeObj[nativeFun](nativeMethods['getSupportedFeatures'], '', '');
  } else if (flutterApp || isNewFlutter) {
    let value = '';
    console.log("getSupportedFeatures");
    if (isNewFlutter) {
      await flutterApp.callHandler(nativeFun, nativeMethods['getSupportedFeatures'], '', '').then(function (res)  {
        console.log('getSupportedFeatures', res);
        value = res;
      });
    } else {
      await flutterApp.callHandler('getSupportedFeatures').then(function (res)  {
        console.log('getSupportedFeatures', res);
        value = res;
      });
    }
    return value;
  } else if (window.dopplerLib && window.dopplerLib.getSupportedFeatures) {
    return window.dopplerLib.getSupportedFeatures();
  } else if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getSupportedFeatures').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    if (process.env.NODE_ENV === 'development') {
      // 没有返回对应的key就是没有值
      return JSON.stringify({
        goldbank: "true",
        // faceVerify: "true",
        messageMapping: process.env.VUE_APP_MessageMapping, // 当前设备是否支持报文映射
      });
    } else {
      console.log('调用 getSupportedFeatures 失败');
      return JSON.stringify({});
    }
  }
}


/**
 * 打开系统通讯录选择联系人
 * @returns base64解码后
 * {
 * "isEnable":true     // true，用户已选择联系人或者当前设备不支持(可以编辑)，false 用户没有选择联系人。
 * "contactsName":"",   // 联系人姓名
 * "contactsPhone":"",  // 联系人手机号
 * }
 * **/
function openSystemContact(callBack) {
  if (quickSave && quickSave.openSystemContact) {
    console.log("openSystemContact");
    quickSave.openSystemContact(callBack);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') { // 其他包暂不支持，只添加配置
    console.log("openSystemContact");
    nativeObj[nativeFun](nativeMethods['openSystemContact'], callBack, '');
  } else if (flutterApp || isNewFlutter) { // 其他包暂不支持，只添加配置
    console.log("openSystemContact");
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['openSystemContact'], callBack, '')
    } else {
      flutterApp.callHandler('openSystemContact', callBack)
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
      window[callBack](encode(JSON.stringify({
        isEnable: true,
        contactsName: '11111',
        contactsPhone: '0701 23456 78'
      })));
    } else {
      console.log('调用 openSystemContact 失败');
    }
  }
}


/**
 * 人脸识别
 * @param {*} callBack
 * @returns base64解码后
 * {
 * "faceStatus":success //success 人脸成功
 * faceUrl:"" // 人脸路径
 * liveDetectionWay:""  // 人脸验证方式
 * }
 * **/
function startFaceIdentify(callBack) {
  if (quickSave && quickSave.startFaceIdentify) {
    console.log("startFaceIdentify");
    quickSave.startFaceIdentify(callBack);
  } else if (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android') { // 其他包暂不支持，只添加配置
    console.log("startFaceIdentify");
    nativeObj[nativeFun](nativeMethods['startFaceIdentify'], callBack, '');
  } else if (flutterApp || isNewFlutter) { // 其他包暂不支持，只添加配置
    console.log("startFaceIdentify");
    if (isNewFlutter) {
      flutterApp.callHandler(nativeFun, nativeMethods['startFaceIdentify'], callBack, '')
    } else {
      flutterApp.callHandler('startFaceIdentify', callBack)
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
      window[callBack](encode(JSON.stringify({
        faceStatus: 'success',
        faceUrl: 'https://img.alicdn.com/tfs/TB1.k7zOVXXXXX7aXXXXXXXXXXX-200-200.png', // 人脸路径
        liveDetectionWay: 'face'
      })))
    } else {
      console.log('调用 startFaceIdentify 失败');
    }
  }
}


// 获取NC用户相关信息（不需要映射，只在kc系列的IOS中提供, 用于NC迁移KC）
async function getNcInfo() {
  if (quickSave && quickSave.messageHandlers) {
    let value = '';
    await iosNativeMethod('getNcInfo').then((res) =>  {
      value = res;
    });
    return value;
  } else {
    return '';
  }
}

//此方法为连接请求sdk中转到后端接口方法
function httpRequest(url, data) {
  let showErrorToast = true;
  let addHeader = {};
  let requestType = '';
  if (data.showErrorToast === false) {
    showErrorToast = data.showErrorToast;
    delete data.showErrorToast;
  }
  if (data.addHeader) {
    addHeader = data.addHeader;
    delete data.addHeader;
    if (addHeader.requestType) {
      requestType = addHeader.requestType;
      delete addHeader.requestType;
    }
  }
  const param = {
    url: url,
    body: data ? data : { data: {} },
    header: {
      ...addHeader // 新增的请求头
    }
    // callback: baseParam.callback
  }
  let callbackFun = `callback_${Math.random().toString(16).replace(/\./, '')}`
  if ((quickSave && quickSave.httpsRequest) || (window.dopplerLib && window.dopplerLib.httpsRequest) || (flutterApp) || (nativeObj && nativeObj[nativeRequest]) || (quickSave && quickSave.messageHandlers)) {
    return  new Promise((resolve, reject) => {
      if (requestType === 'buriedPointBatch' || requestType === 'uploadFile' || requestType === 'complaintUpload') {
        if (requestType === 'buriedPointBatch' || requestType === 'uploadFile') {
          console.log('X-BizCode', requestType)
          param.header['X-BizCode'] = window.productSource // 为channel的小写
        }
        if (requestType === 'complaintUpload' || requestType === 'buriedPointBatch') {
          param.body = param.body.data
        }
      } else {
        // console.log('encodeUrl', encodeUrl('formoney', param.url));
        // 需要接口加密的产品: 目前除了nc外，其他的都要加密。
        /*let encodeProducts = ['correctloan', 'formoney', 'nairaloans', 'realcredit', 'xcash', 'xcrosscash'];
        if (encodeProducts.includes(window.productSource)) {
          param.url = encodeUrl(window.productSource, param.url);
        }*/
        let ncInfofromKc = localStorage.getItem('ncInfofromKc');
        if (ncInfofromKc) {
          ncInfofromKc = JSON.parse(ncInfofromKc);
          param.header = {
            ...param.header,
            bizCode: ncInfofromKc.bizCode,
            uid: ncInfofromKc.uid,
            reqChannel: ncInfofromKc.reqChannel
          };
          console.log('param.header', param.header);
        }
      }
      console.debug(`请求路径：${url}------提交参数:${JSON.stringify(param.body)}，请求头:${JSON.stringify(param.header)}`)
      param.body = requestSwitch(param.body);
      if (window.dopplerLib) {
        param.header.business_source = 'from-h5'; // ac的请求
        if (window.dopplerLib.httpsRequestBase64) { // 优先使用base64版本的请求sdk功能。
          // console.log('ac-httpsRequestBase64调用参数', url, JSON.stringify(param.body), JSON.stringify(param.header), callbackFun);
          window.dopplerLib.httpsRequestBase64(url, encode(JSON.stringify(param.body)), JSON.stringify(param.header), callbackFun);
        } else {
          // console.log('ac-httpsRequest调用参数', url, JSON.stringify(param.body), JSON.stringify(param.header), callbackFun);
          window.dopplerLib.httpsRequest(url, JSON.stringify(param.body), JSON.stringify(param.header), callbackFun);
        }
      } else {
        param.header.business_source = 'nc-from-h5'; // nc的请求
        // console.log('nativeRequest', 'httpsRequest', flutterApp, nativeObj, nativeRequest, nativeObjType);
        if ((quickSave && quickSave.httpsRequest) || (nativeObj && nativeObj[nativeFun] && nativeObjType === 'android')) {
          nativeObj[nativeRequest](url, JSON.stringify(param.body), JSON.stringify(param.header), callbackFun);
        } else if (flutterApp || (nativeObj && nativeRequest && nativeObjType === 'flutter')) {
          if (nativeObj && nativeRequest && nativeObjType === 'flutter') {
            // console.log('flutterApp.callHandler');
            if (requestType === 'buriedPointBatch') {
              // 埋点网络请求： 大数据的网络请求比较特殊，flutter ios无法封装处理，要单独添加方法。
              flutterApp.callHandler(nativeMethods['pointRequest'], url, encode(JSON.stringify(param.body)), JSON.stringify(param.header), callbackFun);
            } else {
              // 正常网络请求
              flutterApp.callHandler(nativeRequest, url, encode(JSON.stringify(param.body)), JSON.stringify(param.header), callbackFun);
            }
          } else {
            flutterApp.callHandler('httpsRequest', url, encode(JSON.stringify(param.body)), JSON.stringify(param.header), callbackFun);
          }
        } else if (quickSave && quickSave.messageHandlers) {
          quickSave.messageHandlers.httpsRequest.postMessage(JSON.stringify([callbackFun, url, JSON.stringify(param.body), JSON.stringify(param.header)]))
        }
      }
      console.log('startLog')
      window[callbackFun] = function(result) {
        // console.log(param.url + '返回参数', result);
        let res = {};
        if (window.dopplerLib) {
          if (window.dopplerLib.httpsRequestBase64) { // 优先使用base64版本的请求sdk功能。
            res = JSON.parse(decode(result));
          } else {
            res = JSON.parse(result);
          }
        } else {
          const decodeRes = decode(result);
          try {
              res = JSON.parse(decodeRes);
              res = responseSwitch(res);
          } catch (e) { // 兼容原生报错。
            console.log(e);
            res = decodeRes;
          }
        }
        console.log('special log', res);
        if (requestType === 'buriedPointBatch' || requestType === 'acAppEventOpen'
        || requestType === 'uploadFile' || requestType === 'complaintUpload') {
          if (res.code === 0) {
            console.log(`请求路径1：${url}------提交参数:${JSON.stringify(param.body)}----返回数据：${JSON.stringify(res)}`)
            resolve(res);
          } else {
            console.error(`请求路径1：${url}------提交参数:${JSON.stringify(param.body)}----返回数据：${JSON.stringify(res)}`)
            reject(res);
          }
        } else if (param.url.match(/\/lts\/upload/)) {
          if (res.code === '0') {
            console.log(`请求路径2：${url}------提交参数:${JSON.stringify(param.body)}----返回数据：${JSON.stringify(res)}`)
            resolve(res);
          } else {
            // Toast(res.msg); // 埋点的请求，报错也不处理
            console.error(`请求路径2：${url}------提交参数:${JSON.stringify(param.body)}----返回数据：${JSON.stringify(res)}`)
            reject(res);
          }
        } else {
          if (res.status && (res.status.code === 0)) {
            let newRes = JSON.parse(JSON.stringify(res))
            if (process.env.VUE_APP_ENV == 'stage') {
              // 针对合同处理，不显示合同的打印。过多影响调试
              if (newRes.data && newRes.data.contractHtml) {
                console.log('合同contractHtml过程不打印');
                newRes.data.contractHtml = '';
              }
            }
            console.log(`请求路径3：${url}------提交参数:${JSON.stringify(param.body)}----成功返回数据：${JSON.stringify(newRes)}`)
            if (res.data) {
              resolve(res.data);
            } else {
              resolve();
            }
          } else {
            hideJsLoading();
            // 大于70000000的为客户端的code。
            if (showErrorToast && res.status && res.status.code < 70000000) {
              Toast(res.status.msg);
            } else if (showErrorToast && res.status && res.message) {
              // 其他超时的处理
              Toast(res.message);
            } else if (showErrorToast) {
              Toast(res);
            }
            console.error(`请求路径4：${url}------提交参数:${JSON.stringify(param.body)}----返回数据：${JSON.stringify(res)}`)
            reject(res.status);
          }
        }
      };
    })
  } else {
    if (process.env.NODE_ENV == 'development') { // 目前只支持post请求。
      if (requestType === 'buriedPointBatch') {
        param.body = param.body.data;
      }
      const newBody = requestSwitch(param.body);
      return  new Promise((resolve, reject) => {
        request.http.post(param.url, newBody).then(res => {
          // console.log('afterResponse', param.url, res)
          resolve(res);
        }).catch(e => {
          console.log(e.msg);
          if (requestType !== 'buriedPointBatch' && requestType !== 'acAppEventOpen' && !param.url.match(/\/lts\/upload/)) { // 埋点接口
            if (showErrorToast) {
              Toast(e.msg);
            }
          }
          reject(e);
        });
      })
    } else {
      console.log('调用httpsRequest失败')
    }
  }
}

export {
  nativeObj,
  nativeMethods,
  nativeObjType,
  getAesString,
  existedPin,
  httpRequest,
  setExistedPin,
  getCustId,
  getGpsInfo,
  getLocation,
  getWifiList,
  getDeviceId,
  gotoLoanList,
  gotoHomeActivity,
  activateTradeBigdata,
  activateTradeBigdataAC,
  getNetwork,
  getOsName,
  getDefaultTimezone,
  getOsVersion,
  getCurrentAppVersionName,
  getCurrentAppVersion,
  getUid,
  gotoBindBankAccount,
  gotoBindBankCard,
  applyAppsflyerData,
  addAppsflyerData,
  isPhone,
  getBatchNo,
  callPhone,
  setCalendar,
  gotoGooglePlay,
  openAppStore,
  getChannelId,
  uploadAdsEvent,
  startPage,
  endPage,
  logClickEvent,
  logViewEvent,
  showLoading,
  hideJsLoading,
  log,
  getPhone,
  h5Loading,
  h5HideLoading,
  openFeedBackFile,
  getOsVersionCode,
  getEnCustId,
  getNcInfo,
  gotoBalanceTab,
  getSupportedFeatures,
  openSystemContact,
  startFaceIdentify
}
