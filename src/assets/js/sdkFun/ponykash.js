export default {
  nativeObj: 'ponyKashConnect',
  nativeObjType: 'android',
  commonRequst: 'ponyAnotherNetworkLoading',
  httpsRequest: 'ponyNetworkLoading',
  nativeBackCallback: 'pressedBackCallback',
  activateTradeBigdata: 'collectBigdata', // 完成：方法修改-返回值修改
  log: 'printMessage',
  getAesString: 'getPonyAesMsg',
  setExistedPin: 'setPonyExistedPinMsg',
  setCalendar: 'putRemind',
  addAppsflyerData: 'logAppsflyerEvent',
  callPhone: 'phoneCall',
  openFeedBackFile: 'getPonyFeedFile',
  getGpsInfo: 'getLocationInfo',
  gotoGooglePlay: 'openGooglePlay',
  startPage: 'pageStart',
  endPage: 'pageEnd',
  logClickEvent: 'clickLogEvent',
  logViewEvent: 'viewLogEvent',
  getCurrentAppVersion: 'getAppVersionCode',
  getCurrentAppVersionName: 'getAppVersionName',
  getOsVersion: 'getPonyOsVersion',
  getDefaultTimezone: 'getTimezoneDefault',
  getOsName: 'getNameOs',
  getNetwork: 'getNetworkType',
  getCustId: 'getPonyUserCustId',
  getDeviceId: 'getPhoneDeviceId',
  getUid: 'getPonyUserUid',
  getWifiList: 'getPonyWifiList',
  existedPin: 'ponyExistedPin',
  gotoLoanList: 'openPonyLoanList',
  gotoHomeActivity: 'openPonyHomeActivity',
  getPhone: 'getPonyPhone',
  applyAppsflyerData: 'applyPonyAppsflyerMessage',
  showLoading: 'showLoadingDialog',
  hideJsLoading: 'hideLoadingDialog',
  gotoBindBankAccount: 'openBindBankAcc',
  gotoBindBankCard: 'openBindBankCard',
  getOsVersionCode: 'getOsVersionCode',
  finish: 'exitPony',
  getEnCustId: 'getEnCustId',
  gotoBalanceTab: 'gotoBalanceTab',
  getSupportedFeatures: 'getSupportedFeatures'
}
