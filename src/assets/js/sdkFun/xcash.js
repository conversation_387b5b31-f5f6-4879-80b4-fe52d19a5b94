export default {
  nativeObj: 'quickSave',
  nativeObjType: 'android',
  commonRequst: 'commonRequest',
  httpsRequest: 'httpsRequest',
  nativeBackCallback: 'nativeBackCallback',
  activateTradeBigdata: 'activateTradeBigdata',
  log: 'log',
  getAesString: 'getAesString',
  setExistedPin: 'setExistedPin',
  setCalendar: 'setCalendar',
  addAppsflyerData: 'addAppsflyerData',
  callPhone: 'callPhone',
  openFeedBackFile: 'openFeedBackFile',
  getGpsInfo: 'getGpsInfo',
  gotoGooglePlay: 'gotoGooglePlay',
  startPage: 'startPage',
  endPage: 'endPage',
  logClickEvent: 'logClickEvent',
  logViewEvent: 'logViewEvent',
  getCurrentAppVersion: 'getCurrentAppVersion',
  getCurrentAppVersionName: 'getCurrentAppVersionName',
  getOsVersion: 'getOsVersion',
  getDefaultTimezone: 'getDefaultTimezone',
  getOsName: 'getOsName',
  getNetwork: 'getNetwork',
  getCustId: 'getCustId',
  getDeviceId: 'getDeviceId',
  getUid: 'getUid',
  getWifiList: 'getWifiList',
  existedPin: 'existedPin',
  gotoLoanList: 'gotoLoanList',
  gotoHomeActivity: 'gotoHomeActivity',
  getPhone: 'getPhone',
  applyAppsflyerData: 'applyAppsflyerData',
  showLoading: 'showLoading',
  hideJsLoading: 'hideJsLoading',
  gotoBindBankAccount: 'gotoBindBankAccount',
  gotoBindBankCard: 'gotoBindBankCard',
  getOsVersionCode: 'getOsVersionCode',
  finish: 'finish',
  pointRequest: 'pointRequest',
  getEnCustId: 'getEnCustId',
  gotoBalanceTab: 'gotoBalanceTab',
  getSupportedFeatures: 'getSupportedFeatures',
  openAppStore: 'openAppStore'
}
