import axios from 'axios';
const CryptoJS = require('crypto-js');
import { removeEscapeSequences } from '@/assets/js/common.js';
import { requestSwitch, responseSwitch } from '@/api/paramSwitch';
import { encodeDevObj } from '@/api/encodeDev';

// import { Dialog } from 'vant';
// import { logInfo, redLog, blackLog } from '@/utils'

// if (process.env.NODE_ENV === 'development') {
//   axios.defaults.baseURL = '/';
// } else if (process.env.NODE_ENV === 'production') {
//   axios.defaults.baseURL = '';
// }

// console.log(axios.defaults.baseURL);




const http = axios.create({
  timeout: 1000 * 100, // pcx旧的接口需要设置下，不然会影响调试逻辑。
  withCredentials: true,
  headers: {
    token: '' //目前需要每次从客户端拿token
  }
})
// 请求拦截器
const beforeRequest = config => {
  // 设置 token
  // const token = localStorage.getItem('token')
  // token && (config.headers.Authorization = token)
  // // NOTE  添加自定义头部
  if (process.env.NODE_ENV === 'development') { // 只用于本地调试
    // let blcHeaders = { // newcredit
    //   'token': '87befee875934021afef395ded2f38c2', // 这里默认使用原生的token，调试时，则使用后面部分的token.需要修改token和uid
    //   'csign': 'D6C8EA8CC43A79EC7C18C467B3E3619FFAE364D2',
    //   'countryCode': 'NG',
    //   'app_channelid': 'googleplay',
    //   'X-App-VerName': '1.1.0.18',
    //   'X-App-VerCode': '45',
    //   'uid': '************', // 修改这个
    //   'bizCode': 'newcredit',
    //   'requestId': 'd76d0426-58cf-4afa-afa8-d562765d246b',
    //   'language': 'zh_CN',
    //   'X-Agent-Id': 'Organic',
    //   // 'reqChannel': 'PalmCredit',
    //   'X-Gaid': 'e672ff3c-1e66-487e-9c0c-8ac0faf03ffd',
    //   'business_source': "from-h5",
    //   'type': 'phoneNumber'
    // }
    let blcHeaders = { // palmcredt
      'token': '264c5e5d91264c249307f5b9376847c6', // 这里默认使用原生的token，调试时，则使用后面部分的token.需要修改token和uid
      'csign': '0BBA57A5C46DAB7F387C8607BF03D3A5E2F064B4',
      'countryCode': 'NG',
      'app_channelid': 'googleplay',
      'X-App-VerName': '2.7.7.29_testing(576)',
      'X-App-VerCode': '576',
      'uid': '************', // 修改这个
      'bizCode': 'palmcredit',
      'requestId': 'd76d0426-58cf-4afa-afa8-d562765d246b',
      'language': 'zh_CN',
      'X-Agent-Id': 'Organic',
      'reqChannel': 'PalmCredit',
      'X-Gaid': 'e672ff3c-1e66-487e-9c0c-8ac0faf03ffd',
      'business_source': "from-h5",
      'type': 'phoneNumber'
    }
    let imsCfkHeaders = { // pcx
      'X-TOKEN': '6c1a92f2294846eea465c5143668eab5', // 以下为xcross的请求头
      'wpaurl_name': 'xcross',
      'X-UID': ***********, // 修改这个
      'X-CID': ***********, // 全局搜索替换
      'X-APP-ID': 'Xcross',
      'business_source': "from-h5",
      'csign': '0BBA57A5C46DAB7F387C8607BF03D3A5E2F064B4',
      // 'X-REQUEST-ID': 'f2ef1781-b4f7-4200-a04e-158aa05bf199',
      // 'X-Afmobi-RequestId': 'f2ef1781-b4f7-4200-a04e-158aa05bf199',
      'language': 'zh_CN',
      'countryCode': 'NG',
      'X-CHANNEL': 'googleplay',
      'X-Agent-Id': 'Organic',
      'X-App-VerName': '2.7.7.16_testing(553)',
      'reqChannel': 'Xcash',
      'X-Gaid': 'e672ff3c-1e66-487e-9c0c-8ac0faf03ffd'
    }
    // let imsCfkHeaders = { // pcx
    //   'X-TOKEN': 'b2d715ac2cb84f37895882fc9339b26f', // 以下为xcross的请求头
    //   'wpaurl_name': 'xcross',
    //   'X-UID': 3906190162,
    //   'X-CID': ***********,
    //   'X-APP-ID': 'Xcross',
    //   'business_source': "from-h5",
    //   'csign': '0BBA57A5C46DAB7F387C8607BF03D3A5E2F064B4',
    //   // 'X-REQUEST-ID': 'f2ef1781-b4f7-4200-a04e-158aa05bf199',
    //   // 'X-Afmobi-RequestId': 'f2ef1781-b4f7-4200-a04e-158aa05bf199',
    //   'language': 'zh_CN',
    //   'countryCode': 'NG',
    //   'X-CHANNEL': 'googleplay',
    //   'X-Agent-Id': 'Organic',
    //   'X-App-VerName': '2.7.7.16_testing(553)',
    //   'reqChannel': 'Xcash',
    //   'X-Gaid': 'e672ff3c-1e66-487e-9c0c-8ac0faf03ffd'
    // }
    // let blcHeaders = { // formoney
    //   'token': 'd293af80594140289c2c4127ea2119e0', // 这里默认使用原生的token，调试时，则使用后面部分的token.需要修改token和uid
    //   'csign': 'D6C8EA8CC43A79EC7C18C467B3E3619FFAE364D2',
    //   'countryCode': 'NG',
    //   'app_channelid': 'googleplay',
    //   'X-App-VerName': '1.0.0.6',
    //   'X-App-VerCode': '129',
    //   'uid': '7323648',
    //   'bizCode': 'formoney'
    // }
    // let blcHeaders = { // Allcredit
    // 'X-PackageName': 'com.allcredit.ls',
    // 'auth': 'appKey=ce6804d88d2f73d387c51fee5f99f15fff062e64&appId=newcredit&timestamp=1663999427&sign=0558B30E6CE23196131E6A3B5F0471DB',
    // 'bizCode': 'newcredit',
    // 'app-channelid': 'NewCredit',
    // 'sdk_version_name': '1.1.5.1',
    // 'csign': '0BBA57A5C46DAB7F387C8607BF03D3A5E2F064B4',
    // 'X-Agent-Id': 'AllCredit',
    // 'language': 'zh_CN',
    // 'uid': '1245151057',
    // 'phoneModel': 'Infinix X626',
    // 'requestId': '5bdf6138-84f7-473b-b71a-b478d7f7071c',
    // 'countryCode': 'NG',
    // 'system_version': 'Android:9',
    // 'reqChannel': 'NewCredit',
    // 'X-App-VerCode': '',
    // 'X-Afmobi-RequestId': '5bdf6138-84f7-473b-b71a-b478d7f7071c',
    // 'install_channel': '',
    // 'device_id': '51dd14d39ad460f8bbe71b09a247b4be',
    // 'gaid_key': 'e672ff3c-1e66-487e-9c0c-8ac0faf03ffd',
    // 'os_version': 'Android:9',
    // 'token': '356d9eb6a4c948a2b07e92b3ad6f8cdc',
    // 'sdk_version_code': 46,
    // 'X-App-VerName': '1.0.3.6_testing(33)',
    // 'publish_channel': 'googleplay',
    // }
    if (config.url.match(/blc/g)) {
      config.headers = blcHeaders;
    } else if (config.url.match(/ims-service|cfk-service|bfs-service|aad-service/g)) {
      config.headers = imsCfkHeaders;
    } else {
      config.headers = blcHeaders;
    }
    // 转换请求头
    config.headers = requestSwitch(config.headers);
  }
  // console.log('config', config);
  return config
}

http.interceptors.request.use(beforeRequest)

// 响应拦截器
const responseSuccess = response => {
  const bodyKey = encodeDevObj?.bodyKey;
  if (process.env.NODE_ENV === 'development' && bodyKey) {
    const privateKey = CryptoJS.enc.Utf8.parse(encodeDevObj.privateKey);
    const ivWordArray = CryptoJS.enc.Utf8.parse('0000000000000000');
    if (response.data && response.data[bodyKey]) {
      const encResp = response.data[bodyKey];
      if (encResp.length > 0) {
        try {
          const bytes = CryptoJS.AES.decrypt(encResp.toString(), privateKey, {
            iv: ivWordArray,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
          }).toString(CryptoJS.enc.Utf8)
          // console.log('bytes', bytes);
          if (bytes) {
            // console.log('decryptedData1', removeEscapeSequences(bytes));
            const data = removeEscapeSequences(bytes);
            // 转换参数
            const parseData = responseSwitch(JSON.parse(data));
            if (parseData.status.code === 0) {
              return Promise.resolve(parseData.data);
            } else {
              return Promise.reject(parseData.status);
            }
          }
        } catch (error) {
          console.log('error', error)
        }
      }
    }
  }
  console.log('response', response.data);
  if (response.data.code === "0") {
    return Promise.resolve()
  } else if (response.data.status.code === 0) {
    return Promise.resolve(response.data.data)
  } else {
    // // 强制跳转修改密码
    // Dialog.confirm({
    //   title: '',
    //   className: 'reLoginDailog',
    //   message: response.data && response.data.status && response.data.status.msg,
    //   confirmButtonText: "OK",
    //   showCancelButton: false
    // })
    // .then(() => {
    //   router.push({ path: "/modifyPassword" });
    // })
    // .catch(() => {
    //   // on cancel
    // });
    return Promise.reject(response.data.status)
  }
}

const responseFailed = error => {
  const { response } = error
  if (response) {
    // handleError(response)
    // logInfo(response)
    // cons error = new Error(response.data.msg)
    console.log('fail')
    return Promise.reject({
      code: response.status,
      msg: response.statusText
    })
  } else if (!window.navigator.onLine) {
    // redLog('没有网络')
    return Promise.reject(new Error('请检查网络连接'))
  }
  return Promise.reject(error)
}
http.interceptors.response.use(responseSuccess, responseFailed)

export default {
  http
}
