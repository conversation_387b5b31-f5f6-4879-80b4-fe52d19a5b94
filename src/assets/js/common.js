import Big from 'big.js';

// TODO: 这个底层方法有缺陷，保留两位小数的应该使用thousandsToFixed，该方法原本是用来给整数圆整的。
//千分位转换
export function singleToThousands(num){
  const strSplit = String(num).toString().split(".");
  const integer = strSplit[0].split("");
  integer.reverse();
  const decimal = strSplit[1];
  const newInteger = [];
  for (let i = 0; i < integer.length; i++) {
    if (i % 3 === 0 && i !== 0) {
      newInteger.push(",");
    }
    newInteger.push(integer[i]);
  }
  newInteger.reverse();
  let result = newInteger.join("");
  if (decimal) {
    result += `.${decimal}`;
  }
  return result;
}

// 千分位圆整，保留小数，不进位。
export function singleToThousandsWithoutCarryBit(num){
  const strSplit = String(num).toString().split(".");
  const integer = strSplit[0].split("");
  integer.reverse();
  const newInteger = [];
  for (let i = 0; i < integer.length; i++) {
    if (i % 3 === 0 && i !== 0) {
      newInteger.push(",");
    }
    newInteger.push(integer[i]);
  }
  newInteger.reverse();
  let result = newInteger.join("");
  return result;
}

//千分位转换同时保留两位小数
export function thousandsToFixed(num) {
    return Number(num).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
}

//时间格式转换
export const dateFormat = (date, fmt) => {
  const o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours() % 24 === 0 ? "00" : date.getHours() % 24,
    "H+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds() //毫秒
  };

  const week = {
    0: "/u65e5",
    1: "/u4e00",
    2: "/u4e8c",
    3: "/u4e09",
    4: "/u56db",
    5: "/u4e94",
    6: "/u516d"
  };

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "/u661f/u671f"
          : "/u5468"
        : "") + week[date.getDay() + ""]
    );
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? o[k]
          : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }

  return fmt;
};


/**
 * 处理小数相加问题
 * @param {Number} num1 数字1
 * @param {Number} num2 数字2
*/
export function floatAdd(num1, num2) {
  let x = new Big(num1);
  return x.plus(num2);
}

/**
 * 处理小数减问题
 * @param {Number} num1 数字1
 * @param {Number} num2 数字2
*/
export function floatMinus(num1, num2) {
  let x = new Big(num1);
  return x.minus(num2);
}

  /**
 * 生成32位uuid
*/
export function guid(value) {
  let key = value || 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx';
  return key.replace(/[xy]/g, function(c) {
      var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
      return v.toString(16);
  });
}

  /**
* 生成requestId
*/
export function getRequestId() {
  return guid('xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx');
}

// 获取路径上的参数
export const getQueryString = () => {
  const ref = location.href;
  const obj = {};
  if (ref.indexOf("?") !== -1) {
    const index = ref.indexOf("?");
    const name = ref.substring(index + 1, ref.length)
    const str = name.split("&");
    for (let i = 0; i < str.length; i++) {
      obj[str[i].split("=")[0]] = decodeURI(str[i].split("=")[1])
    }
  }
  // if (process.env.VUE_APP_ENV !== "production") {
  //   obj.product = 'wayacredit';
  // }
  return obj
}

/*判断客户端*/
export function judgeClient() {
  let u = navigator.userAgent;
  let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;   //判断是否是 android终端
  let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);     //判断是否是 iOS终端
  if (isAndroid) {
    return 'Android';
  } else if (isIOS) {
    return 'IOS';
  } else {
    return 'PC';
  }
}

// isIOS
export function isIOS() {
  return judgeClient() === 'IOS';
}

/**
 * 防抖、 delay时间内只能触发一次、
 */
export const debounce = (fn, delay = 1000, start = true) => {
  let done = true;
  let timer = null;
  return function() {
    const _this = this;
    const args = arguments;
    // 首次执行
    if (start) {
      // 开关打开时，执行任务
      if (done) {
        done = false;
        fn.apply(_this, args);
      }
      // 清空上一次操作
      clearTimeout(timer);
      // 任务开关打开
      timer = setTimeout(function() {
        done = true;
      }, delay);
    } else {
      // 清空上一次操作
      clearTimeout(timer);
      // delay时间之后，任务执行
      timer = setTimeout(function() {
        fn.apply(_this, args);
      }, delay);
    }
  };
};


/**
* 判断是否有过期, 若过期则移除
* @param {String} flag 缓存标识
* @param {Number} time 时间（小时）
*/
export const inTimeAreaCal = (flag, time) => {
 const flagTime = localStorage.getItem(flag);
 const expiredTime = new Date().getTime() - 60 * 60 * 1000 * time;
 // 超出时间后移除
 if (flagTime > 0 && flagTime < expiredTime) {
   localStorage.removeItem(flag);
   return false;
 }
 if (flagTime > 0 && flagTime > expiredTime) {
   return true;
 }
 return false;
}

// 递归替换对象或者数组中的对象的key值
export const replaceKeys = function(obj, keyMap) {
  // 一个递归函数来遍历和替换键
  function traverseAndReplace(item) {
    if (Array.isArray(item)) {
      // 如果是数组，递归其每个元素
      return item.map(traverseAndReplace);
    } else if (item !== null && typeof item === 'object') {
      // 如果是对象，遍历其键
      const newItem = {};
      for (const key in item) {
        // 如果映射中有这个键，则使用映射中的键
        const newKey = keyMap[key] || key;
        // 递归处理值
        newItem[newKey] = traverseAndReplace(item[key]);
      }
      return newItem;
    }
    // 如果不是对象或数组，直接返回值
    return item;
  }

  // 使用递归函数处理输入对象
  return traverseAndReplace(obj);
}

// 删除无效空格
export const removeEscapeSequences = (text) => {
  return text.replace(/[^ -~]+/g, '');
}

//  反转一个对象的键和值
export const invertObject = function (obj) {
  const inverted = {};
  for (const key in obj) {
    // 检查属性是否是对象自身的属性而不是继承的属性
    if (obj[key]) {
      // 将原始对象的键作为值，将原始对象的值作为键
      inverted[obj[key]] = key;
    }
  }
  return inverted;
}
