$themeColor: var(--themeColor, #02B17B);
$lightColor: var(--lightColor, rgba(2, 177,123,0.05));
$color: var(--color, #02B17B);
$background: var(--background, #02B17B);
$rangeBackground: var(--rangeBackground, #224182);

body {
  background: #ffffff;
}

.px-10 {
	font-size: 12px;
  transform: scale(0.84);
}

.toast-over-write{
  z-index: 10000 !important;
}
body {
  font-size:13px;
  font-size-adjust:none;
  font-stretch:normal;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  /*line-height:1.4;*/
  text-align:center;
  }
  body, ul, ol, dl, dd, h1, h2, h3, h4, h5, h6, p, form, fieldset, legend, input, textarea, select, button, th, td {
  margin:0;
  padding:0;
  }
  h1, h2, h3, h4, h5, h6 {
  font-size:100%;
  font-weight:normal;
  }
  table {
  font-size:inherit;
  }
  input, select {
  font-size:100%;
  font-size-adjust:none;
  font-stretch:normal;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:normal;
  }
  button {
  overflow:visible;
  }
  th, em, strong, b, address, cite {
  font-style:normal;
  font-weight:normal;
  }
  li {
  list-style-image:none;
  list-style-position:outside;
  list-style-type:none;
  }
  img, fieldset {
  border:0 none;
  }
  ins {
  text-decoration:none;
  }
  a{
      color: #1b3155
  }

.van-toast--text {
    word-break: normal;
}

.error_input_border input {
  border: 1px solid #E84E40 !important;
}

.error_input_border .cancleTipClass input {
  border: 1px solid transparent !important;
}

.error_input_border .field-textarea textarea {
  border: 1px solid #E84E40 !important;
}

.error_input_border .field {
  border: 1px solid #e84e40;
}
.cancle_error_input_border.field{
  border-bottom: 0.028rem solid #ccc;
}
.cancle_error_input_border.field-textarea textarea {
  border: 1px solid #C4CAD5 !important;
}
.cancle_error_input_border.field-number input {
  border: 1px solid #C4CAD5 !important;
}
