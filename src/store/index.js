import Vue from 'vue'
import Vuex from 'vuex'
import { httpRequest, logClickEvent, logViewEvent, startPage } from "@/assets/js/native";
import { getRequestId } from "@/assets/js/common";
import api from "@/api/interface";


Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    productSource: '',
    themeColor: '',
    deviceType: '',
    statusObj: {
      N: {
        name: 'Outstanding'
      },
      O: {
        name: 'Overdue'
      },
      F: {
        name: 'Paid'
      },
      U: {
        name: 'Distursing'
      },
      P: {
        name: 'Pending'
      },
      C: {
        name: 'Cancel'
      },
      A: {
        name: 'Under Review'
      }
    },
    channel: process.env.VUE_APP_CHANNEL || '',
    channelPackageName: process.env.VUE_APP_CHANNEL_PACKAGE_NAME || '',
    nodeCode: { // 节点码(日历变更时提交)
      CALENDAR_NODE_R: 'R', // 注册
      CALENDAR_NODE_B: 'B' , // BVN通过
      CALENDAR_NODE_C: 'C' , // 授信通过
      CALENDAR_NODE_W: 'W' , // 提现成功
      CALENDAR_NODE_RP: 'RP' , // 还款成功
    },
    contract: '',
    bank: {}, // 银行
    coupon: {}, // 提现选择的优惠券
    repayCoupon: {}, // 还款选择的优惠券
    repayAndLoanStatus: false, // 还款再借是否成功。
    uid: '',
    userInfor: '', // 用户信息
    addHeader: {}, // 新增的请求头
    acWithdrawSubmitData: '', // 来源于ac的提现后上报的数据
    repaySchduleList: [], // 还款计划
    notComplaintArr: ['ponykash', 'wayacredit', 'truemoneyios', 'creditwiseios'], // 暂无客诉功能的包
    supportedFeatures: {}, // 客户端支持功能列表，用于新增原生能力的判断
    uploadDocuments: {
      bankAccountBalance: [
        // {
        //   id: '',
        //   img: 'http://gips2.baidu.com/it/u=*********,**********&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960'
        // }
      ], // 银行账户余额
      salaryFlow: [], // 近三个月银行账户工资发放流水
      photoOfWorkBadge: [], // 工作证明
      salaryPayslip: [
        // {
        //   id: '',
        //   img: 'http://gips2.baidu.com/it/u=*********,**********&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960'
        // }
      ] // 工资单
    }, // 缓存上传图片的信息
    needReCreditContractList: '', // 是否需要更新紧急联系人信息
    needReCreditliveDetection: '', // 是否需要更新人脸
    checkDeviceStage: '', // 设备检查状态 N:正常流程,UF:人脸,OTP:短信验证,P:通过
    checkDeviceStageResult: '', // 设备检查状态结果 Y: 验证通过 N: 不通过
  },
  getters: {
  },
  mutations: {
    SET_CONTRACT: (state, contract) => {
      state.contract = contract;
    },
    SET_BANK: (state, bank) => {
      state.bank = bank;
    },
    SET_COUPON: (state, coupon) => {
      state.coupon = coupon;
    },
    SET_REPAYCOUPON: (state, coupon) => {
      state.repayCoupon = coupon;
    },
    SET_REPAYANDLOANSTATUS: (state, repayAndLoanStatus) => {
      state.repayAndLoanStatus = repayAndLoanStatus;
    },
    SET_UID: (state, uid) => {
      state.uid = uid;
    },
    SET_PRODUCTSOURCE: (state, productSource) => {
      state.productSource = productSource;
    },
    SET_THEMECOLOR: (state, themeColor) => {
      state.themeColor = themeColor;
    },
    SET_DEVICETYPE: (state, deviceType) => {
      state.deviceType = deviceType;
    },
    SET_USERINFOR: (state, userInfor) => {
      state.userInfor = userInfor;
    },
    SET_ADDHEADER: (state, addHeader) => {
      state.addHeader = addHeader;
    },
    SET_ACWITHDRAWSUBMITDATA: (state, acWithdrawSubmitData) => {
      state.acWithdrawSubmitData = acWithdrawSubmitData;
    },
    SET_REPAYSCHDULELIST: (state, repaySchduleList) => {
      state.repaySchduleList = repaySchduleList;
    },
    SET_SUPPORTEDFEATURES: (state, supportedFeatures) => {
      state.supportedFeatures = supportedFeatures;
    },
    SET_UPLOADDOCUMENTS: (state, obj) => {
      // 触发整个对象的更新
      let uploadDocuments = Object.assign({}, state.uploadDocuments);
      uploadDocuments[obj.key] = obj.value;
      state.uploadDocuments = uploadDocuments;
    },
    SET_NEEDRECREDITCONTRACTLIST: (state, needReCreditContractList) => {
      state.needReCreditContractList = needReCreditContractList;
    },
    SET_NEEDRECREDITLIVEDETECTION: (state, needReCreditliveDetection) => {
      state.needReCreditliveDetection = needReCreditliveDetection;
    },
    SET_CHECKDEVICESTAGE : (state, checkDeviceStage) => {
      state.checkDeviceStage = checkDeviceStage;
    },
    SET_CHECKDEVICESTAGERESULT: (state, checkDeviceStageResult) => {
      state.checkDeviceStageResult = checkDeviceStageResult;
    },
  },
  actions: {
    //埋点上报
    reportEvent({state}, { page, eventName, eventData }) {
      if (state.deviceType !== 'AC') {
        const uid = state.uid;
        let channel = state.channel || '';
        const data = {
          data: [{
            uid: uid ? uid.toString() : '',
            channel: channel,
            event_page: page,
            event_name: eventName,
            event_data: eventData
          }]
        }
        // 因埋点上报太多，代理请求过多影响了正常的业务请求，严重影响开发效率，故屏蔽开发时埋点上报，改为打印。
        if (process.env.VUE_APP_ENV !== 'development') {
          httpRequest(api.bigDataUrl.buriedPointBatch, {
            addHeader: {
              requestType: 'buriedPointBatch'
            },
            data: data
          })
          .catch(e => {
            //前端不需要关心埋点上报是否成功失败
            //埋点接口不能影响到正常业务进行，所以此处捕获异常并且不做任何处理
            console.log('埋点接口 err msg', e)
          })
          if (eventName.includes('click')) {
            logClickEvent(page, eventName);
          } else if (eventName.includes('page_view')) {
            startPage(page);
          } else {
            logViewEvent(page, eventName);
          }
        } else {
          console.log(`%c埋点上报：${eventName}`, 'color: #b3b222', data)
        }
      }
    },
    // AC埋点上报
    acReportEvent({state}, { page, eventName, param, eventData }) {
      const uid = state.uid;
      const channel = state.productSource || 'newcredit';
      const data = {
        uid: uid.toString(),
        event_id: getRequestId(),
        trace_id: getRequestId(),
        event_type: page,
        event_name: eventName,
        event_time: Date.parse(new Date()), // 客户端本地时间戳，毫秒
        arg1: channel,
        arg2: param.arg1 || '',
        arg3: param.arg2 || '',
        event_data: eventData || {}
      }
      httpRequest('/app/event/open', {
        data: data,
        addHeader: {
          requestType: 'acAppEventOpen'
        }
      })
        .catch(e => {
          //前端不需要关心埋点上报是否成功失败
          //埋点接口不能影响到正常业务进行，所以此处捕获异常并且不做任何处理
          console.log('/app/event/open', e)
        })
    }
  },
  modules: {
  }
})
