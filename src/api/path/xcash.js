/**
 * 开发环境使用的地址配置
 *
 */

let bigDataHttp = process.env.VUE_APP_BIGDATA_API_MAP_URL;
let httpBlc = process.env.VUE_APP_API_MAP_URL;

if (process.env.NODE_ENV === 'development') {
  bigDataHttp = '/wqqe'
  httpBlc = '/cbjg'
}

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `${bigDataHttp}/schnorkel/silicane/fracticipita`, // 大数据埋点
      uploadFile: `${bigDataHttp}/aphydrotropic/uninvoked`, // 上传附件
      complaintUpload: `${bigDataHttp}/storage/asciferous/pakeha`, // 客诉提交
      //creditInvestigationUpload: `${bigDataHttp}/mirage/lymphopoietic/splenocyte`, // 增信提交
      creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload_v2`, // 增信提交
      getUrls: `${bigDataHttp}/squamelliferous/unalienable` // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: `${httpBlc}/commensurability/aggravator`,
      queryCouponUsageRecord4nc: `${httpBlc}/ephemerid/repuff/sunny`,
      createCoupon: `${httpBlc}/ironbound/valencianite`
    },
    repayment: {
      activeQueryPlutusPaymentResult: `${httpBlc}/knawel/upcurl/rearrest`,
      queryLoanDetils4nc: `${httpBlc}/mineworker/caffetannic/chrononomy`,
      getContractPath4nc: `${httpBlc}/magistratic/sheminith/endocyst`,
      queryPassAmount: `${httpBlc}/cimicid/nemertini/blindfold`,
      loanAgainGenMultiOrder: `${httpBlc}/esperantist/oratorially`,
      cancelLoanPreStatus: `${httpBlc}/squillid/pantothenate`,
      generatePlutusWebOrder4nc: `${httpBlc}/scotch/coxarthritis/schedular`,
      queryRepayPlanDetailList: `${httpBlc}/adiposis/tale/retaliation`,
      queryCustRepayInfo: `${httpBlc}/radiodynamics/marketstead`,
      submitRepayRequestion: `${httpBlc}/triptote/dioscorein/nataraja`,
      hasOnlineLoan: `${httpBlc}/idealess/header`, // 获取是否有在途
      popAPPConfig: `${httpBlc}/farmhouse/woodkern` // 获取还款页面配置
    },
    withdraw: {
      getValidCode4nc: `${httpBlc}/subsimilation/semimajor`,
      checkValidCodeBVN4nc: `${httpBlc}/interosseal/churchdom`,
      queryNoBindCardInfo: `${httpBlc}/goddaughter/leucocidin/arrearage`, // 获取用户预提现订单信息
      queryCustomerBankCardList4nc: `${httpBlc}/rhizogenous/acatamathesia`,
      queryWithdrawAcctInfoCutoverSelect4nc: `${httpBlc}/profarmer/isomorphous/fleabite`,
      loanApply4merge: `${httpBlc}/mermis/erythrocatalysis`,
      loanApplyNoBindCardInit: `${httpBlc}/scopulipedes/penalist/disimpassioned`,
      queryCustCardInfo: `${httpBlc}/ladleful/partialistic`,
      queryCustProductInfo: `${httpBlc}/sparklingness/epidermic`,
      setTransactionPassword4nc: `${httpBlc}/decomposition/formalism`,
      dropLoanApplyCase: `${httpBlc}/overtax/incautious/cylindroconoidal`
    },
    commonBlc: {
      queryStageInfo4nc: `${httpBlc}/superindividualism/unprejudged`,
      queryLoanList4nc: `${httpBlc}/unswivel/retransfuse/encapsulation`,
      queryCustFinancialStatus4nc: `${httpBlc}/pennaceous/melastomad`,
      routeRedirect: `${httpBlc}/scouther/playwoman`,
      starsPopup: `${httpBlc}/highlandish/unsociableness`,
      saveStarsScore: `${httpBlc}/pupivorous/pulmoniferous/tournefortia`,
      loanCalculate4nc: `${httpBlc}/spondylous/irenical`,
      getSysDate4nc: `${httpBlc}/precontrive/megacycle`,
      getUserType: `${httpBlc}/sawman/sudary`, // 用户类型
      queryActivityInfo: `${httpBlc}/elkhound/propensity/afghan`, // 活动信息接口（目前只有PC/NC使用。新包使用需要映射）
      queryLoanApplyAuthority: `${httpBlc}/rhizinaceae/dissimilarly/proslaveryism`, // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: `${httpBlc}/mismarry/centauric`, // 验证nin
      applyLoanCaptchaSend: `${httpBlc}/cardiolysis/ephemeris`, // 发送otp
      increaseCredit: `${httpBlc}/se/lecanine`, // 提交增信资料触发增信
      queryDict: `${httpBlc}/arabism/dextrorsely/judaization`, // 字典接口
      deviceVerification: `${httpBlc}/gorfly/monotelephonic/antisupernaturalism` // 设备id检测验证
    },
    bankAccount: {
      verifyBankAccount4nc: `${httpBlc}/sottish/rhenium`,
      addBankAccount4nc: `${httpBlc}/bilifaction/preceptoral/quadrimetallic`,
      queryBankList4nc: `${httpBlc}/opencast/nonextensile/pentactinal`,
      queryPlutusBindBankCardStatus: `${httpBlc}/saltwort/mudee/homeotype`
    },
    profileInfo: {
      saveContactList: `${httpBlc}/theatricism/overplease/haustorial` // 更新紧急联系人信息
    }
  }
}
