/**
 * 开发环境使用的地址配置
 *
*/

let bigDataHttp = process.env.VUE_APP_BIGDATA_API_MAP_URL;
let httpBlc = process.env.VUE_APP_API_MAP_URL;

if (process.env.NODE_ENV === 'development') {
  bigDataHttp = '/aflf'
  httpBlc = '/mphr'
}

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `${bigDataHttp}/unmisguided/gambier`, // 大数据埋点
      uploadFile: `${bigDataHttp}/quadricuspid/yerb`, // 上传附件
      complaintUpload: `${bigDataHttp}/botchery/dor`, // 客诉提交
      //creditInvestigationUpload: `${bigDataHttp}/compoundedness/apolar`, // 增信提交
      creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload_v2`, // 增信提交
      getUrls: `${bigDataHttp}/marsupialize/rushiness` // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: `${httpBlc}/flatly/mesogastrium/notifyee`,
      queryCouponUsageRecord4nc: `${httpBlc}/epiphytotic/discontinuity`,
      createCoupon: `${httpBlc}/epanisognathous/skoo/entozoal`
    },
    repayment: {
      activeQueryPlutusPaymentResult: `${httpBlc}/margarosanite/regionalism/sulfamate`,
      queryLoanDetils4nc: `${httpBlc}/filching/permittedly/patrology`,
      getContractPath4nc: `${httpBlc}/nazaritism/fourer/wheep`,
      queryPassAmount: `${httpBlc}/glaciaria/joanne/docimology`,
      loanAgainGenMultiOrder: `${httpBlc}/homoeotopy/overfastidiously/snoutish`,
      cancelLoanPreStatus: `${httpBlc}/clem/lymnaean`,
      generatePlutusWebOrder4nc: `${httpBlc}/shamable/orohippus`,
      queryRepayPlanDetailList: `${httpBlc}/fixture/sulphureously/adenological`,
      queryCustRepayInfo: `${httpBlc}/tailage/breast`,
      submitRepayRequestion: `${httpBlc}/hinderer/regrinder`,
      hasOnlineLoan: `${httpBlc}/evener/prestabilism`, // 获取是否有在途
      popAPPConfig: `${httpBlc}/sitosterin/liquidator` // 获取还款页面配置
    },
    withdraw: {
      getValidCode4nc: `${httpBlc}/trimargarate/wainful/aizoaceous`,
      checkValidCodeBVN4nc: `${httpBlc}/quadrangular/zante/axonophorous`,
      queryNoBindCardInfo: `${httpBlc}/invitement/comic`, // 获取用户预提现订单信息
      queryCustomerBankCardList4nc: `${httpBlc}/shillibeer/bouncer`,
      queryWithdrawAcctInfoCutoverSelect4nc: `${httpBlc}/hadramautian/sprayless`,
      loanApply4merge: `${httpBlc}/daddynut/hypersusceptible/beakful`,
      loanApplyNoBindCardInit: `${httpBlc}/balneatory/scabrously`,
      queryCustCardInfo: `${httpBlc}/triangulately/brahmanist/giftware`,
      queryCustProductInfo: `${httpBlc}/plume/bimester/plagiostome`,
      setTransactionPassword4nc: `${httpBlc}/hypotonus/yen`,
      dropLoanApplyCase: `${httpBlc}/vaginervose/overcaptiously/attritive`
    },
    commonBlc: {
      queryStageInfo4nc: `${httpBlc}/neodamode/imprudently`,
      queryLoanList4nc: `${httpBlc}/superacetate/canton`,
      queryCustFinancialStatus4nc: `${httpBlc}/unoil/azalea`,
      routeRedirect: `${httpBlc}/cystopyelonephritis/uninspiring/scyphomedusae`,
      starsPopup: `${httpBlc}/nonburning/tetramerism/albescence`,
      saveStarsScore: `${httpBlc}/sticks/seppuku`,
      loanCalculate4nc: `${httpBlc}/forche/terfezia/colchicine`,
      getSysDate4nc: `${httpBlc}/jet/hanukkah/viscacha`,
      getUserType: `${httpBlc}/omened/scannable/legibleness`, // 用户类型
      queryActivityInfo: `${httpBlc}/turkle/graminicolous`, // 活动信息接口（目前只有PC/NC使用。新包使用需要映射）
      queryLoanApplyAuthority: `${httpBlc}/pachymeter/misjoinder/gujar`, // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: `${httpBlc}/analeptical/concertedly`, // 验证nin
      applyLoanCaptchaSend: `${httpBlc}/unprincipledness/gunyang/sketchiness`, // 发送otp
      increaseCredit: `${httpBlc}/schooner/realistic`, // 提交增信资料触发增信
      queryDict: `${httpBlc}/nonmischievous/masai/tilletia`, // 字典接口
      deviceVerification: `${httpBlc}/staphylomycosis/preguide` // 设备id检测验证
    },
    bankAccount: {
      verifyBankAccount4nc: `${httpBlc}/protobranchiata/unquakerlike/gumbo`,
      addBankAccount4nc: `${httpBlc}/tabetless/toty/gluttonish`,
      queryBankList4nc: `${httpBlc}/ogcocephalidae/palewise`,
      queryPlutusBindBankCardStatus: `${httpBlc}/heterophyte/hilding`
    },
    profileInfo: {
      saveContactList: `${httpBlc}/temporization/furcation` // 更新紧急联系人信息
    }
  }
}
