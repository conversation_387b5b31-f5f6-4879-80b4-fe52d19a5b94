/**
 * 生产环境使用的地址配置
 *
 */

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `/api/wayacredit/belgacom/biologia`, // 大数据埋点
      uploadFile: '/api/wayacredit/mood/laborer', // 上传附件
      complaintUpload: '/api/wayacredit/consider/candidate', // 客诉提交
      //creditInvestigationUpload: '/api/wayacredit/principle/pick', // 增信提交
      creditInvestigationUpload: '/api/wayacredit/dtraaieher/geez', // 增信提交
      getUrls: '/api/wayacredit/dinner/beard' // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: '/user/loan/coupon/coupon-list',   // '/blc-service/coupon/v1/queryCouponList4nc',
      queryCouponUsageRecord4nc:'/user/loan/query/coupon-usage-record',   // '/blc-service/coupon/v1/queryCouponUsageRecord4nc'
      createCoupon: '/user/coupon/v1/getNewCoupon' // '/blc-service/coupon/v1/createCoupon'
    },
    repayment: {
      activeQueryPlutusPaymentResult:'/user/loan/plutus/payment-result',   //  '/blc-service/plutus/v1/activeQueryPlutusPaymentResult',
      queryLoanDetils4nc:'/user/loan/detail',   // '/blc-service/query/v1/queryLoanDetils4nc',
      getContractPath4nc:'/user/loan/contractUrl',   // '/blc-service/query/v1/getContractPath4nc',
      queryPassAmount: '/user/query/pass-amount',   //'/blc-service/query/v1/queryPassAmount',
      loanAgainGenMultiOrder: '/user/loan/again-generate-mult-order',   //'/blc-service/apply/v1/loanAgainGenMultiOrder',
      cancelLoanPreStatus: '/user/preloan/cancel-preloan',   //'/blc-service/apply/v1/cancelLoanPreStatus',
      generatePlutusWebOrder4nc:'/user/loan/plutus/gen/repay-order',   // '/blc-service/plutus/v1/generatePlutusWebOrder4nc',
      queryRepayPlanDetailList:'/user/loan/query/repay-plan-detail-list',   //  '/blc-service/query/v1/queryRepayPlanDetailList',
      queryCustRepayInfo:'/user/loan/query/repay-info',   // '/blc-service/cust/queryCustRepayInfo',
      submitRepayRequestion: '/user/loan/submit/repay/requestion',   //'/blc-service/user/v1/submitRepayRequestion'
      hasOnlineLoan: '/user/html/num1/hasRepaying' // 获取是否有在途
    },
    withdraw: {
      getValidCode4nc:'/common/getCode',   //  '/blc-service/user/v1/getValidCode4nc',
      checkValidCodeBVN4nc: '/common/check-valid-code-bvn',   // '/blc-service/user/v1/checkValidCodeBVN4nc',
      queryNoBindCardInfo:'/user/loan/submit/repay/requestion', // '/blc-service/apply/v1/queryNoBindCardInfo',
      queryCustomerBankCardList4nc:'/user/card/query/customer/cardList',   //  '/blc-service/query/v1/queryCustomerBankCardList4nc',
      queryWithdrawAcctInfoCutoverSelect4nc:'/user/query/withdraw-account-info-customer-select',   //  '/blc-service/query/v1/queryWithdrawAcctInfoCutoverSelect4nc',
      loanApply4merge:'/user/loan/new-apply',   // '/blc-service/cust/loanApply4merge',
      loanApplyNoBindCardInit:'/user/loan/apply-init-info', // '/blc-service/apply/v1/loanApplyNoBindCardInit',
      queryCustCardInfo:'/user/card/repay-card-info', // '/blc-service/cust/queryCustCardInfo',
      queryCustProductInfo: '/user/query/product-info', //'/blc-service/cust/queryCustProductInfo',
      setTransactionPassword4nc:'/user/info/reload/password',   // '/blc-service/user/v1/setTransactionPassword4nc',
      dropLoanApplyCase:'/user/info/drop-loan-apply-case'   // '/blc-service/user/v1/dropLoanApplyCase'
    },
    commonBlc: {
      queryStageInfo4nc:'/user/info/stage-info',   //  '/blc-service/query/v1/queryStageInfo4nc',
      queryLoanList4nc:'/user/loan/list',   // '/blc-service/query/v1/queryLoanList4nc',
      queryCustFinancialStatus4nc:'/user/loan/query/finanical-status',   //  '/blc-service/query/v1/queryCustFinancialStatus4nc',
      routeRedirect:'/user/query/bind-card-plutus-url',   // '/blc-service/user/v1/routeRedirect',
      starsPopup:'/user/query/stars-popup',   // '/blc-service/inner/v1/starsPopup',
      saveStarsScore:'/user/save/stars-score',   //  '/blc-service/inner/v1/saveStarsScore',
      loanCalculate4nc:'/user/loan/calculate',   //  '/blc-service/apply/v1/loanCalculate4nc',
      getSysDate4nc:'/common/sys-date',   //  '/blc-service/query/v1/getSysDate4nc',
      getUserType:'/user/query/user-type',   // '/blc-service/user/v1/getUserType'
      queryLoanApplyAuthority: '/user/query/loan-apply-authority', // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: '/common/subscribe/bank/nin' // 验证nin
    },
    bankAccount: {
      verifyBankAccount4nc: '/user/info/verify-account',   //'/blc-service/user/v1/verifyBankAccount4nc',
      addBankAccount4nc:'/user/bank/add-bank',   //  '/blc-service/apply/v1/addBankAccount4nc',
      queryBankList4nc:'/user/bank/bankList',   //    '/blc-service/query/v1/queryBankList4nc',
      queryPlutusBindBankCardStatus:'/user/bank/card/bind-status' //  '/blc-service/user/v1/queryPlutusBindBankCardStatus'
    }
  }
}
