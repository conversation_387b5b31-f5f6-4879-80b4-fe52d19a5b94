export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `/api/creditwiseios/epub/rmon`, // 大数据埋点
      uploadFile: '/api/creditwiseios/fib/pcnation', // 上传附件
      complaintUpload: '/api/creditwiseios/dbase/hobbies', // 客诉提交
      //creditInvestigationUpload: '/api/creditwiseios/tycoon/growth', // 增信提交
      creditInvestigationUpload: '/api/creditwiseios/neiesvur/fotrci', // 增信提交
      getUrls: '/api/creditwiseios/key/snack' // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: '/user/advance/advertisement-coupon/diminish-picture-list',   // '/blc-service/coupon/v1/queryCouponList4nc',
      queryCouponUsageRecord4nc:'/user/advance/query/diminish-picture-status-record',   // '/blc-service/coupon/v1/queryCouponUsageRecord4nc'
      createCoupon: '/user/get/coupon' // '/blc-service/coupon/v1/createCoupon'
    },
    repayment: {
      activeQueryPlutusPaymentResult:'/user/advance/middleground/payment-result',   //  '/blc-service/plutus/v1/activeQueryPlutusPaymentResult',
      queryLoanDetils4nc:'/user/advance/detail',   // '/blc-service/query/v1/queryLoanDetils4nc',
      getContractPath4nc:'/user/advance/communication-content',   // '/blc-service/query/v1/getContractPath4nc',
      queryPassAmount: '/user/query/pazz-money',   //'/blc-service/query/v1/queryPassAmount',
      loanAgainGenMultiOrder: '/user/advance/again-engender-mult-order',   //'/blc-service/apply/v1/loanAgainGenMultiOrder',
      cancelLoanPreStatus: '/user/preloan/cancel-preloan',   //'/blc-service/apply/v1/cancelLoanPreStatus',
      generatePlutusWebOrder4nc:'/user/advance/middle/gen/repayment-order',   // '/blc-service/plutus/v1/generatePlutusWebOrder4nc',
      queryRepayPlanDetailList:'/user/advance/query/repayment-game-detail-list',   //  '/blc-service/query/v1/queryRepayPlanDetailList',
      queryCustRepayInfo:'/user/advance/query/repayment-info',   // '/blc-service/cust/queryCustRepayInfo',
      submitRepayRequestion: '/user/advance/send/repayment/requestion',   //'/blc-service/user/v1/submitRepayRequestion'
      hasOnlineLoan: '/user/html/version1/isLoaning' // 获取是否有在途
    },
    withdraw: {
      getValidCode4nc:'/common/give-correct-number',   //  '/blc-service/user/v1/getValidCode4nc',
      checkValidCodeBVN4nc: '/common/restrain-correct-number-autonomous-auth',   // '/blc-service/user/v1/checkValidCodeBVN4nc',
      queryNoBindCardInfo:'/user/advance/send/repayment/requestion', // '/blc-service/apply/v1/queryNoBindCardInfo',
      queryCustomerBankCardList4nc:'/user/picture/query/client/cant-picture-list',   //  '/blc-service/query/v1/queryCustomerBankCardList4nc',
      queryWithdrawAcctInfoCutoverSelect4nc:'/user/query/makeMoney-clarify-info-client-select',   //  '/blc-service/query/v1/queryWithdrawAcctInfoCutoverSelect4nc',
      loanApply4merge:'/user/advance/new-apply',   // '/blc-service/cust/loanApply4merge',
      loanApplyNoBindCardInit:'/user/advance/apply-init-info', // '/blc-service/apply/v1/loanApplyNoBindCardInit',
      queryCustCardInfo:'/user/picture/repayment-picture-info', // '/blc-service/cust/queryCustCardInfo',
      queryCustProductInfo: '/user/query/article-info', //'/blc-service/cust/queryCustProductInfo',
      setTransactionPassword4nc:'/user/userData/restart/private-key',   // '/blc-service/user/v1/setTransactionPassword4nc',
      dropLoanApplyCase:'/user/userData/drop-advance-apply-case'   // '/blc-service/user/v1/dropLoanApplyCase'
    },
    commonBlc: {
      queryStageInfo4nc:'/user/userData/degree-info',   //  '/blc-service/query/v1/queryStageInfo4nc',
      queryLoanList4nc:'/user/advance/list',   // '/blc-service/query/v1/queryLoanList4nc',
      queryCustFinancialStatus4nc:'/user/advance/query/finanical-status',   //  '/blc-service/query/v1/queryCustFinancialStatus4nc',
      routeRedirect:'/user/query/bind-picture-middleground-url',   // '/blc-service/user/v1/routeRedirect',
      starsPopup:'/user/query/evaluate-app',   // '/blc-service/inner/v1/starsPopup',
      saveStarsScore:'/user/maintain/stars-score',   //  '/blc-service/inner/v1/saveStarsScore',
      loanCalculate4nc:'/user/advance/compute-game-info',   //  '/blc-service/apply/v1/loanCalculate4nc',
      getSysDate4nc:'/common/organisation-date',   //  '/blc-service/query/v1/getSysDate4nc',
      getUserType:'/user/query/user-case',   // '/blc-service/user/v1/getUserType'
      queryLoanApplyAuthority: '/user/query/advance-apply-authorityinfo', // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: '/user/check/bank/nin' // 验证nin
    },
    bankAccount: {
      verifyBankAccount4nc: '/user/userData/acknowledgment-clarify',   //'/blc-service/user/v1/verifyBankAccount4nc',
      addBankAccount4nc:'/user/cant/plus-cant',   //  '/blc-service/apply/v1/addBankAccount4nc',
      queryBankList4nc:'/user/cant/cant-clarify-list',   //    '/blc-service/query/v1/queryBankList4nc',
      queryPlutusBindBankCardStatus:'/user/cant/picture/bind-status' //  '/blc-service/user/v1/queryPlutusBindBankCardStatus'
    }
  }
}
