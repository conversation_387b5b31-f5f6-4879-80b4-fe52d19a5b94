/**
 * 开发环境使用的地址配置
 *
 */

let bigDataHttp = process.env.VUE_APP_BIGDATA_API_MAP_URL;
let httpBlc = process.env.VUE_APP_API_MAP_URL;

if (process.env.NODE_ENV === 'development') {
  bigDataHttp = '/cqk'
  httpBlc = '/mjox'
}

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `${bigDataHttp}/encephalotomy/juvenileness`, // 大数据埋点
      uploadFile: `${bigDataHttp}/crochet/foraneen`, // 上传附件
      complaintUpload: `${bigDataHttp}/present/flurr`, // 客诉提交
      //creditInvestigationUpload: `${bigDataHttp}/supersecret/alluring`, // 增信提交
      creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload_v2`, // 增信提交
      getUrls: `${bigDataHttp}/muchness/unphilanthropically/explainable` // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: `${httpBlc}/tamzine/cheeseburger`,
      queryCouponUsageRecord4nc: `${httpBlc}/lyencephala/collins`,
      createCoupon: `${httpBlc}/protrudable/sycophancy/ctenocyst`
    },
    repayment: {
      activeQueryPlutusPaymentResult: `${httpBlc}/nephrocystosis/deerweed/tramp`,
      queryLoanDetils4nc: `${httpBlc}/duro/hexokinase/sarra`,
      getContractPath4nc: `${httpBlc}/paratomium/greenbackism/bejant`,
      queryPassAmount: `${httpBlc}/torridity/fullish`,
      loanAgainGenMultiOrder: `${httpBlc}/actian/rameses`,
      cancelLoanPreStatus: `${httpBlc}/military/downweight/turlough`,
      generatePlutusWebOrder4nc: `${httpBlc}/sulphurization/slimsy`,
      queryRepayPlanDetailList: `${httpBlc}/ferricyanhydric/bursautee/dissuasively`,
      queryCustRepayInfo: `${httpBlc}/hardstanding/convictive/silen`,
      submitRepayRequestion: `${httpBlc}/impendency/preinductive`,
      hasOnlineLoan: `${httpBlc}/luwian/heartfelt`, // 获取是否有在途
      popAPPConfig: `${httpBlc}/bradyseismic/cyclothymiac` // 获取还款页面配置
    },
    withdraw: {
      getValidCode4nc: `${httpBlc}/koff/unharbored`,
      checkValidCodeBVN4nc: `${httpBlc}/shaveweed/undiaphanous/promycelial`,
      queryNoBindCardInfo: `${httpBlc}/bladderwort/sompne`, // 获取用户预提现订单信息
      queryCustomerBankCardList4nc: `${httpBlc}/twitcheling/woffler/telotype`,
      queryWithdrawAcctInfoCutoverSelect4nc: `${httpBlc}/nonoutlawry/cysteine`,
      loanApply4merge: `${httpBlc}/infinitation/rambunctious/indenter`,
      loanApplyNoBindCardInit: `${httpBlc}/donnert/syrma/exampleship`,
      queryCustCardInfo: `${httpBlc}/malinowskite/fangy`,
      queryCustProductInfo: `${httpBlc}/mercurialis/spuriae`,
      setTransactionPassword4nc: `${httpBlc}/periclasite/slumber/cowherb`,
      dropLoanApplyCase: `${httpBlc}/mattboard/lagoonside/reconciler`
    },
    commonBlc: {
      queryStageInfo4nc: `${httpBlc}/diceras/juncous`,
      queryLoanList4nc: `${httpBlc}/protoglobulose/speal`,
      queryCustFinancialStatus4nc: `${httpBlc}/sulphurate/dactylous/myctophidae`,
      routeRedirect: `${httpBlc}/undetectable/tigerly`,
      starsPopup: `${httpBlc}/pyoxanthose/butanol/roundishness`,
      saveStarsScore: `${httpBlc}/pentremital/claustrophobia`,
      loanCalculate4nc: `${httpBlc}/mayfowl/prevalescent`,
      getSysDate4nc: `${httpBlc}/anthophora/angiology/bovid`,
      getUserType: `${httpBlc}/hydrogenous/lightbrained`, // 用户类型
      queryActivityInfo: `${httpBlc}/thiasine/untappable`, // 活动信息接口（目前只有PC/NC使用。新包使用需要映射）
      queryLoanApplyAuthority: `${httpBlc}/dermatography/intermittency`, // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: `${httpBlc}/heteronymic/noncompression/reattend`, // 验证nin
      applyLoanCaptchaSend: `${httpBlc}/rhythmical/corroborative/muckite`, // 发送otp
      increaseCredit: `${httpBlc}/discusser/mesalike`, // 提交增信资料触发增信
      queryDict: `${httpBlc}/bobfly/catholicism`, // 字典接口
      deviceVerification: `${httpBlc}/elaeomargaric/semiquaver/hyperdulical` // 设备id检测验证
    },
    bankAccount: {
      verifyBankAccount4nc: `${httpBlc}/unfeeing/transpalmar`,
      addBankAccount4nc: `${httpBlc}/adjustation/pterocarpous/potentize`,
      queryBankList4nc: `${httpBlc}/emma/oilcoat`,
      queryPlutusBindBankCardStatus: `${httpBlc}/erthling/lipolytic`
    },
    profileInfo: {
      saveContactList: `${httpBlc}/fatil/senary` // 更新紧急联系人信息
    }
  }
}
