/**
 * 开发环境使用的地址配置
 *
 */

let bigDataHttp = process.env.VUE_APP_BIGDATA_API_MAP_URL;
let httpBlc = process.env.VUE_APP_API_MAP_URL;

if (process.env.NODE_ENV === 'development') {
  bigDataHttp = '/iot'
  httpBlc = '/buks'
}

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `${bigDataHttp}/unseclusive/burliness/rhaetian`, // 大数据埋点
      uploadFile: `${bigDataHttp}/eumenidae/beautyship`, // 上传附件
      complaintUpload: `${bigDataHttp}/hadassah/martingale`, // 客诉提交
      //creditInvestigationUpload: `${bigDataHttp}/northeasterly/slighter`, // 增信提交
      creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload_v2`, // 增信提交
      getUrls: `${bigDataHttp}/gettable/chamberlainship/unjustness` // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: `${httpBlc}/gingerness/cetonian`,
      queryCouponUsageRecord4nc: `${httpBlc}/pellitory/upshut`,
      createCoupon: `${httpBlc}/centripetally/outporter`
    },
    repayment: {
      activeQueryPlutusPaymentResult: `${httpBlc}/oversea/culture`,
      queryLoanDetils4nc: `${httpBlc}/unhomogeneous/activable`,
      getContractPath4nc: `${httpBlc}/caesaropapism/felsobanyite`,
      queryPassAmount: `${httpBlc}/bourignonist/horseshoer/ere`,
      loanAgainGenMultiOrder: `${httpBlc}/jinrikiman/subversive/ramisectomy`,
      cancelLoanPreStatus: `${httpBlc}/scotomia/thoughtlessness`,
      generatePlutusWebOrder4nc: `${httpBlc}/traversely/autocollimation/cyclosporinae`,
      queryRepayPlanDetailList: `${httpBlc}/leadback/thysanoura`,
      queryCustRepayInfo: `${httpBlc}/jowler/skanda`,
      submitRepayRequestion: `${httpBlc}/killick/aquascutum/emergentness`,
      hasOnlineLoan: `${httpBlc}/pranksomeness/ventriculoscopy/transfigurate`, // 获取是否有在途
      popAPPConfig: `${httpBlc}/contrafissura/caddice` // 获取还款页面配置
    },
    withdraw: {
      getValidCode4nc: `${httpBlc}/uneliminated/swizzle/popshop`,
      checkValidCodeBVN4nc: `${httpBlc}/separatress/borocarbide/reflectingly`,
      queryNoBindCardInfo: `${httpBlc}/chimakuan/unwrinkleable/dodgeful`, // 获取用户预提现订单信息
      queryCustomerBankCardList4nc: `${httpBlc}/casparian/dunstable/bookrack`,
      queryWithdrawAcctInfoCutoverSelect4nc: `${httpBlc}/appeasingly/unbooked/tumbrel`,
      loanApply4merge: `${httpBlc}/twaddlingly/salesmanship`,
      loanApplyNoBindCardInit: `${httpBlc}/proclamatory/tautonymy/hypochlorhydric`,
      queryCustCardInfo: `${httpBlc}/basementward/permoralize/underjawed`,
      queryCustProductInfo: `${httpBlc}/magnifico/aponeurorrhaphy/coelar`,
      setTransactionPassword4nc: `${httpBlc}/preimage/orris/cheirography`,
      dropLoanApplyCase: `${httpBlc}/colation/eremic/didinium`
    },
    commonBlc: {
      queryStageInfo4nc: `${httpBlc}/cogovernment/consiliary`,
      queryLoanList4nc: `${httpBlc}/fulgora/institutionalist`,
      queryCustFinancialStatus4nc: `${httpBlc}/stretch/millerism/colosseum`,
      routeRedirect: `${httpBlc}/spathic/antivenereal/hoi`,
      starsPopup: `${httpBlc}/regarment/hepatalgia/corpuscle`,
      saveStarsScore: `${httpBlc}/tavert/overmercifulness/sasa`,
      loanCalculate4nc: `${httpBlc}/generalty/nonwar`,
      getSysDate4nc: `${httpBlc}/lubritorium/dalliance/deceitfully`,
      getUserType: `${httpBlc}/osteogenesis/elapinae/juggling`, // 用户类型
      queryActivityInfo: `${httpBlc}/wordish/homoseismal/quillaic`, // 活动信息接口（目前只有PC/NC使用。新包使用需要映射）
      queryLoanApplyAuthority: `${httpBlc}/wadder/hydrocephaly`, // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: `${httpBlc}/vertebrosacral/angiocholitis`, // 验证nin
      applyLoanCaptchaSend: `${httpBlc}/phonautogram/needlework/veretillum`, // 发送otp
      increaseCredit: `${httpBlc}/meltable/lecture/buffy`, // 提交增信资料触发增信
      queryDict: `${httpBlc}/unstarch/semicalcareous/repulse`, // 字典接口
      deviceVerification: `${httpBlc}/divertor/anticourtier/biochemically` // 设备id检测验证
    },
    bankAccount: {
      verifyBankAccount4nc: `${httpBlc}/patly/bersiamite/figurately`,
      addBankAccount4nc: `${httpBlc}/miscegenation/anthrarufin`,
      queryBankList4nc: `${httpBlc}/proenlargement/arteriectasia`,
      queryPlutusBindBankCardStatus: `${httpBlc}/brander/bonnaz/reptilia`
    },
    profileInfo: {
      saveContactList: `${httpBlc}/craig/celibate/sepiola` // 更新紧急联系人信息
    }
  }
}
