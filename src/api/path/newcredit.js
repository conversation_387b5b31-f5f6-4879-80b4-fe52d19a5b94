/**
 * 开发环境使用的地址配置
 *
 */

let bigDataHttp = process.env.VUE_APP_BIGDATA_API_MAP_URL;
let httpBlc = process.env.VUE_APP_API_MAP_URL;

if (process.env.NODE_ENV === 'development') {
  bigDataHttp = '/ozfy'
  httpBlc = '/hnes'
}

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `${bigDataHttp}/unripped/spondean`, // 大数据埋点
      uploadFile: `${bigDataHttp}/smyrniote/lexigraphy`, // 上传附件
      complaintUpload: `${bigDataHttp}/sikh/corixidae/amphibologically`, // 客诉提交
      //creditInvestigationUpload: `${bigDataHttp}/kshatriya/overcomplacent`, // 增信提交
      creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload_v2`, // 增信提交
      getUrls: `${bigDataHttp}/recreational/divinator` // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: `${httpBlc}/untributary/purohepatitis`,
      queryCouponUsageRecord4nc: `${httpBlc}/underact/sally`,
      createCoupon: `${httpBlc}/indices/cacochymia`
    },
    repayment: {
      activeQueryPlutusPaymentResult: `${httpBlc}/fuchsone/neutrologistic`,
      queryLoanDetils4nc: `${httpBlc}/porometer/artistdom`,
      getContractPath4nc: `${httpBlc}/kuvera/bailment`,
      queryPassAmount: `${httpBlc}/passableness/bedrug/odds`,
      loanAgainGenMultiOrder: `${httpBlc}/wistless/anton`,
      cancelLoanPreStatus: `${httpBlc}/eriophyes/champacol/unobservantness`,
      generatePlutusWebOrder4nc: `${httpBlc}/insular/equipaga/butyraldehyde`,
      queryRepayPlanDetailList: `${httpBlc}/ridibund/acinar`,
      queryCustRepayInfo: `${httpBlc}/precultural/unwonder`,
      submitRepayRequestion: `${httpBlc}/coronoid/sovereignly`,
      hasOnlineLoan: `${httpBlc}/entanglingly/farrisite`, // 获取是否有在途
      popAPPConfig: `${httpBlc}/adenometritis/shipowner/untempested` // 获取还款页面配置
    },
    withdraw: {
      getValidCode4nc: `${httpBlc}/intertissued/ponderary/wareroom`,
      checkValidCodeBVN4nc: `${httpBlc}/unacceptant/octoon`,
      queryNoBindCardInfo: `${httpBlc}/dolerophanite/eudaemon`, // 获取用户预提现订单信息
      queryCustomerBankCardList4nc: `${httpBlc}/overemotional/pezizales/commendam`,
      queryWithdrawAcctInfoCutoverSelect4nc: `${httpBlc}/difficileness/hoose`,
      loanApply4merge: `${httpBlc}/raininess/aleutian/gravelly`,
      loanApplyNoBindCardInit: `${httpBlc}/euthenist/brendan`,
      queryCustCardInfo: `${httpBlc}/postvenereal/aloetic/floscularian`,
      queryCustProductInfo: `${httpBlc}/holotrichida/ovopyriform`,
      setTransactionPassword4nc: `${httpBlc}/scotomatic/spathous`,
      dropLoanApplyCase: `${httpBlc}/cautionry/gyrocar/oarswoman`
    },
    commonBlc: {
      queryStageInfo4nc: `${httpBlc}/loxocosm/sarmentous`,
      queryLoanList4nc: `${httpBlc}/bystreet/centesimally/recongelation`,
      queryCustFinancialStatus4nc: `${httpBlc}/unboundless/apogamous`,
      routeRedirect: `${httpBlc}/zwinglian/semifrantic`,
      starsPopup: `${httpBlc}/sandalwood/radiopelvimetry`,
      saveStarsScore: `${httpBlc}/pathy/sulvasutra/assassinist`,
      loanCalculate4nc: `${httpBlc}/untagged/underroller`,
      getSysDate4nc: `${httpBlc}/lass/puisne`,
      getUserType: `${httpBlc}/twelfhyndeman/twentiethly`, // 用户类型
      queryActivityInfo: `${httpBlc}/departmentally/uranate/unenlivening`, // 活动信息接口（目前只有PC/NC使用。新包使用需要映射）
      queryLoanApplyAuthority: `${httpBlc}/dentolabial/outboard`, // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: `${httpBlc}/haustement/megaloblastic`, // 验证nin
      applyLoanCaptchaSend: `${httpBlc}/lagopus/inconsumably/intemperature`, // 发送otp
      increaseCredit: `${httpBlc}/manzil/loessic/volley`, // 提交增信资料触发增信
      queryDict: `${httpBlc}/remount/medulla`, // 字典接口
      deviceVerification: `${httpBlc}/iodoso/indecidua` // 设备id检测验证
    },
    bankAccount: {
      verifyBankAccount4nc: `${httpBlc}/tendency/homoousia/croisette`,
      addBankAccount4nc: `${httpBlc}/galloway/witchbells`,
      queryBankList4nc: `${httpBlc}/minahassan/invoker/wim`,
      queryPlutusBindBankCardStatus: `${httpBlc}/retrogress/supraocular/boundedness`
    },
    profileInfo: {
      saveContactList: `${httpBlc}/daggletailed/earthiness/worset` // 更新紧急联系人信息
    }
  }
}
