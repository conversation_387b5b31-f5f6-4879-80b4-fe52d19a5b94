/**
 * 生产环境使用的地址配置
 *
 */

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `/api/truemoneyios/tributaries/defray`, // 大数据埋点
      uploadFile: '/api/truemoneyios/dissidents/tunning', // 上传附件
      complaintUpload: '/api/truemoneyios/eliot/directive', // 客诉提交
      //creditInvestigationUpload: '/api/truemoneyios/care/musical', // 增信提交
      creditInvestigationUpload: '/api/truemoneyios/hthlaeeg/doaiyfbtttcyl', // 增信提交
      getUrls: '/api/truemoneyios/devote/serve' // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: '/user/laonOrder/voucher/voucher-list',   // '/blc-service/voucher/v1/queryCouponList4nc',
      queryCouponUsageRecord4nc:'/user/laonOrder/query/voucher-usage-record',   // '/blc-service/voucher/v1/queryCouponUsageRecord4nc'
      createCoupon: '/user/laonOrder/getNewCou' // '/blc-service/coupon/v1/createCoupon'
    },
    repayment: {
      activeQueryPlutusPaymentResult:'/user/laonOrder/plutus/compensation-result',   //  '/blc-service/plutus/v1/activeQueryPlutusPaymentResult',
      queryLoanDetils4nc:'/user/laonOrder/particular',   // '/blc-service/query/v1/queryLoanDetils4nc',
      getContractPath4nc:'/user/laonOrder/contractUrl',   // '/blc-service/query/v1/getContractPath4nc',
      queryPassAmount: '/userinfo/find/pass-amount',   //'/blc-service/query/v1/queryPassAmount',
      loanAgainGenMultiOrder: '/user/laonOrder/again-generate-mult-order',   //'/blc-service/apply/v1/loanAgainGenMultiOrder',
      cancelLoanPreStatus: '/user/preToLoan/cancel-preToLoan',   //'/blc-service/apply/v1/cancelLoanPreStatus',
      generatePlutusWebOrder4nc:'/user/laonOrder/plutus/gen/repay-order' ,  // '/blc-service/plutus/v1/generatePlutusWebOrder4nc',
      queryRepayPlanDetailList:'/user/laonOrder/query/repay-plan-particular-list',   //  '/blc-service/query/v1/queryRepayPlanDetailList',
      queryCustRepayInfo:'/user/laonOrder/query/repay-information',   // '/blc-service/cust/queryCustRepayInfo',
      submitRepayRequestion: '/user/laonOrder/submit/repay/requestion',   //'/blc-service/user/v1/submitRepayRequestion'
      hasOnlineLoan: '/user/hfive/ben1/loaning' // 获取是否有在途
    },
    withdraw: {
      getValidCode4nc:'/tool/giveCode',   //  '/blc-service/user/v1/getValidCode4nc',
      checkValidCodeBVN4nc: '/tool/check-valid-code-bvn',   // '/blc-service/user/v1/checkValidCodeBVN4nc',
      queryNoBindCardInfo:'/user/laonOrder/submit/repay/requestion', // '/blc-service/apply/v1/queryNoBindCardInfo',
      queryCustomerBankCardList4nc:'/user/poster/query/customer/cardList',   //  '/blc-service/query/v1/queryCustomerBankCardList4nc',
      queryWithdrawAcctInfoCutoverSelect4nc:'/userinfo/find/withdraw-account-information-customer-select',   //  '/blc-service/query/v1/queryWithdrawAcctInfoCutoverSelect4nc',
      loanApply4merge:'/user/laonOrder/new-apply',   // '/blc-service/cust/loanApply4merge',
      loanApplyNoBindCardInit:'/user/laonOrder/apply-init-information', // '/blc-service/apply/v1/loanApplyNoBindCardInit',
      queryCustCardInfo:'/user/poster/repay-poster-information', // '/blc-service/cust/queryCustCardInfo',
      queryCustProductInfo: '/userinfo/find/product-information', //'/blc-service/cust/queryCustProductInfo',
      setTransactionPassword4nc:'/user/information/reload/password',   // '/blc-service/user/v1/setTransactionPassword4nc',
      dropLoanApplyCase:'/user/information/drop-laonOrder-apply-case'   // '/blc-service/user/v1/dropLoanApplyCase'
    },
    commonBlc: {
      queryStageInfo4nc:'/user/information/stage-information',   //  '/blc-service/query/v1/queryStageInfo4nc',
      queryLoanList4nc:'/user/laonOrder/list',   // '/blc-service/query/v1/queryLoanList4nc',
      queryCustFinancialStatus4nc:'/user/laonOrder/query/finanical-status',   //  '/blc-service/query/v1/queryCustFinancialStatus4nc',
      routeRedirect:'/userinfo/find/bind-poster-plutus-url',   // '/blc-service/user/v1/routeRedirect',
      starsPopup:'/userinfo/find/stars-popup',   // '/blc-service/inner/v1/starsPopup',
      saveStarsScore:'/user/store/stars-score',   //  '/blc-service/inner/v1/saveStarsScore',
      loanCalculate4nc:'/user/laonOrder/calculate',   //  '/blc-service/apply/v1/loanCalculate4nc',
      getSysDate4nc:'/tool/sys-date' ,  //  '/blc-service/query/v1/getSysDate4nc',
      getUserType:'/userinfo/find/user-type',   // '/blc-service/user/v1/getUserType'
      queryLoanApplyAuthority: '/userinfo/find/laonOrder-apply-authority', // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: '/user/appointment/bank/bynin' // 验证nin
    },
    bankAccount: {
      verifyBankAccount4nc: '/user/information/validate-account',   //'/blc-service/user/v1/verifyBankAccount4nc',
      addBankAccount4nc:'/user/banklist/plus-banklist',   //  '/blc-service/apply/v1/addBankAccount4nc',
      queryBankList4nc:'/user/banklist/bankList',   //    '/blc-service/query/v1/queryBankList4nc',
      queryPlutusBindBankCardStatus:'/user/banklist/poster/bind-status' //  '/blc-service/user/v1/queryPlutusBindBankCardStatus'
    }
  }
}
