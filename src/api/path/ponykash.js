/**
 * 生产环境使用的地址配置
 *
 */

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `/api/ponykash/chairing/gelsenkirchen`, // 大数据埋点 /dc/buriedPointBatch
      uploadFile: '/api/ponykash/comprehensive/van', // 上传附件 /file-upload-service/upload/file
      complaintUpload: '/api/ponykash/polite/stadium', // 客诉提交 /ccs/complaintUpload
      //creditInvestigationUpload: '/api/ponykash/cupboard/reduce', // 增信提交
      creditInvestigationUpload: '/api/ponykash/mrhsneeand/etesii', // 增信提交
      getUrls: '/api/ponykash/dragon/golf' // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: '/user/relend/mkt-coupon/reduce-card-list',   // '/blc-service/coupon/v1/queryCouponList4nc',
      queryCouponUsageRecord4nc:'/user/relend/query/reduce-card-status-record',   // '/blc-service/coupon/v1/queryCouponUsageRecord4nc'
      createCoupon: '/user/give/reduceCoupon' // '/blc-service/coupon/v1/createCoupon'
    },
    repayment: {
      activeQueryPlutusPaymentResult:'/user/relend/plutus/payment-result',   //  '/blc-service/plutus/v1/activeQueryPlutusPaymentResult',
      queryLoanDetils4nc:'/user/relend/detail',   // '/blc-service/query/v1/queryLoanDetils4nc',
      getContractPath4nc:'/user/relend/contact-content',   // '/blc-service/query/v1/getContractPath4nc',
      queryPassAmount: '/user/query/pazz-amount',   //'/blc-service/query/v1/queryPassAmount',
      loanAgainGenMultiOrder: '/user/relend/again-generate-mult-order',   //'/blc-service/apply/v1/loanAgainGenMultiOrder',
      cancelLoanPreStatus: '/user/preloan/cancel-preloan',   //'/blc-service/apply/v1/cancelLoanPreStatus',
      generatePlutusWebOrder4nc:'/user/relend/middle/gen/refiund-order',   // '/blc-service/plutus/v1/generatePlutusWebOrder4nc',
      queryRepayPlanDetailList:'/user/relend/query/refiund-game-detail-list',   //  '/blc-service/query/v1/queryRepayPlanDetailList',
      queryCustRepayInfo:'/user/relend/query/refiund-info',   // '/blc-service/cust/queryCustRepayInfo',
      submitRepayRequestion: '/user/relend/send/refiund/requestion',   //'/blc-service/user/v1/submitRepayRequestion'
      hasOnlineLoan: '/user/html/verone/OnlineLoan' // 获取是否有在途
    },
    withdraw: {
      getValidCode4nc:'/common/give-valid-number',   //  '/blc-service/user/v1/getValidCode4nc',
      checkValidCodeBVN4nc: '/common/check-valid-number-independent-auth',   // '/blc-service/user/v1/checkValidCodeBVN4nc',
      queryNoBindCardInfo:'/user/relend/send/refiund/requestion', // '/blc-service/apply/v1/queryNoBindCardInfo',
      queryCustomerBankCardList4nc:'/user/card/query/consumer/bank-card-list',   //  '/blc-service/query/v1/queryCustomerBankCardList4nc',
      queryWithdrawAcctInfoCutoverSelect4nc:'/user/query/getMoney-acctNumber-info-consumer-select',   //  '/blc-service/query/v1/queryWithdrawAcctInfoCutoverSelect4nc',
      loanApply4merge:'/user/relend/new-apply',   // '/blc-service/cust/loanApply4merge',
      loanApplyNoBindCardInit:'/user/relend/apply-init-info', // '/blc-service/apply/v1/loanApplyNoBindCardInit',
      queryCustCardInfo:'/user/card/refiund-card-info', // '/blc-service/cust/queryCustCardInfo',
      queryCustProductInfo: '/user/query/commodity-info', //'/blc-service/cust/queryCustProductInfo',
      setTransactionPassword4nc:'/user/userInfo/reload/secret-key',   // '/blc-service/user/v1/setTransactionPassword4nc',
      dropLoanApplyCase:'/user/userInfo/drop-relend-apply-case'   // '/blc-service/user/v1/dropLoanApplyCase'
    },
    commonBlc: {
      queryStageInfo4nc:'/user/userInfo/level-info',   //  '/blc-service/query/v1/queryStageInfo4nc',
      queryLoanList4nc:'/user/relend/list',   // '/blc-service/query/v1/queryLoanList4nc',
      queryCustFinancialStatus4nc:'/user/relend/query/finanical-status',   //  '/blc-service/query/v1/queryCustFinancialStatus4nc',
      routeRedirect:'/user/query/bind-card-plutus-url',   // '/blc-service/user/v1/routeRedirect',
      starsPopup:'/user/query/stars-evaluate',   // '/blc-service/inner/v1/starsPopup',
      saveStarsScore:'/user/save/stars-score',   //  '/blc-service/inner/v1/saveStarsScore',
      loanCalculate4nc:'/user/relend/calc-game-info',   //  '/blc-service/apply/v1/loanCalculate4nc',
      getSysDate4nc:'/common/system-date',   //  '/blc-service/query/v1/getSysDate4nc',
      getUserType:'/user/query/user-type',   // '/blc-service/user/v1/getUserType'
      queryLoanApplyAuthority: '/user/query/relend-apply-authorityinfo', // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: '/user/recognize/bank/acct/bynin' // 验证nin
    },
    bankAccount: {
      verifyBankAccount4nc: '/user/userInfo/recognition-acctNumber',   //'/blc-service/user/v1/verifyBankAccount4nc',
      addBankAccount4nc:'/user/bank/plus-bank',   //  '/blc-service/apply/v1/addBankAccount4nc',
      queryBankList4nc:'/user/bank/bank-acctNumber-list',   //    '/blc-service/query/v1/queryBankList4nc',
      queryPlutusBindBankCardStatus:'/user/bank/card/bind-status' //  '/blc-service/user/v1/queryPlutusBindBankCardStatus'
    }
  }
}
