/**
 * 开发环境使用的地址配置
 *
*/

let bigDataHttp = process.env.VUE_APP_BIGDATA_API_URL;
let httpBlc = process.env.VUE_APP_API_URL;

if (process.env.NODE_ENV === 'development') {
  bigDataHttp = ''
  httpBlc = ''
}

export default () => {
  return {
    bigDataUrl: {
      buriedPointBatch: `${bigDataHttp}/dc/buriedPointBatch/v2`, // 大数据埋点
      uploadFile: `${bigDataHttp}/file-upload-service/upload/file`, // 上传附件
      complaintUpload: `${bigDataHttp}/ccs/complaintUpload`, // 客诉提交
      //creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload`, // 增信提交
      creditInvestigationUpload: `${bigDataHttp}/ccs/credit_investigation_upload_v2`, // 增信提交
      getUrls: `${bigDataHttp}/ccs/s3/urls` // 获取图片预览
    },
    coupon: {
      queryCouponList4nc: `${httpBlc}/blc-service/coupon/v1/queryCouponList4nc`,
      queryCouponUsageRecord4nc: `${httpBlc}/blc-service/coupon/v1/queryCouponUsageRecord4nc`,
      createCoupon: `${httpBlc}/blc-service/coupon/v1/createCoupon`
    },
    repayment: {
      activeQueryPlutusPaymentResult: `${httpBlc}/blc-service/plutus/v1/activeQueryPlutusPaymentResult`,
      queryLoanDetils4nc: `${httpBlc}/blc-service/query/v1/queryLoanDetils4nc`,
      getContractPath4nc: `${httpBlc}/blc-service/query/v1/getContractPath4nc`,
      queryPassAmount: `${httpBlc}/blc-service/query/v1/queryPassAmount`,
      loanAgainGenMultiOrder: `${httpBlc}/blc-service/apply/v1/loanAgainGenMultiOrder`,
      cancelLoanPreStatus: `${httpBlc}/blc-service/apply/v1/cancelLoanPreStatus`,
      generatePlutusWebOrder4nc: `${httpBlc}/blc-service/plutus/v1/generatePlutusWebOrder4nc`,
      queryRepayPlanDetailList: `${httpBlc}/blc-service/query/v1/queryRepayPlanDetailList`,
      queryCustRepayInfo: `${httpBlc}/blc-service/cust/queryCustRepayInfo`,
      submitRepayRequestion: `${httpBlc}/blc-service/user/v1/submitRepayRequestion`,
      hasOnlineLoan: `${httpBlc}/blc-service/h5/v1/hasOnlineLoan`, // 获取是否有在途
      popAPPConfig: `${httpBlc}/blc-service/query/v1/popAPPConfig` // 获取还款页面配置
    },
    withdraw: {
      getValidCode4nc: `${httpBlc}/blc-service/user/v1/getValidCode4nc`,
      checkValidCodeBVN4nc: `${httpBlc}/blc-service/user/v1/checkValidCodeBVN4nc`,
      queryNoBindCardInfo: `${httpBlc}/blc-service/apply/v1/queryNoBindCardInfo`, // 获取用户预提现订单信息
      queryCustomerBankCardList4nc: `${httpBlc}/blc-service/query/v1/queryCustomerBankCardList4nc`,
      queryWithdrawAcctInfoCutoverSelect4nc: `${httpBlc}/blc-service/query/v1/queryWithdrawAcctInfoCutoverSelect4nc`,
      loanApply4merge: `${httpBlc}/blc-service/cust/loanApply4merge`,
      loanApplyNoBindCardInit: `${httpBlc}/blc-service/apply/v1/loanApplyNoBindCardInit`,
      queryCustCardInfo: `${httpBlc}/blc-service/cust/queryCustCardInfo`,
      queryCustProductInfo: `${httpBlc}/blc-service/cust/queryCustProductInfo`,
      setTransactionPassword4nc: `${httpBlc}/blc-service/user/v1/setTransactionPassword4nc`,
      dropLoanApplyCase: `${httpBlc}/blc-service/user/v1/dropLoanApplyCase`
    },
    commonBlc: {
      queryStageInfo4nc: `${httpBlc}/blc-service/query/v1/queryStageInfo4nc`,
      queryLoanList4nc: `${httpBlc}/blc-service/query/v1/queryLoanList4nc`,
      queryCustFinancialStatus4nc: `${httpBlc}/blc-service/query/v1/queryCustFinancialStatus4nc`,
      routeRedirect: `${httpBlc}/blc-service/user/v1/routeRedirect`,
      starsPopup: `${httpBlc}/blc-service/inner/v1/starsPopup`,
      saveStarsScore: `${httpBlc}/blc-service/inner/v1/saveStarsScore`,
      loanCalculate4nc: `${httpBlc}/blc-service/apply/v1/loanCalculate4nc`,
      getSysDate4nc: `${httpBlc}/blc-service/query/v1/getSysDate4nc`,
      getUserType: `${httpBlc}/blc-service/user/v1/getUserType`, // 用户类型
      queryActivityInfo: `${httpBlc}/blc-service/activity/queryActivityInfo`, // 活动信息接口（目前只有PC/NC使用。新包使用需要映射）
      queryLoanApplyAuthority: `${httpBlc}/blc-service/query/v1/queryLoanApplyAuthority`, // 判断借款权限，包括循环贷资格等
      verifyGoldmanNIN: `${httpBlc}/blc-service/cust/verifyGoldmanNIN`, // 验证nin
      applyLoanCaptchaSend: `${httpBlc}/blc-service/otp/v1/applyLoanCaptcha/send`, // 发送otp
      increaseCredit: `${httpBlc}/blc-service/apply/v1/increase-credit`, // 提交增信资料触发增信
      queryDict: `${httpBlc}/blc-service/query/v1/queryDict4nc`, // 字典接口
      deviceVerification: `${httpBlc}/blc-service/user/v1/deviceVerification` // 设备id检测验证
    },
    bankAccount: {
      verifyBankAccount4nc: `${httpBlc}/blc-service/user/v1/verifyBankAccount4nc`,
      addBankAccount4nc: `${httpBlc}/blc-service/apply/v1/addBankAccount4nc`,
      queryBankList4nc: `${httpBlc}/blc-service/query/v1/queryBankList4nc`,
      queryPlutusBindBankCardStatus: `${httpBlc}/blc-service/user/v1/queryPlutusBindBankCardStatus`
    },
    profileInfo: {
      saveContactList: `${httpBlc}/blc-service/apply/v1/saveContactList` // 更新紧急联系人信息
    }
  }
}
