import basePath from "./path/basePath";
import store from '@/store'; // 引入 store

// 初始使用基础路径
let pathObj = basePath();
const isDevMapping = process.env.NODE_ENV === 'development' && process.env.VUE_APP_MessageMapping === 'true';


/**
 * 根据 store 中的配置获取正确的路径对象
 * @returns {Promise<Object>} 路径配置对象
 */
export async function initPathConfig() {
  try {
    // 检查是否启用了路径映射功能
    const isPathMapping = isDevMapping || store.state.supportedFeatures?.messageMapping === 'true';
    
    // 根据路径映射设置和产品类型选择路径，使用VUE_APP_API_MAP_URL
    if (isPathMapping && process.env.VUE_APP_PRODUCT) {
      try {
        // 动态导入对应产品的路径配置
        const pathModule = require(`./path/${process.env.VUE_APP_PRODUCT}`).default;
        pathObj = pathModule();
        console.log('已加载产品特定路径配置:', process.env.VUE_APP_PRODUCT);
      } catch (err) {
        console.log('未找到产品路径配置，使用基础路径');
        pathObj = basePath();
      }
    } else {
      // 未启用路径映射或未找到产品路径配置，使用VUE_APP_API_URL
      console.log('根据配置使用基础路径');
      pathObj = basePath();
    }
    return pathObj;
  } catch (error) {
    console.error('加载路径配置失败', error);
    pathObj = basePath();
    return pathObj;
  }
}

// 更新路径配置的函数
export function updatePathConfig() {
  initPathConfig();
}

// 导出当前路径配置
// 使用 getter 属性确保每次访问时获取最新配置
export default {
  get bigDataUrl() { return pathObj.bigDataUrl },
  get coupon() { return pathObj.coupon },
  get repayment() { return pathObj.repayment },
  get withdraw() { return pathObj.withdraw },
  get commonBlc() { return pathObj.commonBlc },
  get bankAccount() { return pathObj.bankAccount },
  get profileInfo() { return pathObj.profileInfo }
};
