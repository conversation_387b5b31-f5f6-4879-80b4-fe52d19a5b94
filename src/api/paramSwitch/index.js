import { invertObject, replaceKeys } from '@/assets/js/common.js';
import store from '../../store'
let keyMapObj = {};

// const supportedFeatures = store.state.supportedFeatures;


// if (process.env.NODE_ENV === 'development') {
//   keyMap = require(`./all/common`)
// } else {
//   keyMap = require(`./all/${process.env.VUE_APP_PRODUCT}`)
// }
try {
  keyMapObj = require(`./all/${process.env.VUE_APP_PRODUCT}`);
} catch (error) {
  // console.log('error', error)
}

const requestKeyMap = keyMapObj.default;
const responseKeyMap = invertObject(requestKeyMap);
const isDevMapping = process.env.NODE_ENV === 'development' && process.env.VUE_APP_MessageMapping === 'true';

export const requestSwitch = function(obj) {
  // 当前设备是否支持报文映射
  const isMessageMapping = isDevMapping || store.state.supportedFeatures.messageMapping && store.state.supportedFeatures.messageMapping === 'true';
  return requestKeyMap && isMessageMapping ? replaceKeys(obj, requestKeyMap) : obj;
}


export const responseSwitch = function(obj) {
  // 当前设备是否支持报文映射
  const isMessageMapping = isDevMapping || store.state.supportedFeatures.messageMapping && store.state.supportedFeatures.messageMapping === 'true';
  return responseKeyMap && isMessageMapping  ? replaceKeys(obj, responseKeyMap) : obj;
}
