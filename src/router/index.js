import Vue from 'vue';
import VueRouter from 'vue-router';

// 提现相关功能
// const index = () => import(/* webpackChunkName: "withdraw" */ '@/views/withdraw/index.vue')
// const withdrawStatus = () => import(/* webpackChunkName: "withdraw" */ '@/views/withdraw/withdraw-status.vue')
// const setPin = () => import(/* webpackChunkName: "withdraw" */ '@/views/withdraw/set-pin.vue')
// const contract = () => import(/* webpackChunkName: "withdraw" */ '@/views/withdraw/contract.vue')
// const forgetPin = () => import(/* webpackChunkName: "withdraw" */ '@/views/withdraw/forget-pin.vue')
// const pinResetStatus = () => import(/* webpackChunkName: "withdraw" */ '@/views/withdraw/pin-reset-status.vue')

// // 还款相关功能
// const repaymentIndex = () => import(/* webpackChunkName: "repayment" */ '@/views/repayment/index.vue')
// const afterRepayment = () => import(/* webpackChunkName: "repayment" */ '@/views/repayment/after-repayment.vue')
// const loanDetail = () => import(/* webpackChunkName: "repayment" */ '@/views/repayment/loan-detail.vue')
// const loadUrlPage = () => import(/* webpackChunkName: "repayment" */ '@/views/repayment/load-url-page.vue')
// const termDetail = () => import(/* webpackChunkName: "repayment" */ '@/views/repayment/term-detail.vue')

import index from '@/views/withdraw/new-index.vue'
import withdrawStatus from '@/views/withdrawStatus/new-withdraw-status.vue'
import setPin from '@/views/setPin/new-set-pin.vue'
import contract from '@/views/withdraw/contract.vue'
import forgetPin from '@/views/forgetPin/new-forget-pin.vue'
import pinResetStatus from '@/views/pinResetStatus/new-pin-reset-status.vue'
import orderConfirm from '@/views/orderConfirm/new-order-confirm.vue'

// 还款相关功能
import repaymentIndex from '@/views/repayment/new-repayment-index.vue'
import afterRepayment from '@/views/afterRepayment/new-after-repayment.vue'
import loanDetail from '@/views/loanDetail/new-loan-detail.vue'
import loadUrlPage from '@/views/repayment/load-url-page.vue'
import termDetail from '@/views/termDetail/new-term-detail.vue'

// 还款额外逻辑

const afterRepaymentLead = () => import(/* webpackChunkName: "repaymentExtra" */ '@/views/afterRepaymentLead/new-after-repayment-lead.vue')



// 优惠券相关功能
const couponsList = () => import(/* webpackChunkName: "coupon" */ '@/views/coupon/new-coupons-list.vue')
const invaildCouponsList = () => import(/* webpackChunkName: "invaildCouponsList" */ '@/views/invaildCouponsList/new-invaild-coupons-list.vue')
const couponsFaq = () => import(/* webpackChunkName: "couponsFaq" */ '@/views/couponsFaq/new-coupons-faq.vue')


// 绑账户、绑卡相关功能
const checkCardStatus = () => import(/* webpackChunkName: "pay" */ '@/views/checkCardStatus/new-check-card-status.vue')
const cardStatus = () => import(/* webpackChunkName: "pay" */ '@/views/cardStatus/new-card-status.vue')
const addBankAccount = () => import(/* webpackChunkName: "pay" */ '@/views/addBankAccount/new-add-bank-account.vue')
const bankList = () => import(/* webpackChunkName: "pay" */ '@/views/bankList/new-bank-list.vue')

// AC半流程功能

const initPage = () => import(/* webpackChunkName: "halfFlow" */ '@/views/halfFlow/init-page.vue')
const rejectResult = () => import(/* webpackChunkName: "halfFlow" */ '@/views/halfFlow/new-reject-result.vue')
const creditResult = () => import(/* webpackChunkName: "halfFlow" */ '@/views/halfFlow/new-credit-result.vue')

// 客诉
const complaint = () => import(/* webpackChunkName: "complaint" */ '@/views/complaint/new-complaint.vue')

// 增信
const increaseLimit = () => import(/* webpackChunkName: "increaseLimit" */ '@/views/increaseLimit/increaseLimit.vue')

// 提现挽留弹窗优化
const surveyActivity = () => import(/* webpackChunkName: "withdraw" */ '@/views/surveyActivity/surveyActivity.vue')

// 显示内容
const showVerdictAnnouncement = () => import(/* webpackChunkName: "showVerdictAnnouncement" */ '@/views/showVerdictAnnouncement/show-verdict-announcement.vue')

// 上传照片（用于增信）

const uploadDocuments = () => import(/* webpackChunkName: "uploadDocuments" */ '@/views/uploadDocuments/uploadDocuments.vue')

const updateEmergencyContact = () => import(/* webpackChunkName: "updateEmergencyContact" */ '@/views/updateEmergencyContact/new-updateEmergencyContact.vue')
const faceVerification = () => import(/* webpackChunkName: "faceVerification" */ '@/views/faceVerification/new-faceVerification.vue')

const inviteReview = () => import(/* webpackChunkName: "inviteReview" */ '@/views/inviteReview/new-inviteReview.vue')

const helpCenter = () => import(/* webpackChunkName: "helpCenter" */ '@/views/helpCenter/new-helpCenter.vue')


Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'index',
    component: index,
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/withdrawStatus',
    name: 'withdrawStatus',
    component: withdrawStatus
  },
  {
    path: '/repaymentIndex',
    name: 'repaymentIndex',
    component: repaymentIndex,
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/afterRepayment',
    name: 'afterRepayment',
    component: afterRepayment
  },
  {
    path: '/afterRepaymentLead',
    name: 'afterRepaymentLead',
    component: afterRepaymentLead
  },
  {
    path: '/couponsList',
    name: 'couponsList',
    component: couponsList
  },
  {
    path: '/setPin',
    name: 'setPin',
    component: setPin
  },
  {
    path: '/contract',
    name: 'contract',
    component: contract,
    meta: {
      title: 'Legal Document',
    }
  },
  {
    path: '/checkCardStatus',
    name: 'checkCardStatus',
    component: checkCardStatus
  },
  {
    path: '/cardStatus',
    name: 'cardStatus',
    component: cardStatus
  },
  {
    path: '/loanDetail',
    name: 'loanDetail',
    component: loanDetail
  },
  {
    path: '/loadUrlPage',
    name: 'loadUrlPage',
    component: loadUrlPage
  },
  {
    path: '/termDetail',
    name: 'termDetail',
    component: termDetail
  },
  {
    path: '/addBankAccount',
    name: 'addBankAccount',
    component: addBankAccount,
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/bankList',
    name: 'bankList',
    component: bankList,
  },
  {
    path: '/forgetPin',
    name: 'forgetPin',
    component: forgetPin,
  },
  {
    path: '/invaildCouponsList',
    name: 'invaildCouponsList',
    component: invaildCouponsList,
  },
  {
    path: '/pinResetStatus',
    name: 'pinResetStatus',
    component: pinResetStatus,
  },
  {
    path: '/couponsFaq',
    name: 'couponsFaq',
    component: couponsFaq,
  },
  {
    path: '/initPage',
    name: 'initPage',
    component: initPage,
  },
  {
    path: '/rejectResult',
    name: 'rejectResult',
    component: rejectResult,
  },
  {
    path: '/creditResult',
    name: 'creditResult',
    component: creditResult,
  },
  {
    path: '/orderConfirm',
    name: 'orderConfirm',
    component: orderConfirm,
  },
  {
    path: '/complaint',
    name: 'complaint',
    component: complaint,
  },
  {
    path: '/increaseLimit',
    name: 'increaseLimit',
    component: increaseLimit,
  },
  {
    path: '/surveyActivity',
    name: 'surveyActivity',
    component: surveyActivity,
  },
  {
    path: '/showVerdictAnnouncement',
    name: 'showVerdictAnnouncement',
    component: showVerdictAnnouncement,
  },
  {
    path: '/uploadDocuments',
    name: 'uploadDocuments',
    component: uploadDocuments,
  },
  {
    path: '/updateEmergencyContact',
    name: 'updateEmergencyContact',
    component: updateEmergencyContact,
  },
  {
    path: '/faceVerification',
    name: 'faceVerification',
    component: faceVerification,
  },
  {
    path: '/inviteReview',
    name: 'inviteReview',
    component: inviteReview,
  },
  {
    path: '/helpCenter',
    name: 'helpCenter',
    component: helpCenter,
  },
  {
    path: '/bookNextLoan',
    name: 'bookNextLoan',
    component: () => import(/* webpackChunkName: "bookNextLoan" */ '@/views/bookNextLoan/new-book-next-loan.vue')
  },
  {
    path: '/importantTips',
    name: 'importantTips',
    component: () => import(/* webpackChunkName: "importantTips" */ '@/views/importantTips/importantTips.vue')
  },
  {
    path: '/nativeDemo',
    name: 'nativeDemo',
    component: () => import('@/views/nativeDemo/nativeDemo.vue')
  }
]

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes
});

export default router
