import { extend } from 'vee-validate';
import { required, min, max } from 'vee-validate/dist/rules';

//自定义提示

extend('required', {
    ...required,
    message: () => "Can not be empty"
})

extend('min', min)
extend('max', max)

/*---------------------自定义校验规则 start-------------------------------*/
//Email Address 校验
extend('emailTest', {
    message: () => 'Please input correct information',
    validate: value => /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value)
});
//Phone Number 校验
extend('phoneTest', {
    message: () => 'Please input correct information',
    validate: value => /^(0[7-9][0-1])\d{8}$/.test(value)
});
//限制只能输入一个小数点，并且第一位和最后一位不能是小数点
extend('digitalTest', {
    message: () => 'Wrong cell phone number, please correct',
    validate: value => /^(0|[1-9][0-9]*?)(\.[0-9]{1,2})?$/.test(value)
});
//living数组校验
extend('livingTest', {
    message: () => "Can not be empty",
    validate: value => value.length > 0
});
/*---------------------自定义校验规则 end-------------------------------*/