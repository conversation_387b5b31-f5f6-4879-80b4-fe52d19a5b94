import CMoney from '@/components/c-money.vue';
import { mapState } from 'vuex';
import { gotoHomeActivity, gotoLoanList } from "@/assets/js/native";
import complete from '@/assets/images/bg-complete.jpg';
import overdue from '@/assets/images/bg-overdue.jpg';
import api from "@/api/interface";



export default {
  name: 'loanDetail',
  components: {
    CMoney
  },
  computed: mapState(['statusObj']),
  data() {
    return {
      loanId: '',
      complete,
      overdue,
      loanDetail: {} // 借款详情
    };
  },

  methods: {
    getDueDate() {
      let self = this;
      if (self.loanDetail.repaySchduleList) {
        let len = self.loanDetail.repaySchduleList.length;
        if (len === 1) {
          return self.loanDetail.repaySchduleList[0].dueDate;
        } else {
          return self.loanDetail.repaySchduleList[len - 1].dueDate;
        }
      }
      
    },
    getBackImg(status) {
      console.log('status', status);
      if (status === 'N' || status === 'P' || status === 'F') {
        return this.complete;
      } else if (status === 'O') {
        return this.overdue;
      }
    },
    // 获取借款详情
    queryLoanDetils4nc() {
      let self = this;
      return new Promise((resolve, reject) => {
        self.$http(api.repayment.queryLoanDetils4nc, {
            data:{
              "loanId": self.loanId
            }
        }).then(res => {
          self.loanDetail = res;
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    },
    goRepayment() {
      let self = this;
      self.$router.push({
        path: '/repaymentIndex',
        query: {
          loanId: self.loanId
        }
      })
    },
    myBackFun() {
      gotoHomeActivity('loanDetail');
      gotoLoanList();
    },
    // 查看合同
    LoanAgreement() {
      let self = this;
      return new Promise((resolve, reject) => {
        self.$http(api.repayment.getContractPath4nc, {
            data:{
              needEncryptContract: true, // 需要获取base64的合同
              loanId: self.loanId
            }
        }).then(res => {
          self.loanDetail = res;
          self.$store.commit('SET_CONTRACT', self.$decode(res.content));
          self.$router.push({ name: "contract" });
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    }
  },

  mounted() {
    let self = this;
    self.loanId = self.$route.query.loanId;
    self.queryLoanDetils4nc();
    window.scrollTo(0, 0);
  },
};