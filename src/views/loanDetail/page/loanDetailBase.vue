<template>
  <div>
  <c-header titleName="Loan Detail" :backFun="myBackFun">
    <template #right>
      <div class="loan-agreement" @click="LoanAgreement()">Loan Agreement</div>
    </template>
  </c-header>
  <div class="loan-detail-history" v-if="loanDetail">
    <div class="con-status" :style="{'background-image': 'url(' + getBackImg(loanDetail.loanStatus) + ')'}">
      <div class="name">
        <div class="status">Contract Status</div>
        <div class="type" v-text="statusObj[loanDetail.loanStatus] && statusObj[loanDetail.loanStatus].name"></div>
      </div>
      <div class="time" v-if="loanDetail.overdueDays > 0">
        <div class="num" v-text="loanDetail.overdueDays"></div>
        <div class="days">Days</div>
      </div>
    </div>
    <div class="amount">
      <div class="title">Total Repayment Amount</div>
      <CMoney :currencyNum="common.thousandsToFixed(loanDetail.totalRepaymentAmt > 0 ? loanDetail.totalRepaymentAmt : loanDetail.loanAmt)"></CMoney>
      <div class="date">
        <div class="part">
          <div class="item">Sart Date</div>
          <div class="time" v-text="loanDetail.startDate"></div>
        </div>
        <div class="line"></div>
        <div class="part">
          <div class="item right">Due Date</div>
          <div class="time" v-text="getDueDate()"></div>
        </div>
      </div>
    </div>
    <ul class="detail-ul">
      <li class="li">
        <div class="name">Principal</div>
        <CMoney :currencyNum="common.thousandsToFixed(loanDetail.loanAmt)"></CMoney>
      </li>
      <li class="li">
        <div class="name">Interest</div>
        <CMoney :currencyNum="common.thousandsToFixed(loanDetail.interest)"></CMoney>
      </li>
      <!-- <li class="li">
        <div class="name">VAT</div>
        <CMoney :currencyNum="common.thousandsToFixed(loanDetail.vatfee)"></CMoney>
      </li> -->
      <li class="li">
        <div class="name">Service Charge</div>
        <CMoney :currencyNum="common.thousandsToFixed(loanDetail.serviceCharge)"></CMoney>
      </li>
      <li class="li">
        <div class="name">Penalty Charge</div>
        <CMoney :currencyNum="common.thousandsToFixed(loanDetail.penaltyCharge)"></CMoney>
      </li>
      <!-- <li class="li">
        <div class="name">Coupon</div>
        <CMoney className="coupon" :currencyNum="common.thousandsToFixed(0)"></CMoney>
      </li> -->
    </ul>
    <ul class="detail-ul">
      <li class="li">
        <div class="name">Contract ID</div>
        <div class="item" v-text="loanDetail.contractNo"></div>
      </li>
      <li class="li">
        <div class="name">Product Type</div>
        <div class="item" v-text="loanDetail.productName"></div>
      </li>
    </ul>
    <div class="action" v-if="loanDetail.loanStatus != 'F'">
      <div class="pay-detail">
        <CMoney className="coupon" :currencyNum="common.thousandsToFixed(loanDetail.totalRepaymentAmt)"></CMoney>
        <div class="repay-amount">Total Repayment Amount</div>
      </div>
      <div class="pay-now" @click="goRepayment">
        Pay Now
      </div>
    </div>
    </div>
  </div>
</template>

<script>
import publicMixns from '../loanDetailMixins.js'
export default {
    name: 'loanDetailBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.loan-agreement{
  font-size: 14px;
  font-family: Avenir, Avenir-Medium;
  font-weight: 500;
  text-align: right;
  color: #333333;
  line-height: 38px;
  margin-right: 18px;
}
.loan-detail-history{
  min-height: 100%;
  background: #edf0f6;
  margin-top: 56px;
  .con-status{
    height: 56px;
    background-size: 100% 80px;
    background-repeat: no-repeat;
    display: flex;
    padding: 12px 18px;
    justify-content: space-between;
    .name {
      .status{
        opacity: 0.7;
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        color: #ffffff;
        text-align: left;
        line-height: 16px;
      }
      .type {
        font-size: 24px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #ffffff;
        line-height: 33px;
      }
    }
    .time{
      width: 60px;
      height: 60px;
      background: #ffffff;
      border-radius: 60px;
      .num{
        font-size: 18px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #e84e40;
        line-height: 25px;
        margin-top: 10px;
      }
      .days{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #e84e40;
        line-height: 16px;
        margin-top: -4px;
      }
    }
  }
  .amount{
    padding: 8px 18px;
    text-align: left;
    background: #ffffff;
    .title {
      font-size: 12px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: #919db3;
      line-height: 16px;
    }
    ::v-deep .c-money{
      .monetary-unit{
        transform: scale(1);
        font-size: 20px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #1b3155;
        line-height: 27px;
      }
      .currency-num{
        font-size: 48px;
        font-family: DINAlternate, DINAlternate-Bold;
        font-weight: 700;
        text-align: left;
        color: $themeColor;
        line-height: 66px;
      }
    }
    .date{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 22px;
      .part{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 16px;
        .item{
          color: #919db3;
          &.right{
            text-align: right;
          }
        }
        .time{
          color: #1b3155;
          margin-top: 5px;
        }
      }
      .line{
        width: 39px;
        height: 2px;
        background: #919db3;
      }
    }
  }
  .detail-ul{
    padding: 8px 18px;
    text-align: left;
    background: #ffffff;
    margin-top: 8px;
    .li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name{
        font-size: 14px;
        color: #919DB3;
        line-height: 30px;
      }
      ::v-deep .c-money{
        position: relative;
        font-weight: 700;
        display: flex;
        .monetary-unit{
          position: absolute;
          top: 3px;
          left: -5px;
          color: #1b3155;
        }
        .currency-num{
          font-family: DINAlternate-Bold;
          font-size: 14px;
          text-align: right;
          color: #1b3155;
        }
      }
      .item{
        font-size: 14px;
        font-family: DINAlternate, DINAlternate-Bold;
        font-weight: 700;
        text-align: right;
        color: #1b3155;
        line-height: 16px;
      }
    }
  }
  .action{
    position: fixed;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    width: 100%;
    background: #ffffff;
    height: 50px;
    align-items: center;
    .pay-detail{
      padding-left: 18px;
      ::v-deep .c-money{
        font-weight: 800;
        .monetary-unit{
          font-size: 18px;
          font-family: Avenir, Avenir-Heavy;
          color: #e84e40;
        }
        .currency-num{
          font-size: 18px;
          font-family: Avenir, Avenir-Heavy;
          color: #e84e40;
        }
      }
      .repay-amount{
        opacity: 0.7;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #919db3;
        font-size: 12px;
        transform: scale(0.84);
        display: inline-block;
        margin-left: -10px;
      }
    }
    .pay-now{
      width: 120px;
      height: 50px;
      background: #e84e40;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      font-family: Avenir, Avenir-Heavy;
      font-weight: 800;
      color: #ffffff;
    }
  }
}
</style>
