<template>
  <div class="loan-detail-container" :class="setBgStyleWayacredit(loanDetail.loanStatus)">
  <c-header titleName="Loan Detail" :backFun="myBackFun" :styType="'b'">
    <template #right>
      <div class="loan-agreement" @click="LoanAgreement()">Loan Agreement</div>
    </template>
  </c-header>
  <div class="loan-detail-history" v-if="loanDetail">
    <div class="con-status">
      <div class="name">
        <div class="status">Contract Status</div>
        <div class="type" v-text="statusObj[loanDetail.loanStatus] && statusObj[loanDetail.loanStatus].name"></div>
      </div>
      <div class="time" v-if="loanDetail.overdueDays > 0">
        <div class="num" v-text="loanDetail.overdueDays"></div>
        <div class="days">Days</div>
      </div>
    </div>
    <div class="amount">
      <div class="title">Total Repayment Amount</div>
      <CMoney :currencyNum="common.thousandsToFixed(loanDetail.totalRepaymentAmt > 0 ? loanDetail.totalRepaymentAmt : loanDetail.loanAmt)"></CMoney>
      <div class="date">
        <div class="part">
          <div class="item">Sart Date</div>
          <div class="time" v-text="loanDetail.startDate"></div>
        </div>
        <div class="line">
          <img :src="setIconWayacredit(loanDetail.loanStatus)" alt="">
        </div>
        <div class="part">
          <div class="item right">Due Date</div>
          <div class="time" v-text="getDueDate()"></div>
        </div>
      </div>
    </div>
    <div class="loan-detail-box">
      <ul class="detail-ul">
        <li class="li">
          <div class="name">Principal</div>
          <CMoney :currencyNum="common.thousandsToFixed(loanDetail.loanAmt)"></CMoney>
        </li>
        <li class="li">
          <div class="name">Interest</div>
          <CMoney :currencyNum="common.thousandsToFixed(loanDetail.interest)"></CMoney>
        </li>
        <!-- <li class="li">
          <div class="name">VAT</div>
          <CMoney :currencyNum="common.thousandsToFixed(loanDetail.vatfee)"></CMoney>
        </li> -->
        <li class="li">
          <div class="name">Service Charge</div>
          <CMoney :currencyNum="common.thousandsToFixed(loanDetail.serviceCharge)"></CMoney>
        </li>
        <li class="li" v-if="loanDetail.penaltyCharge !== 0">
          <div class="name">Penalty Charge</div>
          <CMoney :currencyNum="common.thousandsToFixed(loanDetail.penaltyCharge)"></CMoney>
        </li>
        <!-- <li class="li">
          <div class="name">Coupon</div>
          <CMoney className="coupon" :currencyNum="common.thousandsToFixed(0)"></CMoney>
        </li> -->
      </ul>
      <ul class="detail-ul tow-ul">
        <li class="li">
          <div class="name">Contract ID</div>
          <div class="item" v-text="loanDetail.contractNo"></div>
        </li>
        <li class="li">
          <div class="name">Product Type</div>
          <div class="item" v-text="loanDetail.productName"></div>
        </li>
      </ul>
    </div>
    <div class="action" v-if="loanDetail.loanStatus != 'F'">
      <div class="pay-detail">
        <div class="repay-amount">Total Repayment Amount</div>
        <CMoney className="coupon" :currencyNum="common.thousandsToFixed(loanDetail.totalRepaymentAmt)"></CMoney>
      </div>
      <div class="pay-now" @click="goRepayment">
        Pay Now <img class="icon-btn" src="@/assets/images/wayacredit/icon_btn.png" alt="">
      </div>
    </div>
    </div>
  </div>
</template>

<script>
import publicMixns from '../loanDetailMixins.js'
export default {
    name: 'loanDetailWayacredit',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    },
    methods: {
      setBgStyleWayacredit(status) {
        let str = ''
        if(status === 'F') {
          str = 'paid'
        } else if (status === 'N' || status === 'P') {
          str = 'normal'
        } else if(status === 'O') {
          str = 'overdue'
        }
        return str
      },
      setIconWayacredit(status) {
        let icon = ''
        if(status === 'F') {
          icon = require('@/assets/images/wayacredit/loan-paid.png')
        } else if (status === 'N' || status === 'P') {
          icon = require('@/assets/images/wayacredit/loan-normal.png')
        } else if(status === 'O') {
          icon = require('@/assets/images/wayacredit/loan-overdue.png')
        }
        return icon
      }
    }
}
</script>

<style lang="scss" scoped>
.loan-detail-container {
  width: 100%;
  height: 100%;
  background: #1E1E20;
  padding-bottom: 90px;
  padding-top: 56px;
  box-sizing: border-box;
}
.loan-agreement{
  font-size: 14px;
  font-family: Avenir, Avenir-Medium;
  font-weight: 500;
  text-align: right;
  color: #fff;
  line-height: 38px;
  margin-right: 18px;
}
.loan-detail-history{
  height: 100%;
  overflow-y: auto;

  .con-status{
    height: 105px;
    display: flex;
    padding: 12px 18px;
    justify-content: space-between;
    margin-left: 20px;
    margin-right: 20px;
    box-sizing: border-box;
    .name {
      .status{
        opacity: 0.7;
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        color: #ffffff;
        text-align: left;
        line-height: 16px;
      }
      .type {
        font-size: 24px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #ffffff;
        line-height: 33px;
      }
    }
    .time{
      width: 60px;
      height: 60px;
      background: #ffffff;
      border-radius: 60px;
      .num{
        font-size: 18px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #e84e40;
        line-height: 25px;
        margin-top: 10px;
      }
      .days{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #e84e40;
        line-height: 16px;
        margin-top: -4px;
      }
    }
  }
  .amount{
    padding: 24px;
    text-align: left;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.50);
    background: #26282D;
    margin: -20px 20px 12px 20px;
    .title {
      color: rgba(255, 255, 255, 0.70);
      font-family: HarmonyOS Sans SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }
    ::v-deep .c-money{
      border-bottom: 1px solid rgba(255, 255, 255, 0.30);
      .monetary-unit{
        transform: scale(1);
        font-family: HarmonyOS Sans SC;
        font-size: 32px;
        font-style: normal;
        font-weight: 900;
        color: $themeColor;
      }
      .currency-num{
        font-family: HarmonyOS Sans SC;
        font-size: 32px;
        font-style: normal;
        font-weight: 900;
        text-align: left;
        color: $themeColor;
      }
    }
    .date{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 22px;
      .part{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 16px;
        .item{
          color: rgba(255, 255, 255, 0.70);
          font-family: Noto Sans;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          &.right{
            text-align: right;
          }
        }
        .time{
          color: #FFF;
          font-family: HarmonyOS Sans SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          margin-top: 5px;
        }
      }
      .line{
        img {
          width: 23px;
          height: 23px;
        }
      }
    }
  }
  .loan-detail-box {
    padding: 24px;
    text-align: left;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.50);
    background: #26282D;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 30px;
  }
  .tow-ul {
    border-top: 1px solid rgba(255, 255, 255, 0.30);
    padding-top: 23px;
    .li:last-child {
      margin-bottom: 0;
    }
  }
  .detail-ul{
    .li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 23px;
      .name{
        color: #FFF;
        font-family: HarmonyOS Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
      ::v-deep .c-money{
        position: relative;
        font-weight: 700;
        display: flex;
        .monetary-unit{
          color: #FFF;
          font-feature-settings: 'clig' off, 'liga' off;
          font-family: HarmonyOS Sans SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
        .currency-num{
          color: #FFF;
          font-feature-settings: 'clig' off, 'liga' off;
          font-family: HarmonyOS Sans SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
      .item{
        font-size: 14px;
        font-family: DINAlternate, DINAlternate-Bold;
        font-weight: 700;
        text-align: right;
        color: #fff;
        line-height: 16px;
      }
    }
  }
  .action{
    position: fixed;
    left: 22px;
    bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;
    background: #FFF;
    width: calc(100% - 44px);
    padding: 10px 0;
    .pay-detail{
      margin-left: 10px;
      ::v-deep .c-money{
        .monetary-unit{
          color: #1E1E20;
          font-family: Impact;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }
        .currency-num{
          color: #1E1E20;
          font-family: Impact;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
        }
      }
      .repay-amount{
        color: #1E1E20;
        text-align: left;
        font-family: HarmonyOS Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
      }
    }
    .pay-now{
      margin-right: 10px;
      color: #1E1E20;
      text-align: right;
      font-family: HarmonyOS Sans SC;
      font-style: normal;
      font-weight: 400;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      .icon-btn {
        display: inline-block;
        width: 18px;
        height: 18px;
        margin-left: 8px;
      }
    }
  }
}

.normal {
  .con-status {
    border-radius: 15px 15px 0px 0px;
    border: 1px solid #F4A200;
    background: rgba(244, 162, 0, 0.10);
  }
  .amount {
    ::v-deep .c-money{
      .currency-num {
        color: #F4A200;
      }
      .monetary-unit {
        color: #F4A200;
      }
    }
  }

}
.overdue {
  .con-status {
    border-radius: 15px 15px 0px 0px;
    border: 1px solid #FF2626;
    background: rgba(255, 38, 38, 0.10);
  }

  .amount {
    ::v-deep .c-money{
      .currency-num {
        color: #FF2626;
      }
      .monetary-unit {
        color: #FF2626;
      }
    }

  }
}
.paid {
  padding-bottom: 0;
  .con-status {
    border-radius: 15px 15px 0px 0px;
    border: 2px solid #00FFAD;
    background: rgba(0, 255, 173, 0.10);
  }
  .amount {
    ::v-deep .c-money{
      .currency-num {
        color: #00FFAD;
      }
      .monetary-unit {
        color: #00FFAD;
      }
    }
  }

}
</style>
