
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'loanDetail',
  components: {
    baseLoanDetail: () => import(/* webpackChunkName: "baseld" */ './page/loanDetailBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "loanDetailWayacredit" */ './page/loanDetailWayacredit.vue'),
    loanDetailCommon1: () => import(/* webpackChunkName: "loanDetailCommon1" */ './page/loanDetailCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseLoanDetail',
        to: '',
        from: '',
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.loanDetail) {
      this.componentTag = globalConfig.pageStyleSetting.loanDetail
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
