import { mapState } from 'vuex';
import { gotoHomeActivity, gotoGooglePlay, openAppStore } from "@/assets/js/native";

import reviewPopupBase from '@/components/review-popup.vue';
import reviewPopupWayacredit from '@/components/review-popup-wayacredit.vue';
import addAccount from '@/components/add-account.vue';
import { judgeClient } from '@/assets/js/common';
import api from "@/api/interface";
const product = localStorage.getItem('productSource')
let reviewPopup = '';
if (product === 'wayacredit') {
  reviewPopup = reviewPopupWayacredit;
} else {
  reviewPopup = reviewPopupBase;
}
export default {
  name: 'cardStatus',
  computed: {
    ...mapState([ 'productSource', 'channelPackageName', 'notComplaintArr']),
    showNoVipCoupon() {
      if (this.status === 'SUCCESS' || (this.status === 'FAILED' && this.skip)) {
        // 绑定失败状态下，为跳过状态且是来自提现的绑卡，正常显示。视为可以提现的状态
        return this.bindCardType.includes('widthdrawAddBankCard')
      }
      return false;
    }
  },
  components: {
    reviewPopup,
    addAccount
  },
  data() {
    return {
      statusObj: {
        SUCCESS: {
          name: 'Success',
          tips: 'Your bank card has been linked',
          actionName: ''
        },
        FAILED: {
          name: 'Failed',
          tips: '',
          actionName: 'Try Again'
        }
      },
      status: '',
      bindCardType: '', // 缓存的绑卡来源类型  widthdrawAddBankCard 提现绑卡  widthdrawStatusAddBankCard 提现结果页绑卡
      timer: null,
      count: 3,
      showReviewPopup: false,
      hidden: false,
      reviewPopupTitle: '',
      reviewPopupContent: '',
      fromName: '',
      device: '',
      showBanner: false, // 是否显示绑卡/绑账户banner
      cardType: '', // 绑卡类型： account为账户 其他情况没有注明则为绑卡
      skip: false, // 是否是绑卡跳过
    };
  },

  methods: {
    goIndex(type, secondType) {
      if (secondType === 'noVipCoupon') {
        if (this.cardType === 'account') {
          this.report('bindcard_success_novipcoupon_click');
        } else {
          this.report('bindaccount_success_novipcoupon_click');
        }
      }
      if (this.bindCardType.includes('widthdrawStatusAddBankCard')) {
        gotoHomeActivity('cardStatus');
      } else {
        this.$router.push({ path: '/', query: {
          cardStatus: type
        }}); // 跳转回首页
      }
    },
    // 跳转绑卡中台的逻辑
    tryAgain() {
      let vm = this;
      let obj = {};
      if (vm.productSource === 'palmcredit') {
        obj.businessChannel = 'palmcreditnew';
      }
      let param  = {
        scene: "2"
      }
      let bindCardType = localStorage.getItem('bindCardType');
      if (bindCardType.includes('secondAddBankCard') || bindCardType.includes('secondAddBankAccount')) {
        param.scene = "12"
        if (bindCardType.includes('secondAddBankCard')) {
          param.cardType = '';
        } else {
          param.cardType = 'account';
        }
      }
      if (this.cardType === 'account') {
        vm.report('bindaccount_fail_tryagain_click');
      } else {
        vm.report('bindcard_fail_tryagain_click');
      }
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.routeRedirect, {
          "data":{
            ...param,
            ...obj
          } // 目前是写死的
        }).then(res => {
          // 跳转绑卡中台的地址
          location.href = res.redirectUrl;
          resolve();
        }).catch(e => {
          reject(e);
        })
      })
    },
    // 埋点
    report(eventName) {
      let self = this;
      self.$store.dispatch('reportEvent', {
        page: 'cardStatus',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    hidePopup(obj) {
      const vm = this;
      vm.showReviewPopup = false;
      if (obj.type === 'confirm') {
        vm.report('confirm_click');
        vm.$loading();
        vm.saveStarsScore(obj.score).then(() => {
            vm.jumpAPPStore(obj.score);
        }).catch(() => {
            vm.jumpAPPStore(obj.score);
        })
      } else {
        vm.report('close_click');
      }
    },
    jumpAPPStore(score) {
        const vm = this;
        if (score <= 3) {
          vm.$hideLoading();
          // 新包客诉功能的关闭
          if (!this.notComplaintArr.includes(vm.productSource)) {
            vm.$router.push({ path: '/complaint'})
          }
        } else {
          vm.hidden = false;
          document.addEventListener('visibilitychange', vm.visibilityFun, false);
          if (vm.device === 'Android') {
            // 打开对应的google play页面
            gotoGooglePlay(vm.channelPackageName);
          } else {
            openAppStore();
          }
          // 如果没有打开，则调用以下逻辑。
          setTimeout(() => {
              vm.$hideLoading();
              if (!vm.hidden) {
                if (vm.device === 'Android') {
                  location.href = `https://play.google.com/store/apps/details?id=${vm.channelPackageName}`
                }
              } else {
                  document.removeEventListener('visibilitychange', vm.visibilityFun, false);
              }
          }, 2000);
        }
    },
    visibilityFun() {
      let vm = this;
      if (document.hidden) {
        vm.hidden = true;
      } else {
        vm.hidden = false;
      }
    },
    starsPopup() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.starsPopup, {
        "data":{
          scene: 'singleSwitch'
        }
        }).then(res => {
        resolve(res);
        }).catch(e => {
        reject(e);
        })
      })
    },
    // 保存用户分数
    saveStarsScore(score) {
        let vm = this;
        return  new Promise((resolve, reject) => {
            vm.$http(api.commonBlc.saveStarsScore, {
                "data":{
                    score: score
                }
            }).then(res => {
            resolve(res);
            }).catch(e => {
            reject(e);
            })
        })
    },
    beforeRouteEnter (to, from) {
      this.fromName = from.name;
    },
    updateBindCard(cardType) {
      if (this.cardType === 'account') {
        if (this.status === 'FAILED') {
          this.report('bindaccount_fail_banner_click');
        } else {
          this.report('bindaccount_success_banner_click');
        }
      } else {
        if (this.status === 'FAILED') {
          this.report('bindcard_fail_banner_click');
        } else {
          this.report('bindcard_success_banner_click');
        }
      }
      let bindCardType = localStorage.getItem('bindCardType');
      // 这里cardType是banner的cardType。也就是是实际banner点击的类型值
      const newBindCardType = cardType === 'account' ? 'secondAddBankAccount' : 'secondAddBankCard';
      // 缓存第二次绑卡/绑账户跳转，用于回调判断
      // 会存在第二次绑卡失败的情况，所以每次点击banner都要更新
      if (bindCardType.includes('widthdrawStatusAddBankCard')) {
        bindCardType = `widthdrawStatusAddBankCard,${newBindCardType}`;
      }
      if (bindCardType.includes('widthdrawAddBankCard')) {
        bindCardType = `widthdrawAddBankCard,${newBindCardType}`;
      }
      localStorage.setItem('bindCardType', bindCardType);
    }
  },

  mounted() {
    let vm = this;
    vm.beforeRouteEnter(vm.$parent.to, vm.$parent.from);
    vm.device = judgeClient();
    vm.status = vm.$route.query.status;
    const queryObj = vm.$route.query.queryObj ? JSON.parse(vm.$route.query.queryObj) :  {};
    vm.showBanner = queryObj.showBanner;
    console.log('vm.showBanner', vm.showBanner, 'queryObj', queryObj);
    vm.cardType = queryObj.cardType;
    vm.skip = queryObj.skip;
    console.log('skip', vm.skip);
    const bindCardType = localStorage.getItem('bindCardType');
    if (bindCardType) {
      vm.bindCardType = bindCardType;
    }
    if (vm.cardType === 'account') {
      if (vm.status === 'FAILED') {
        vm.report('bindaccount_fail_page_status_view');
        vm.report('bindaccount_fail_tryagain_view');
        if (vm.showBanner) {
          vm.report('bindaccount_fail_banner_view');
        }
      } else {
        vm.report('bindaccount_success_page_status_view');
        if (!vm.bindCardType.includes('widthdrawStatusAddBankCard')) {
          vm.report('bindaccount_success_novipcoupon_view');
        }
        if (vm.showBanner) {
          vm.report('bindaccount_success_banner_view');
        }
      }
    } else {
      if (vm.status === 'FAILED') {
        vm.report('bindcard_fail_page_status_view');
        vm.report('bindcard_fail_tryagain_view');
        if (vm.showBanner) {
          vm.report('bindcard_fail_banner_view');
        }
      } else {
        vm.report('bindcard_success_page_status_view');
        if (!vm.bindCardType.includes('widthdrawStatusAddBankCard')) {
          vm.report('bindcard_success_novipcoupon_view');
        }
        if (vm.showBanner) {
          vm.report('bindcard_success_banner_view');
        }
      }
    }
    // 失败状态下，使用后台的提示文字
    if (vm.status === 'FAILED') {
      vm.statusObj.FAILED.tips = vm.$route.query.message ? vm.$route.query.message : ''
    }
    // 来自提现结果页的绑卡，展示五星好评。
    if (vm.bindCardType.includes('widthdrawStatusAddBankCard') && vm.fromName !== 'complaint') {
      vm.starsPopup().then(res => {
          vm.reviewPopupTitle = res.showStarsTitle;
          vm.reviewPopupContent = res.showStarsContent;
          vm.showReviewPopup = res.showStarsPopup;
      });
      vm.report('five_star_activity');
    }
    if (vm.status === 'SUCCESS' && vm.bindCardType === 'widthdrawAddBankCard') {
      // vm.timer = setInterval(function() {
      //   vm.count = vm.count - 1;
      //   if (vm.count == 0) {
      //     clearInterval(vm.timer);
      //     vm.goIndex('success');
      //   }
      // }, 1000);
    }
    window.scrollTo(0, 0);
  }
};