
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'cardStatus',
  components: {
    baseCardStatus: () => import(/* webpackChunkName: "basecrdSt" */ './page/cardStatusBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "cardStatusWayacredit" */ './page/cardStatusWayacredit.vue'),
    cardStatusCommon1: () => import(/* webpackChunkName: "cardStatusCommon1" */ './page/cardStatusCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseCardStatus',
        to: '',
        from: '',
      }
  },
  created() {
      if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.cardStatus) {
        this.componentTag = globalConfig.pageStyleSetting.cardStatus
      }

  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
