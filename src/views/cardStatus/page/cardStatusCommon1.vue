<template>
  <div class="status">
    <div class="next" v-if="status === 'SUCCESS' && bindCardType.includes('widthdrawStatusAddBankCard')" @click="goIndex('success')">Next</div>
    <img class="img" :src="status === 'FAILED' ? require(`@/assets/images/failure.png`) : require(`@/assets/images/${productSource}/success.png`)" alt="">
    <template v-if="status && statusObj[status]">
        <div class="name" v-text="statusObj[status].name"></div>
        <div class="tips" v-html="statusObj[status].tips"></div>
        <div class="action" v-if="status === 'FAILED'" @click="tryAgain()" v-text="statusObj[status].actionName"></div>
    </template>
    <addAccount :cardType="cardType" @bindCard="updateBindCard"  v-if="showBanner" />
    <div class="back-home" v-if="status === 'FAILED' && !showNoVipCoupon" @click="goIndex('failed')" >Back Homepage</div>
    <div class="back-home success" v-if="showNoVipCoupon" @click="goIndex('success', 'noVipCoupon')" >Back</div>
    <reviewPopup :show="showReviewPopup" :title="reviewPopupTitle" :content="reviewPopupContent" @hidePopup="hidePopup"></reviewPopup>
  </div>
</template>

<script>
import publicMixns from '../cardStatusMixins.js'
export default {
    name: 'cardStatusCommon1',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
  .status{
    width: 100%;
    height: 100%;
    .next{
      height: 56px;
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: right;
      color: $color;
      line-height: 56px;
      margin-right: 18px;
    }
    .img{
      width: 160px;
      height: 100px;
      margin-top: 30px;
    }
    .name{
      font-size: 24px;
      font-family: Avenir, Avenir-Heavy;
      font-weight: 800;
      text-align: center;
      color: #1b3155;
      line-height: 33px;
      margin-top: 4px;
    }
    .tips{
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: center;
      color: #919db3;
      line-height: 18px;
      margin-top: 7px;
      margin-top: 5px;
      margin-left: 25px;
      margin-right: 25px;
    }
    .action{
      height: 42px;
      background: $background;
      border-radius: 7px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      width: 324px;
      margin: 0 auto;
      margin-top: 25px;
    }
    .back-home{
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: CENTER;
        color: $color;
        margin-top: 18px;
        &.success{
          text-decoration-line: underline;
        }
    }
    .add-account{
      margin-top: 20px;
    }
  }
</style>