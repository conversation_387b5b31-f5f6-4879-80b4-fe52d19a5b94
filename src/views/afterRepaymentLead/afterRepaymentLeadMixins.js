import { gotoHomeActivity } from "@/assets/js/native";

export default {
  name: 'afterRepaymentLeadBase',
  data() {
    return {
      num: 2000
    };
  },

  mounted() {
    this.report('vip_page_activity');
    this.mumAutoPlusAnimation({
      time: 1500,
			num: 200000,
			regulator: 33
    });
  },

  methods: {
    back() {
      gotoHomeActivity('afterRepaymentLead')
    },
    goToWithdraw() {
      this.report('vip_getnow_activity_click');
      this.$router.push({ path: '/'});
    },
    // 埋点
    report(eventName) {
      let self = this;
      self.$store.dispatch('reportEvent', {
        page: 'afterRepaymentLead',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    //数字自增到某一值动画参数（自定义配置）
		mumAutoPlusAnimation(options) {
				options = options || {};
				let	time = options.time; //总时间--毫秒为单位
				let	finalNum = options.num; //要显示的真实数值
				let	regulator = options.regulator || 100; //调速器，改变regulator的数值可以调节数字改变的速度
 
				let	step = finalNum / (time / regulator);/*每30ms增加的数值--*/
				let	count = 0; //计数器
				let	initial = this.num;
 
				const timer = setInterval(() => {
 
					count = count + step;
 
					if(count >= finalNum) {
						clearInterval(timer);
						count = finalNum;
					}
					var t = Math.floor(count);
					if(t == initial) return;
 
					initial = t;
 
					this.num = this.common.singleToThousands(initial);
				}, 30);
			}
  },
};