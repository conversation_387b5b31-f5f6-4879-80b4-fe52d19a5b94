<template>
  <div class="after-repayment-lead">
    <div class="vip">
      <img class="back-img" src="@/assets/images/back.png" @click="back()" alt=""  />
      <img class="crown" src="@/assets/images/page/repayment/crown.png" alt="">
      <div class="congratulations">Congratulations on getting</div>
      <div class="vip-pri">VIP privileges</div>
      <img class="confirm" src="@/assets/images/page/repayment/confirm.png" alt="">
    </div>
    <div class="content">
      <div class="tips">Your new credit limit Up to </div>
      <div class="money-animation">
        <div class="currency">₦</div>
        <div class="num" v-text="num"></div>
      </div>
    </div>
    <div class="btn" @click="goToWithdraw">
      <div class="inner">Get Money Now</div>
    </div>
  </div>
</template>

<script>
import publicMixns from '../afterRepaymentLeadMixins.js'
export default {
    name: 'afterRepaymentBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.after-repayment-lead{
  .vip{
    background-image: url(../../../assets/images/page/repayment/lead-bg.png);
    height: 250px;
    width: 100%;
    background-size: 100% 250px;
    background-repeat: no-repeat;
    position: relative;
    .back-img{
      width: 24px;
      height: 24px;
      position: absolute;
      left: 15px;
      top: 17px;
    }
    .crown{
      width: 100px;
      height: 45px;
      margin-top: 40px;
    }
    .congratulations{
      font-family: Montserrat;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      background: linear-gradient(180deg, #FFF 0%, #D1A23F 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 5px;
    }
    .vip-pri{
      font-family: Montserrat;
      font-size: 36px;
      font-style: normal;
      font-weight: 800;
      text-transform: capitalize;
      background: linear-gradient(180deg, #FFF 0%, #FAE864 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .confirm{
      width: 82px;
      height: 82px;
      margin-top: 20px;
    }
  }
  .content{
    .tips{
      color: #000;
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-transform: capitalize;
      margin-top: 12px;
    }
    .money-animation{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 5px;
      .currency{
        color: #B4921C;
        text-shadow: 0px 5px 10px rgba(0, 0, 0, 0.20);
        font-family: Inter;
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        margin-bottom: -28px;
      }
      .num{
        color: #B4921C;
        font-family: Inter;
        font-size: 60px;
        font-style: normal;
        font-weight: 800;
      }
    }
  }
  .btn{
    width: 276px;
    height: 50px;
    border-radius: 50px;
    background: #926B1B;
    position: fixed;
    left: calc((100% - 276px) / 2);
    bottom: 150px;
    .inner{
      width: 276px;
      height: 50px;
      border-radius: 50px;
      background: linear-gradient(180deg, #FFE870 0%, #BB8B22 100%);
      position: absolute;
      bottom: 6px;
      color: #000;
      text-align: center;
      font-family: Inter;
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 50px;
    }
  }
}
</style>