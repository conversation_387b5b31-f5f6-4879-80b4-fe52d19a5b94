
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
export default {
  name: 'afterRepayment',
  components: {
    baseAfterRepaymentLead: () => import(/* webpackChunkName: "baseafR" */ './page/afterRepaymentLeadBase.vue'),
  },
  data() {
    return {
      componentTag: 'baseAfterRepaymentLead',
      to: '',
      from: '',
    }
  },
  created() {
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
