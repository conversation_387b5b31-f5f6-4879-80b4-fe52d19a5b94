
import loanAmountInput from '@/components/loan-amount-input.vue';
import loanTerms from '@/components/loan-terms.vue';
import newLoanDetail from '@/components/new-loan-detail.vue';
import { mapState } from 'vuex';
import { guid, dateFormat } from '@/assets/js/common';
import api from "@/api/interface";
import backImg from '@/assets/images/UBA.png';
import ninBankImg from '@/assets/images/nin.png';
import goldManImg from '@/assets/images/goldMan.png';
import CButton from '@/components/c-button.vue';
import { 
  getGpsInfo, 
  getBatchNo, 
  getWifiList, 
  getDeviceId, 
  getLocation, 
  gotoHomeActivity, 
  activateTradeBigdata, 
  activateTradeBigdataAC, 
  getCustId
} from "@/assets/js/native";

export default {
  name: 'bookNextLoanBase',
  components: {
    CButton,
    loanTerms,
    newLoanDetail,
    loanAmountInput,
  },
  computed: {
    ...mapState(['productSource', 'deviceType', 'addHeader',])
  },
  data() {
    return {
      backImg,
      ninBankImg,
      goldManImg,
      amount: '', // 额度
      oldAmount: '', // 缓存的输入额度
      tempAddLimit: '',
      loanCaculateObj: { // 复借计划计算详情
        type: 'prefer',
        caculateStatus: false, // 试算状态
        checkShow: '' // 是否默认勾选保险费
      },
      selectedProductIndex: -1, // 产品勾选索引
      selectedInfor: { // 用户选择产品信息
        chooseTypeData: { // 选择的产品对应的最大值和最小值区间
          minAmount: 0,
          maxAmount: 0
        },
      },
      productList: [], // 产品列表
      deviceId: '',
      wifiList: '',
      lastLoanAcctInfo: { // 上次借款使用的账户信息
        bankCode: '',
        bankAcctNo: '',
        bankName: '',
        acctImg: ''
      }
    };
  },

  created() {
    this.initNativeData();
  },

  mounted() {
    this.report('bookloan_activity');
    this.getLoanInfor();
  },

  methods: {
    myBackFun() {
      gotoHomeActivity('bookNextLoan')
    },
    // 埋点
    report(eventName) {
      let self = this;
      self.$store.dispatch('reportEvent', {
        page: 'bookloan_activity',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    // blur触发的试算逻辑
    blurLoanCalculate() {
      if (this.selectedProductIndex === -1) return;
      const product = this.productList[this.selectedProductIndex];
      const productFee = product.productFee[0];
      const maxAmount = productFee.maxAmt;
      const minAmount = productFee.minAmt;
      const amount = this.getAmount();
      // 输入完成后，判断输入的值是否处于区间内，不在区间内，则设置为最大值。
      if ((maxAmount < amount || minAmount > amount)) {
        this.amount = this.thousands(maxAmount);
        if (maxAmount < amount) {
          this.$toast({
            message: `<div style="width: 240px">Withdraw amount is ₦${minAmount}-₦${maxAmount}</div>`,
            type: 'html'
          });
        } else {
          this.$toast({
            message: `<div style="width: 240px">At least ₦${this.common.singleToThousands(minAmount)}</div>`,
            type: 'html'
          });
        }
      }
      // 触发试算
      this.startLoanApplyCalc();
    },
    // 提现金额监听
    loanKeyUp(obj) {
      let value = +(obj.amount ? obj.amount.replace(/,/g, '') : 0);
      if (value) {
        if (value > this.selectedInfor.chooseTypeData.maxAmount) {
          this.$toast({
            message: `<div style="width: 200px">At most ₦${this.common.singleToThousands(this.selectedInfor.chooseTypeData.maxAmount)}</div>`,
            type: 'html'
          });
          console.log('this.oldAmount', this.oldAmount);
          this.amount = this.oldAmount;
        } else {
          this.amount = this.common.singleToThousands(value);
        }
      } else {
        this.amount = 0;
      }
    },
    // 输入控制
    keyDown(e) {
      let keyCode = e.keyCode;
      if ((keyCode < 48 || keyCode > 57) && keyCode != 8) {
        e.preventDefault();
      }
    },
    // 选择分期产品
    chooseLoanTerm(param) {
      this.selectedProductIndex = param.index;
      this.showProductList = false;
      const product = this.productList[this.selectedProductIndex];
      const productFee = product.productFee[0];
      this.selectedInfor.chooseTypeData.maxAmount = productFee.maxAmt;
      this.selectedInfor.chooseTypeData.minAmount = productFee.minAmt;
      if (productFee.tempAddLimit > 0) { // 提额金额
        this.tempAddLimit = productFee.tempAddLimit;
        this.report('reloan_credit_increase_label_view');
      }
      // 设置输入框金额
      this.amount = this.thousands(this.selectedInfor.chooseTypeData.maxAmount);
      // 当前产品是否有保险费
      if ((productFee.insuranceRate && productFee.insuranceRate.feeRate > 0) && this.loanCaculateObj.checkShow === '') {
        this.loanCaculateObj.checkShow = true;
      }
      this.startLoanApplyCalc();
      if (productFee.term === 1) {
        this.report(`new_loan_single_${product.type.replace(/\s/g, '_')}_days_click`);
      } else {
        this.report(`'new_loan_multi'_${product.type.replace(/\s/g, '_')}_days_click`);
      }
    },
    // 保险费勾选
    checkInsurance() {
      this.loanCaculateObj.checkShow = !this.loanCaculateObj.checkShow;
      this.startLoanApplyCalc()
    },
    // 借款试算
    startLoanApplyCalc() {
      if (this.selectedProductIndex === -1) return;
      const product = this.productList[this.selectedProductIndex];
      const productFee = product.productFee[0];
      // 保险费勾选情况
      const enableInsurance = (productFee.insuranceRate && productFee.insuranceRate.feeRate > 0) ? this.loanCaculateObj.checkShow : '';
      this.loanCaculateObj.caculateStatus = false;
      this.loanApplyCalc({
        loanAmt: this.getAmount(),
        loanSpan: product.borrowCount,
        loanTerm: productFee.term,
        loanType: product.loanType,
        needEncryptContract: true, // 需要获取base64的合同
        productFeeId: productFee.productFeeId,
        txnScene: product.txnScene,
        enableInsurance: enableInsurance
      }).then(res => {
        if (res.plans.length > 0) {
          res.plans.forEach(item => {
            item.termAmount = this.common.thousandsToFixed(item.termAmount);
            if (enableInsurance && item.termFeeList.length > 0) {
              item.insuranceAmountOutstd = item.termFeeList && item.termFeeList[0].INSURANCE_FEE;
            }
            item.dueDate = dateFormat(new Date(item.dueDate.replace(/-/g, '/')), 'MM/dd/yyyy');
          });
          res.loanCalcVOList.forEach(item => {
            if (item.key === 'Received') { // 若字段改变，后端也要变更这个字段
              this.loanCaculateObj.received = this.common.thousandsToFixed(item.value);
            }
            if (enableInsurance && item.key === 'Credit Protection Service') { // 若字段改变，后端也要变更这个字段
              this.loanCaculateObj.insuranceFee = this.common.thousandsToFixed(item.value || 0);
            }
          })
          this.loanCaculateObj.pltfFeeAmt = res.pltfFeeAmt > 0 ? this.common.thousandsToFixed(res.pltfFeeAmt) : '';
          this.loanCaculateObj.plans = res.plans;
          this.loanCaculateObj.showInsurance = productFee.insuranceRate && productFee.insuranceRate.feeRate > 0;
          this.loanCaculateObj.orderTime = res.orderTime;
          this.loanCaculateObj.deadline = res.deadline;
        }
        this.loanCaculateObj.caculateStatus = true;
      }).catch(e => {
        console.log(e);
      })
    },
    // 获取借款信息
    getLoanInfor() {
      this.$loading();
      this.queryCustRepayInfo().then((res) => {
        // 设置额度
        this.setAmount(res.loanAgainAmtResp.loanAmt);
        // 设置还款账户信息
        this.setAccountInfo(res.lastLoanAcctInfo);
        // 处理产品
        this.dealProductInfo(res.querySimpleProductResp);
        // 默认选择第一个产品
        if (this.productList.length > 0) {
          this.chooseLoanTerm({ index: 0 });
        }
        this.$hideLoading();
      }).catch(() => {
      })
    },
    // 设置还款账户信息
    setAccountInfo(lastLoanAcctInfo) {
      this.lastLoanAcctInfo = lastLoanAcctInfo;
      // 设置图片
      this.lastLoanAcctInfo.acctImg = this.getBankAcctImg(this.lastLoanAcctInfo);
    },
    // 设置额度
    setAmount(loanAmt) {
      // 复借额度初始化
      this.amount = this.thousands(loanAmt);
    },
    // 合并客户还款信息接口
    queryCustRepayInfo() {
      return  new Promise((resolve, reject) => {
        this.$http(api.repayment.queryCustRepayInfo, {
          data:{
            blcQueryIsUsedCouponReq: {},
            blcQueryCouponListReq: {},
            queryProductWithSerV5Req: {
              isReloanApply: true
            }
          },
          showErrorToast: false
        }).then(res => {
          resolve(res);
        }).catch(e => {
          // 无产品需要提示弹窗。
          if (e.code !== 1104016) {
            this.$toast(e.msg);
          }
          reject(e);
        });
      });
    },
    // 处理产品信息
    dealProductInfo(querySimpleProductResp) {
      let allProductList = querySimpleProductResp && querySimpleProductResp.productList;
      if (allProductList && allProductList.length > 0) {
        allProductList.forEach((item) => {
          let productFee = item.productFee[0];
          item.days = productFee.totalBorrowCount;
          item.type = `${item.days} days`;
          item.term = productFee.term;
          item.rate = productFee.dayRate * 100;
          if (item.productFee[0].term > 1) {
            item.tips = `Every ${ item.borrowCount } days in ${item.productFee[0].term} installment`;
          }
          this.report(`${item.type}_repayment_tenor_button_view`);
          return item;
        });
        // 分离单期和多期产品，分开展示以及排序
        let singleProductList = [];
        let muiltiProductList = allProductList.filter(item => {
          if (item.productFee[0].term === 1) {
              singleProductList.push(item);
          }
          return item.productFee[0].term > 1;
        });
        // 单期排序
        singleProductList.sort((a, b) => {
          let res = a.days - b.days;
          if (res > 0) {
            return -1
          } else if (res < 0) {
            return 1;
          } else {
            if (a.rate > b.rate) {
              return 1
            } else if (a.rate < b.rate) {
              return -1;
            } else {
              return 0;
            }
          }
        });
        // 多期排序
        muiltiProductList.sort((a, b) => {
          let res = a.days - b.days;
          if (res > 0) {
            return -1
          } else if (res < 0) {
            return 1;
          } else {
            if (a.rate > b.rate) {
              return 1
            } else if (a.rate < b.rate) {
              return -1;
            } else {
              return 0;
            }
          }
        });
        allProductList = [...muiltiProductList, ...singleProductList];
        // 若用户仅支持可见单期时，下方增加固定展示7*4和14*4多期产品
        if (muiltiProductList.length === 0 && singleProductList.length > 0) {
          const lockMuiltiProductList = [{
            tips: 'Every 7 days in 4 installment',
            type: '28 days',
            islock: true,
            productId: '999999999'
          }, {
            tips: 'Every 14 days in 4 installment',
            type: '56 days',
            islock: true,
            productId: '999999998'
          }];
          allProductList = [...allProductList, ...lockMuiltiProductList];
        }
        this.productList = allProductList;
      }
    },
    // 调用接口计算分期产品还款计划
    loanApplyCalc(data) {
      return  new Promise((resolve, reject) => {
        this.$http(api.commonBlc.loanCalculate4nc, {
          data:{
            ...data
          }
        }).then(res => {
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    thousands(num) {
      return this.common.singleToThousands(num);
    },
    // 提现金额获取
    getAmount() {
      const amount = this.amount;
      return +(amount ? amount.replace(/,/g, '') : 0);
    },
    startBook() {
      this.report('bookloan_booknow_click');
      if (this.selectedProductIndex === -1) {
        this.report(`book_next_loan_process_not_choose_tenor_error`);
        this.$toast({
          message: 'Please select loan tenor'
        });
        return;
      }
      let product = this.productList[this.selectedProductIndex];
      let productFee = product.productFee[0];
      let loanAgainGenList = [];
      const amount = this.getAmount();
      const chooseTypeData = this.selectedInfor.chooseTypeData;
      if (amount < chooseTypeData.minAmount || amount > chooseTypeData.maxAmount) {
        this.report(`go_withdraw_amount_${amount}_min_${chooseTypeData.minAmount}_max_${chooseTypeData.maxAmount}`);
        this.$toast({
          message: `<div style="width: 240px">Withdraw amount is ₦${chooseTypeData.minAmount}-₦${chooseTypeData.maxAmount}</div>`,
          type: 'html'
        });
        return;
      }
      let loanAgainGenObj = {
        acctType: product.acctType,
        deviceId: this.deviceId,
        loanAmt: amount,
        productFeeId: productFee.productFeeId,
        productId: product.productId,
        term: product.term,
        txnScene: product.txnScene,
        wifi: this.wifiList,
        priorityLevel: 1, // 1为首选产品
        enableInsurance: productFee.insuranceRate && productFee.insuranceRate.feeRate > 0 ? this.loanCaculateObj.checkShow : '', // 是否勾选保险费
        channelIsPCX: false // 是否是来自pcx的复借。
      }
      loanAgainGenList.push(loanAgainGenObj);
      this.$loading();
      this.loanAgainGenMultiOrder({
        entrySource: 2 , // 请求来源 1:还款复借页面 2:还款落地预约借款页面'
        loanType: 'AGAIN_LOAN', // AGAIN_LOAN表示还款再借
        loanAgainGenList: loanAgainGenList
      }).then(() => {
        this.$hideLoading();
        // 跳转到提现结果页
        this.$router.push({ path: "/withdrawStatus", query: {
          data: JSON.stringify({
            status: true,
            loanAmount: parseInt(amount),
            bankDetail: {
              bankCode: this.lastLoanAcctInfo.bankCode,
              bankAcctNo: this.lastLoanAcctInfo.bankAcctNo,
              bankName: this.lastLoanAcctInfo.bankName
            },
            loanTerm: productFee.term,
            repaymentDetailList: this.copyPlans(this.loanCaculateObj.plans),
            orderTime: this.loanCaculateObj.orderTime,
            dueDate: this.loanCaculateObj.deadline
          })
        }});
      }).catch(e => {
        console.log(e);
      });
    },
    copyPlans(plans) {
      const newPlans = JSON.parse(JSON.stringify(plans));
      newPlans.forEach(item => {
        const amount = item.termAmount;
        item.termAmount = +(amount ? amount.replace(/,/g, '') : 0);
      })
      return newPlans;
    },
    loanAgainGenMultiOrder(data) {
      return  new Promise((resolve, reject) => {
        this.$http(api.repayment.loanAgainGenMultiOrder, {
          addHeader: this.deviceType === 'AC' ? this.addHeader : {},
          data:{
            ...data
          }
        }).then(res => {
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    // 原生能力初始化
    initNativeData() {
      getDeviceId().then(deviceId => {
        this.deviceId = deviceId;
      });
      getWifiList().then(wifiList => {
        this.wifiList = wifiList || '';
      })
      if (this.deviceType !== 'AC') {
        this.initNative();
      } else {
        this.initNativeAc();
      }
    },
    // PC/NC端初始化
    initNative() {
      window.getGpsInfoCb = (res) => {
        this.gpsInfo = JSON.parse(this.$decode(res));
      }
      getGpsInfo('getGpsInfoCb'); // 回调
      // 需要调用这个activateTradeBigdata接口之后，才可以正常提现。
      const uid = guid();
      getCustId().then(custId => {
        console.log(uid, custId, 'activateTradeBigdataCb');
        if (custId) {
          activateTradeBigdata(uid, custId, 'activateTradeBigdataCb');
          console.log('activateTradeBigdataBvn', uid, custId, 'activateTradeBigdataCb');
        } else {
          const customerId = localStorage.getItem('customerId');
          activateTradeBigdata(uid, customerId, 'activateTradeBigdataCb');
          console.log('activateTradeBigdataNin', uid, customerId, 'activateTradeBigdataCb');
        }
      });
      window.activateTradeBigdataCb = (res) => {
        const result = JSON.parse(this.$decode(res));
        console.log('activateTradeBigdataCb', result, result.activateState === 'success');
        if (result.activateState === 'success') {
          this.activateState = true;
        } else {
          this.activateState = false;
          this.$toast({
            message: result.errMsg
          })
        }
      }
    },
    // 兼容逻辑，目前没有使用，保留逻辑。
    initNativeAc() {
      if (window.dopplerLib && window.dopplerLib.getGpsInfo) { // 新的GPS原生能力。
        window.getGpsInfoCb = (res) => {
          console.log('getGpsInfoCb', res);
          this.gpsInfo = JSON.parse(this.$decode(res));
          console.log('gpsInfo', this.gpsInfo);
        }
        getLocation('getGpsInfoCb'); // 回调
      } else {
        this.gpsInfo = JSON.parse(getLocation());
      }
      const batchNo = getBatchNo();
      const custId = this.userInfor.custId;
      activateTradeBigdataAC(batchNo, custId);
      window.setActivateTradeResult = (res) => {
        console.log('activateTradeBigdataRes', res, JSON.stringify(res));
        if (res === 'true') {
          console.log('test');
        } else {
          this.$toast({
            message: res.errMsg
          })
        }
      }
    },
    // 默认账户图片
    getBankAcctImg(bankAcct) {
      const isWalletWithdrawalUser = localStorage.getItem('userAccountType') === 'isWalletWithdrawalUser';
      if (!isWalletWithdrawalUser) {
        if (bankAcct.bankCode === '090574') {
          return this.goldManImg;
        } else {
          return this.backImg
        }
      } else {
        return this.ninBankImg
      }
    },
    accountSlice(account) {
      return account ? `(${account.slice(-4, account.length)})` : '';
    }
  }
};