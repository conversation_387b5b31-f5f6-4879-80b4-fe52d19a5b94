<template>
  <div class="book-next-loan">
    <c-header titleName="Book Next Loan" :backFun="myBackFun" />
    <div class="content">
      <div class="loan-amount">
        <div class="title">
          Loan Amount
          <div class="amount-add" v-if="tempAddLimit > 0">
            <div class="wrap">
              <div class="num" v-text="`Including ${common.singleToThousands(tempAddLimit)} limit increase`"></div>
              <div class="arrow"></div>
            </div>
          </div>
        </div>
        <!-- 输入框 -->
        <loanAmountInput
          key="prefer"
          :inputAmount="amount"
          :productInfo="selectedInfor"
          type="prefer"
          @report="report('new_loan_amount_click')"
          @startLoanCalculate="blurLoanCalculate"
          @loanKeyUp="loanKeyUp"
          @keyDown="keyDown"
        ></loanAmountInput>
      </div>
      <!-- 选择产品 -->
      <loanTerms
      v-model="selectedProductIndex"
      @chooseType="chooseLoanTerm"
      :productSource="productSource"
      customType='prefer'
      :productList="productList"></loanTerms>
      <!-- 借据详情 -->
      <newLoanDetail @checkInsurance="checkInsurance" :initShowDetail="true" :loanDetail="loanCaculateObj">
        <!-- 新增账户显示 -->
        <template v-slot:newItem v-if="lastLoanAcctInfo.bankCode">
          <div class="account_title">Disbursement Account</div>
          <div class="account">
            <div class="right">
              <img class="icon" :src="lastLoanAcctInfo.acctImg" />
              <div class="num" v-text="lastLoanAcctInfo.bankName + accountSlice(lastLoanAcctInfo.bankAcctNo)"></div>
            </div>
          </div>
          <div class="tips">
            <p>Your booked loan will be effective upon the successful settlement of your previous loan. </p>
            <p>The final loan amount is subject to the approved amount.</p>
          </div>
        </template>
      </newLoanDetail>
      <CButton @buttonClick="startBook" className="book-now"
          name="Book Now"></CButton>
    </div>
  </div>
</template>

<script>
import publicMixns from '../bookNextLoanMixins.js'
export default {
    name: 'bookNextLoan',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.book-next-loan{
  background: #F2F3F8;
  min-height: 100%;
  padding-top: 68px;
  .content{
    background: #FFFFFF;
    box-shadow: 0 1px 10px 0 rgb(0 54 101 / 6%);
    border-radius: 8px;
    margin: 0px 12px 12px 12px;
    padding: 0 18px;
    .loan-amount {
      text-align: left;
      padding: 8px 4px 8px;
      position: relative;
      .title{
        text-align: left;
        line-height: 12px;
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #C4CAD5;
        padding-top: 0px;
        img{
          width: 135px;
          height: 15px;
          margin-left: 5px;
        }
      }
      .input-amount{
        height: 50px;
        display: flex;
        align-items: flex-end;
        position: relative;
        .currency{
          margin-top: -19px;
          font-family: Avenir-Medium;
          font-size: 20px;
          color: #1B3155;
        }
        input{
          border: none;
          height: 35px;
          color: $themeColor;
          line-height: 35px;
          margin-left: 5px;
          width: calc(100% - 65px);
          outline: none;
          position: relative;
          font-family: DINAlternate-Bold;
          font-size: 35px;
          &::placeholder{
            font-size: 12px;
            font-weight: 600;
            color: rgb(177, 176, 176);
            position: absolute;
            top: 22px;
          }
        }
        img{
          width: 15px;
          height: 15px;
          position: absolute;
          right: 23px;
          top: 25px;
        }
      }
      .amount-add{
        position: absolute;
        left: 76px;
        top: 7px;
        transform: scale(.85);
        .wrap{
          position: relative;
          .num{
            background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
            color: #ffffff;
            width: auto;
            white-space: nowrap;
            padding: 2px 4px;
            font-size: 12px;
          }
          .arrow{
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 3.5px 5px 3.5px 0;
            border-color: transparent #C78900 transparent transparent;
            position: absolute;
            left: -5px;
            top: 5px;
          }
        }
      }
    }
    .new-loan-detail{
      padding-bottom: 10px;
      ::v-deep .more-details{
        display: none;
      }
      ::v-deep .account_title{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #536887;
        text-align: left;
        margin-top: 10px;
      }
      ::v-deep .account{
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 11px;
        margin-bottom: 10px;
        border: 1px solid #919DB3;
        border-radius: 8px;
        padding: 10px;
        .right{
          display: flex;
          align-items: center;
          justify-content: flex-start;
          color: #1B3155;
          font-family: Avenir;
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          img {
            flex: none;
            width: 40px;
            height: 40px;
            margin-right: 5px;
          }
        }
      }
      .tips{
        p{
          font-family: Avenir-Medium;
          font-size: 12px;
          color: #919DB3;
          text-align: left;
        }
      }
    }
    .book-now{
      position: fixed;
      bottom: 30px;
      left: calc((100% - 328px) / 2);
    }
  }
}
</style>
