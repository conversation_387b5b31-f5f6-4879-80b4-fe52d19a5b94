
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
export default {
  name: 'bookNextLoan',
  components: {
    bookNextLoanBase: () => import(/* webpackChunkName: "bookNextLoanBase" */ './page/bookNextLoanBase.vue'),
  },
  data() {
    return {
      componentTag: 'bookNextLoanBase',
      to: '',
      from: '',
    }
  },
  created() {
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
