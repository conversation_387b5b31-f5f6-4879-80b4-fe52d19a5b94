<template>
  <div>
    <c-header titleName="Bank Account" :backFun="myBack"></c-header>
    <div class="add-bank-account">
      <div class="tips">
        Kindly provide one of your own bank accounts that can receive the money.
      </div>
      <div class="bank" :class="{'not-bank': noBank}">
        <div class="warn" v-if="bank.bankCardName">Name of your bank</div>
        <div class="name" :class="{'new-name': bank.bankCardName}" @click="addBank()" v-text="bank.bankCardName ? bank.bankCardName : 'Name of your bank'"></div>
        <img v-if="!bank.bankCardName" :src="arrow" />
      </div>
      <div class="input" :class="{'focus': showFocus}">
        <div class="warn" v-show="showWarn && showFocus">Bank Account Number</div>
        <input ref="input" @focus="changeShow()" v-model="bankNumber" @keydown="keyDown($event)" @keyup="keyUp($event)" type="tel" placeholder="Bank Account Number">
        <div class="progress">
          <span :class="{'index': bankNumber.length < 10}" v-text="bankNumber ? bankNumber.length : 0"></span>
          <span>/10</span>
        </div>
      </div>
      <div class="tips">
        The bank linked to your own BVN
      </div>
      <div class="account bank">
        <div class="warn" v-if="bankAccount && !process">Name of your bank</div>
        <div v-show="process" class="name" :class="{'processing': process}">Processing</div>
        <div v-show="!process" class="name" :class="{'new-name': bankAccount}" v-text="bankAccount ? bankAccount : 'Account Name'"></div>
      </div>
      <div class="tips">
        <div>Tips: </div>
        <div>
          1. Add a bank account to receive your loan or bonus.
        </div>
        <div>
        2. Your account details are safely protected, please feel free to fill in.
        </div>
      </div>
      <div class="guar" v-text="`${channel} security guarantee`">
      </div>
      <div class="next" @click="addBankAccount4nc()">
        Next
      </div>
    </div>
    <van-dialog v-model="showDialog" show-cancel-button className="give-up" confirmButtonText="OK" cancelButtonText="give up" @cancel="giveUp">
        <div class="title">Are you sure to give up?</div>
        <div class="tips">Just one step to receive your money.</div>
    </van-dialog>
  </div>
</template>

<script>
import publicMixns from '../addBankAccountMixins.js'
export default {
    name: 'addBankAccountBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.add-bank-account{
  padding: 12px 18px;
  min-height: 100%;
  margin-top: 56px;
  .warn{
    color: #1b3155;
    text-align: left;
    padding-top: 5px;
    height: 15px;
    transform: scale(0.84);
    margin-left: -18px;
    margin-bottom: -11px;
    font-size: 12px;
  }
  .tips{
    font-size: 12px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: left;
    color: #919db3;
    line-height: 16px;
    margin-top: 6px;
  }
  .bank{
    background: #f3f5f6;
    border-radius: 8px;
    position: relative;
    margin-top: 12px;
    &.not-bank{
      border: 1px solid red;
    }
    .name{
      font-size: 16px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      color: #919db3;
      line-height: 22px;
      display: flex;
      align-items: center;
      height: 50px;
      padding-left: 10px;
      &.new-name{
        color: #1b3155;
      }
      &.processing{
        color: #919db3;
        justify-content: center;
      }
    }
    img {
      transform: rotate(-90deg);
      height: 13px;
      position: absolute;
      bottom: 18px;
      right: 12px;
    }
  }
  .input{
    margin-top: 12px;
    position: relative;
    background: #f3f5f6;
    border-radius: 8px;
    &.focus{
      border: 1px solid red;
    }
    input{
      height: 22px;
      background: #f3f5f6;
      width: calc(100% - 20px);
      border: none;
      border-radius: 8px;
      padding: 14px 10px;
      color: #1b3155;
      font-size: 16px;
      outline:none;
      &::-webkit-input-placeholder{
        font-size: 16px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #919db3;
      }
    }
    .progress{
      position: absolute;
      right: 12px;
      top: 16px;
      font-size: 12px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: right;
      color: #999999;
      transform: scale(0.84);
      .index{
        color: red;
      }
    }
  }
  .account{

  }
  .guar{
    font-size: 12px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: center;
    color: #919db3;
    line-height: 16px;
    margin-top: 32px;
  }
  .next{
    height: 42px;
    background: #02B17B;
    background: $themeColor;
    border-radius: 7px;
    color: #ffffff;
    font-size: 18px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: center;
    line-height: 42px;
    margin-top: 13px;
  }
}
.give-up{
    // color: #536887;
    // padding-bottom: 10px;
    // width: 254px;
    // .van-dialog__content--isolated{
    //   min-height: 70px;
    // }
    // .van-button__content{
    //   span {
    //     background: $themeColor;
    //     display: inline-block;
    //     width: 80%;
    //     color: #ffffff;
    //     height: 36px;
    //     border-radius: 8px;
    //     line-height: 36px;
    //   }
    // }
    // .van-dialog__message--left {
    //     padding: 15px 24px;
    // }
    ::v-deep .van-dialog__content{
      padding: 20px;
      .title{
        color: #536887;
        text-align: left;
        font-weight: 600;
        font-size: 24px;
        height: 75px
      }
      .tips{
        text-align: left;
      }
    }
    ::v-deep .van-dialog__cancel{
      color: $themeColor !important;
    }
    ::v-deep .van-dialog__confirm{
      color: #ffffff !important;
      background: $themeColor;
    }
  }
</style>
