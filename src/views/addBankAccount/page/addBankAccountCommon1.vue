<template>
  <div class="add-bank-account-container">
    <c-header titleName="Bank Account" :backFun="myBack" :styType="'c'"></c-header>
    <div class="add-bank-account">
      <div class="tips">
        Kindly provide one of your own bank accounts that can receive the money.
      </div>
      <div class="bank" :class="{'not-bank': noBank}">
        <!-- <div class="warn" v-if="bank.bankCardName">Name of your bank</div> -->
        <div class="name" :class="{'new-name': bank.bankCardName}" @click="addBank()" v-text="bank.bankCardName ? bank.bankCardName : 'Name of your bank'"></div>
        <img v-if="!bank.bankCardName" :src="require(`@/assets/images/${productSource}/icon_down.png`)" />
      </div>
      <div class="input" :class="{'focus': showFocus}">
        <!-- <div class="warn" v-show="showWarn && showFocus">Bank Account Number</div> -->
        <input ref="input" @focus="changeShow()" v-model="bankNumber" @keydown="keyDown($event)" @keyup="keyUp($event)" type="tel" placeholder="Bank Account Number">
        <div class="progress">
          <span :class="{'index': bankNumber.length < 10}" v-text="bankNumber ? bankNumber.length : 0"></span>
          <span>/10</span>
        </div>
      </div>
      <div class="bvn-tips">
        The bank linked to your own BVN
      </div>
      <div class="account bank">
        <div class="warn" v-if="bankAccount && !process">Name of your bank</div>
        <div v-show="process" class="name" :class="{'processing': process}">Processing</div>
        <div v-show="!process" class="name" :class="{'new-name': bankAccount}" v-text="bankAccount ? bankAccount : 'Account Name'"></div>
      </div>
      <div class="tips-list">
        <div>Tips: </div>
        <div>
          1. Add a bank account to receive your loan or bonus.
        </div>
        <div>
        2. Your account details are safely protected, please feel free to fill in.
        </div>
      </div>

      <div class="next" @click="addBankAccount4nc()">
        Next <img class="icon-btn" :src="require(`@/assets/images/${productSource}/icon_btn.png`)" alt="">
      </div>
      <div class="guar"><img class="icon-guar" :src="require(`@/assets/images/${productSource}/icon-guar.png`)" alt="">{{ `${channel} security guarantee` }}</div>
    </div>
    <van-dialog v-model="showDialog" show-cancel-button className="give-up" confirmButtonText="OK" cancelButtonText="give up" @cancel="giveUp">
        <div class="title">Are you sure to give up?</div>
        <div class="tips">Just one step to receive your money.</div>
    </van-dialog>
  </div>
</template>

<script>
import publicMixns from '../addBankAccountMixins.js'
export default {
    name: 'addBankAccountCommon1',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.add-bank-account-container {
  height: 100%;
  background: #fff;

}
.add-bank-account{
  height: 100%;
  padding: 60px 22px 0 22px;
  box-sizing: border-box;
  .warn{
    color: #fff;
    text-align: left;
    padding-top: 5px;
    height: 15px;
    transform: scale(0.84);
    margin-left: -18px;
    margin-bottom: -11px;
    font-size: 12px;
  }
  .tips{
    color: #1B40FF;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: HarmonyOS Sans SC;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    margin-top: 6px;
    text-align: left;
  }
  .bvn-tips {
    color: rgba(255, 255, 255, 0.50);
    font-family: HarmonyOS Sans SC;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    text-align: left;
    margin-top: 12px;
  }
  .tips-list {
    color: rgba(255, 255, 255, 0.60);
    font-family: HarmonyOS Sans SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    margin-top: 30px;
    text-align: left;
    margin-bottom: 60px;
  }
  .bank{
    border-radius: 50px;
    position: relative;
    margin-top: 12px;
    background: #F8F8F8;
    &.not-bank{
      border: 1px solid red;
    }
    .name{
      color: #8E8E8E;
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      display: flex;
      align-items: center;
      height: 50px;
      padding-left: 10px;
      &.new-name{
        color: #231815;
      }
      &.processing{
        color: #8E8E8E;
        justify-content: center;
      }
    }
    img {
      width: 24px;
      height: 24px;
      position: absolute;
      bottom: 12px;
      right: 12px;
    }
  }
  .input{
    margin-top: 12px;
    position: relative;
    background: #F8F8F8;
    border-radius: 50px;
    &.focus{
      border: 1px solid red;
    }
    input{
      height: 22px;
      background: #F8F8F8;
      width: calc(100% - 20px);
      border: none;
      border-radius: 50px;
      padding: 14px 10px;
      color: #8E8E8E;
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      outline:none;
      text-align: left;
      &::-webkit-input-placeholder{
        color: #8E8E8E;
        text-align: center;
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        text-align: left;
      }
    }
    .progress{
      position: absolute;
      right: 12px;
      top: 16px;
      color: #8E8E8E;
      font-family: HarmonyOS Sans SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      text-align: right;
      .index{
        color: #FF5252;
        font-family: HarmonyOS Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
  .guar{
    color: #FFB300;
    text-align: center;
    font-family: HarmonyOS Sans SC;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    text-transform: capitalize;
    .icon-guar {
      width: 18px;
      height: 18px;
      margin-right: 8px;
    }
  }
  .next{
    height: 42px;
    border-radius: 50px;
    background: $background;
    color: #FFF;
    font-family: HarmonyOS Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    .icon-btn {
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-left: 8px;
    }
  }
}
.give-up{
  border-radius: 18px;
  border: 1px solid #FFF;
  background: #26282D;
    ::v-deep .van-dialog__content{
      padding: 20px;
      .title{
        color: #FFF;
        font-family: HarmonyOS Sans SC;
        font-size: 19px;
        font-style: normal;
        font-weight: 700;
        height: 75px
      }
      .tips{
        color: #999;
        font-family: HarmonyOS Sans SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
      }
    }
    ::v-deep .van-dialog__cancel{
      color: $color !important;
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
    }
    ::v-deep .van-dialog__confirm{
      color: #ffffff;
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      background: $background;
    }
  }
</style>
