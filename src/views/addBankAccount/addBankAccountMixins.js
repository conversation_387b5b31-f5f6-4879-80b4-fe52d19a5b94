import arrow from '@/assets/images/arrow.png';
import { mapState } from 'vuex';
import api from "@/api/interface";

export default {
  name: 'addBankAccount',
  computed: {
    ...mapState(['bank', 'channel', 'productSource']),
    bankUpdate() {
      const vm = this;
      if (vm.bankNumber.length === 10 && vm.bank.bankCardNo) {
        vm.verifyBankAccount4nc();
      }
      return this.bank;
    }
  },
  data() {
    return {
      arrow,
      bankNumber: '',
      bankAccount: '',
      showWarn: true,
      showFocus: false,
      submitFlag: false,
      process: false,
      inputNumber: false,
      showDialog: false,
      noBank: false
    };
  },
  watch: {
  },
  methods: {
  myBack() {
    this.showDialog = true;
  },
  giveUp() {
    this.showDialog = false;
    this.$router.back(-1);
  },
  changeShow() {
    this.showWarn = true;
    this.showFocus = true;
  },
  keyDown() {
    // const vm = this;
    // const key = e.keyCode;
    // console.log(vm.bankNumber.length, key);
    // if (vm.bankNumber.length === 10 && key !== 8) {
    //   if (vm.submitFlag) {
    //     vm.verifyBankAccount4nc();
    //     vm.submitFlag = false;
    //   }
    //   e.preventDefault();
    // }
    // if (vm.bankNumber.length === 9 && key !== 8) {
    //   vm.submitFlag = true;
    // }
  },
  keyUp(e) {
    const vm = this;
    const key = e.keyCode;
    e.target.value = vm.bankNumber = vm.bankNumber.replace(/\D+/g, '');
    if (vm.bankNumber.length === 10 && key !== 8) {
      console.log('verifyBankAccount4nc');
      vm.verifyBankAccount4nc();
    }
    if (vm.bankNumber.length > 10) {
      vm.bankNumber = `${vm.bankNumber}`.slice(0, 10);
      e.target.value = vm.bankNumber;
      e.preventDefault();
    }
  },
  // 验证银行账户
  verifyBankAccount4nc() {
    let vm = this;
    if (!vm.bank.bankCardNo) return;
    vm.process = true;
    return new Promise((resolve, reject) => {
      vm.$http(api.bankAccount.verifyBankAccount4nc, {
        data:{
            "bankCode": vm.bank.bankCardNo,
            "bindBankAccountFlag":"N", // 这个是写死的么？
            "bankAccountNumber": vm.bankNumber
        }
      }).then(res => {
        vm.process = false;
        vm.bankAccount = res.bankAccountName;
        resolve();
      }).catch(e => {
        vm.process = false;
        console.log(e);
        reject(e);
      });
    })
  },
  // 添加银行账户
  addBankAccount4nc() {
    let vm = this;
    vm.addBankReport('addbankaccount_next_button_click');
    if (vm.bankNumber.length < 10 || vm.bank.bankCardName.length === 0) {
      vm.$toast({
        message: 'Please verify the red information is correct!'
      });
      if (!(vm.bank && vm.bank.bankCardName)) {
        vm.noBank = true;
        return;
      }
      if (vm.bankNumber.length < 10) {
        vm.showFocus = true;
        return;
      }
    }
    if (!vm.bankAccount) return;
    return new Promise((resolve, reject) => {
      vm.addBankReport('addbankaccount_start');
      vm.$http(api.bankAccount.addBankAccount4nc, {
          "data":{
              "bankCode": vm.bank.bankCardNo,
              "bankAcctName": vm.bankAccount,
              "bankAccNo": vm.bankNumber
          }
      }).then(res => {
        if (res.isAppend) {
          vm.addBankReport('addbankaccount_success');
          vm.$router.go(-1);
        } else {
          vm.addBankReport('addbankaccount_isAppend_failed');
        }
        resolve();
      }).catch(e => {
        console.log(e);
        vm.addBankReport('addbankaccount_failed');
        reject(e);
      });
    })
  },
  addBank() {
    const vm = this;
    // 延时跳转，避免输入法引起的页面异常。
    setTimeout(function() {
      vm.$router.push({ path: '/bankList' });
    }, 400);
  },
  addBankReport(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'addBankAccount',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
  },
  beforeRouteEnter (to, from) {
    const vm = this;
    if (from.name === 'index') {
      vm.bankNumber = '';
      vm.bankAccount = '',
      vm.showWarn = true,
      vm.showFocus = false,
      vm.submitFlag = false,
      vm.process = false,
      vm.inputNumber = false,
      vm.showDialog =  false
    }
    vm.addBankReport('addbankaccount_page_view');
  }
  },
  mounted() {
    this.beforeRouteEnter(this.$parent.to, this.$parent.from);
  }
};