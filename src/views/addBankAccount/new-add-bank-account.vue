
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'addBankAccount',
  components: {
    baseAddBankAccount: () => import(/* webpackChunkName: "baseaddBa" */ './page/addBankAccountBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "addBankAccountWayacredit" */ './page/addBankAccountWayacredit.vue'),
    addBankAccountCommon1: () => import(/* webpackChunkName: "addBankAccountCommon1" */ './page/addBankAccountCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseAddBankAccount',
        to: '',
        from: '',
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.addBankAccount) {
      this.componentTag = globalConfig.pageStyleSetting.addBankAccount
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
