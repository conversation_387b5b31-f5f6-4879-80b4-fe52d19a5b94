
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
export default {
    name: 'faceVerification',
    components: {
      common: () => import(/* webpackChunkName: "faceVerification" */ './page/common.vue'),
    },
    data() {
        return {
          componentTag: 'common',
          to: '',
          from: '',
        }
    },
    created() {
      const productSource = localStorage.getItem('productSource');
      console.log('productSource', productSource);
    },
    beforeRouteEnter (to, from, next) {
      next(vm => {
        vm.to = to;
        vm.from = from;
        if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
          vm.$refs.child.beforeRouteEnter(to, from);
        }
      })
    }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
