<template>
  <div class="face-verification">
    <c-header titleName="Face Verification"></c-header>
    <div class="content">
      <div class="title">Face recognition to authenticate</div>
      <div class="title-tips">Please face the screen and keep your face in the frame.</div>
      <img class="example" src="@/assets/images/faceVerification/faceVerificationExample.png" />
      <ul class="note-list">
        <li v-for="notice in noticeList" :key="notice.text">
          <img :src="notice.img" alt="">
          <div class="text" v-text="notice.text"></div>
        </li>
      </ul>
      <button class="c-button" @click="startFaceVerification">Face Verification</button>
      <div class="tips">
        <p>Tips:</p>
        <p>1. Do not wear hats or masks</p>
        <p>2. Please start in a well-lit area</p>
        <p>3. Do not wear sunglasses</p>
        <p>4. Make sure it's you, not someone else.</p>
      </div>
    </div>
  </div>
</template>

<script>
import faceVerification from "../faceVerification.js";
export default {
  name: 'commonFaceVerification',
  mixins: [faceVerification],
  data() {
    return {
    };
  },

  mounted() {

  },

  methods: {
    submit() {}
  }
};
</script>
<style lang="scss" scoped>
.face-verification {
  width: 100%;
  height: 100%;
  padding-top: 56px;
  box-sizing: border-box;
  .contact {
    position: absolute;
    right: 19px;
    top: 9px;
    text-align: center;
    img {
      display: inline-block;
      width: 24px;
      height: 24px;
    }
    p {
      color: #0068B2;
      font-family: Avenir;
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
    }
  }
  .c-button {
    margin: 20px auto;
    width: 100%;
    background: $themeColor;
    height: 42px;
    border-radius: 8px;
    border: none;
    color: #FFF;
    text-align: center;
    font-family: Avenir;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
  .note-list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 20px;
    li{
      width: 54px;
      height: 85px;
      img{
        width: 40px;
        height: 40px;
        margin: 0 auto 3px auto;
      }
      .text{
        color: #919DB3;
        text-align: center;
        font-family: Avenir;
        font-size: 11px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        text-align: center;
      }
    }
  }
  .content{
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 0 18px;
    overflow: scroll;
    .title{
      color: #1B3155;
      font-family: Avenir;
      font-size: 18px;
      font-style: normal;
      font-weight: 800;
      line-height: normal;
      text-align: center;
    }
    .title-tips{
      color: #919DB3;
      text-align: center;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    .example{
      width: 213px;
      height: 213px;
      margin: 12px auto 12px;
    }

    .tips{
      color: #919DB3;
      font-family: Avenir;
      font-size: 11px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      width: 250px;
      margin: 0 auto;
    }
  }
}
</style>
