import { mapState } from "vuex";
import {
  startFaceIdentify
} from "@/assets/js/native";
import { decode } from 'js-base64';
import api from "@/api/interface";
import { getDeviceId } from "@/assets/js/native";

export default {
  components: {
  },
  data() {
    return {
      noticeList: [{
        img: require("@/assets/images/faceVerification/ic-light bright.png"),
        text: 'Light bright'
      }, {
        img: require("@/assets/images/faceVerification/ic-no-phone vibration.png"),
        text: 'No phone vibration'
      }, {
        img: require("@/assets/images/faceVerification/ic-no-other people.png"),
        text: 'No other people'
      }, {
        img: require("@/assets/images/faceVerification/ic-no-mask.png"),
        text: 'No mask'
      }],
      deviceId: ''
    }
  },
  computed: {
    ...mapState(['uid', 'supportedFeatures', 'checkDeviceStage', 'checkDeviceStageResult'])
  },
  watch: {
  },
  mounted() {
    this.report('withdraw_face_example_view');
    getDeviceId().then(deviceId => {
      this.deviceId = deviceId;
    });
    // 增加可见切换状态监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  },
  beforeDestroy() {
    // 移除事件切换更新状态
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  },
  destroyed() {
  },
  methods: {
    // 开始人脸验证
    startFaceVerification() {
      this.report('withdraw_face_example_click');
      this.report('withdraw_face_recognition_view');
      const vm = this;
      window['startFaceIdentifyCallBack'] = (res) => {
        const result = JSON.parse(decode(res));
        console.log('startFaceIdentifyCallBack result', result);
        if (result.faceStatus === 'success') {
          vm.$store.commit('SET_NEEDRECREDITLIVEDETECTION', 'N');  // 更新人脸是否提交信息
          if (vm.$route.query.needReCreditliveDetection) { // 存在则表示从提现页进入
            vm.$router.go(-1);
          } else if (vm.$route.query.type === 'checkDeviceStage') { // 设备检测的人脸验证, 返回首页
            this.$store.commit('SET_CHECKDEVICESTAGERESULT', 'Y');
            vm.$router.go(-1);
          } else { // 否则返回两次首页
            vm.$router.go(-2);
          }
        } else {
          if (vm.$route.query.type === 'checkDeviceStage') { // 设备检测的人脸验证失败，重新获取验证类型
            this.getDeviceVerification().then(() => {
              if (this.checkDeviceStage === 'OTP' && this.checkDeviceStageResult !== 'Y') { // 更新为OTP返回拉齐OTP
                vm.$router.go(-1);
              }
              // 其他情况停留在当前页面
            });
          }
        }
        console.log('startFaceIdentifyCallBack result', result);
        this.report('withdraw_face_recognition_back');
      }
      startFaceIdentify('startFaceIdentifyCallBack');
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'faceVerification',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    // 获取当前用户设备验证的类型
    getDeviceVerification() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.deviceVerification, {
          data: {
            deviceId: this.deviceId,
            supportFaceDetectionFlag: this.supportedFeatures.faceVerify === 'true' ? 'Y' : 'N' // 当前设备是否支持人脸识别
          }
        }).then(res => {
          console.log('getDeviceVerification in face res', res)
          if (res.checkDeviceStage === 'P') { 
            // 通过的情况下，也需要设置通过的结果
            // checkDeviceStage用于缓存当前的验证类型，通过时不能修改checkDeviceStage。 结果和类型应该要分开存。
            this.$store.commit('SET_CHECKDEVICESTAGERESULT', 'Y');
          } else { // 不通过的情况下，才需要更新当前的检测类型。
            this.$store.commit('SET_CHECKDEVICESTAGE', res.checkDeviceStage);
          }
          resolve(res);
        }).catch(e => {
          reject(e);
        })
      })
    },
    handleVisibilityChange() {
      if (document.hidden) {
        console.log('Document is hidden');
      } else {
        // 目前因为客户端没有把失败的情况回调给前端，所以前端对于客户端非成功的情况，要再次查询更新一下
        if (this.$route.query.type === 'checkDeviceStage') { // 设备检测的人脸验证失败，重新获取验证类型
          console.log('重新设备检测方式');
          this.getDeviceVerification().then(() => {
            if (this.checkDeviceStage === 'OTP' && this.checkDeviceStageResult !== 'Y') { // 更新为OTP返回拉起OTP
              this.$store.commit('SET_NEEDRECREDITLIVEDETECTION', 'N');  // 对于这种情况，不再需要更新人脸是否提交信息
              this.$router.go(-1);
            }
          });
        }
      }
    }
  }
};