import CMoney from '@/components/c-money.vue';
import CButton from '@/components/c-button.vue';
import icUsed from '@/assets/images/ic-used.png';
import { gotoHomeActivity, gotoLoanList, getCustId } from "@/assets/js/native";
import { mapState } from 'vuex';
import api from "@/api/interface";



export default {
  name: 'couponsList',
  components: {
    CMoney,
    CButton
  },
  data() {
    return {
      icUsed,
      couponsList: [], // 优惠列表
      listRequest: {
        data:{
            "status":"UNUSED",
            "useScene":"1", //
            "channel":"" // PCX的优惠券，使用channel为PC-XCross
        },
        page:{
            "isNext":false,
            "pageNum":0,
            "pageSize":1000,
            "startIndex":0,
            "totalPage":0,
            "totalRecord":0
        }
      },
      tipsObj: {},
      isLoading: false, // 是否正在加载
      containerHeight: '', // 滑动的高度
      finished: true,
      finishedText: '',
      couponTips: 'All loans are available \nSuper invincible coupons',
      mLoanSpan: 0,
      mLoanAmt: 0,
      term: 0,
      availCouponsNum: 0,
      hasLoan: false,
      fromName: '', // 页面来源
      custId: '',
      blackCouponList: ['10%_0911_30day', '10%_0911']
    };
  },
  computed: {
    ...mapState(['coupon', 'repayCoupon', 'userInfor', 'deviceType', 'productSource'])
  },
  methods: {
    //获取coupon列表
    queryCouponList4nc() {
      let vm = this;
      vm.isLoading = true;
      return  new Promise((resolve, reject) => {
        vm.$http(api.coupon.queryCouponList4nc, vm.listRequest).then(res => {
          console.log('优惠券', res);
          vm.isLoading = false;
          vm.finished = true;
          if (res && res.couponList && res.couponList.length > 0) {
            res.couponList.forEach(item => {
              let limit = [];
              if (item.reducedDays != 0) {
                limit.push(vm.atLeastCal('days', item.reducedDays));
              } else if (item.reducedAmount != null && item.reducedAmount != 0) {
                limit.push(vm.atLeastCal('num', item.reducedAmount));
              }
              if ((vm.calculateCouponCanUse(item, vm.mLoanSpan, vm.mLoanAmt) && vm.availCouponsNum != 0) || vm.fromName === 'repaymentIndex' || vm.listRequest.data.useScene == '3,5') {
                item.canUse = true;
              } else {
                item.canUse = false;
              }
              if (vm.fromName === 'withdraw' && vm.availCouponsNum === 0) {
                item.canUse = false;
              }
              if (item.displayName) {
                item.content = item.displayName
              } else {
                if (item.luckyFlag === 'Y') {
                  item.content = 'Lucky coupon-will increase randomly';
                } else if (item.useScene == 4) {
                  item.content = 'Repay and Re-loan';
                } else if (item.useScene == 2) {
                  item.content = 'Repay';
                } else {
                  item.content = 'All loans are available \nSuper invincible coupons';
                }
              }
              item.limit = limit;
              if (vm.blackCouponList.includes(item.couponName)) {
                item.isBlack = true;
              } else {
                item.isBlack = false;
              }
            });
            console.log('res.couponList', vm.listRequest.data.useScene, res.couponList);
            resolve(res);
          } else {
            resolve({
              couponList: []
            })
          }
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          vm.isLoading = false;
          vm.finished = true;
          vm.$toast({
            message: `${e.code}:${e.msg}`
          });
          reject(e)
        });
      })
    },
    queryCouponUsageRecord4nc(cp) {
      let vm = this;
      const query = this.$route.query;
      if (vm.fromName === 'index' || vm.fromName === 'repaymentIndex') {
        return  new Promise((resolve, reject) => {
          vm.$http(api.coupon.queryCouponUsageRecord4nc, {
              data:{
                "productFeeId": query.productFeeId,
                "productId": query.productId
                // "custId": vm.deviceType !== 'AC' ? vm.custId : vm.userInfor.custId // 接口不能再传custId， 后台会自己获取。
              }
          }).then(res => {
            if (res.isUsed) {
              vm.$toast({ // 已使用的情况。
                message: res.tipMsg
              });
            } else {
              if (vm.fromName === 'index') {
                vm.$store.commit('SET_COUPON', cp);
                vm.$router.go(-1);
              } else if (vm.fromName === 'repaymentIndex') { // 目前还款券还不支持比例减免
                cp.discountAmt = cp.denominations ? cp.denominations : cp.amt; // amt为pcx的优惠券
                console.log('SET_REPAYCOUPON', cp);
                vm.$store.commit('SET_REPAYCOUPON', cp);
                vm.$router.go(-1);
              }
            }
            resolve();
          }).catch(e => {
            // 后续这里报错，需要退出，返回到首页。
            vm.$toast({
              message: `${e.code}:${e.msg}`
            });
            reject(e);
          });
        });
      } else if (vm.hasLoan) {
        gotoHomeActivity('couponsList');
        gotoLoanList();
      } else { // 其他情况，进入提现页。
        vm.$router.push({ path: '/', query: {
          fromName: 'useCoupon'
        }});
      }
    },
    atLeastCal(type, value) {
      if (type == 'days') {
        return `At least ${value} days`;
      } else {
        return `At least ₦${value}`;
      }
    },
    /**
     * 计算优惠券是否可用
     * @param {Number} mLoanSpan 借款天数（目前分期没有使用优惠券）。
     * @param {Number} mLoanAmt 借款数额。
     * **/
    calculateCouponCanUse(coupon, mLoanSpan, mLoanAmt) {
      if (!coupon) {
        return false;
      }
      let atLeastAmt = 0;
      if (coupon.reducedAmount != null) {
        atLeastAmt = coupon.reducedAmount;
      }
      let atLeastDay = coupon.reducedDays;
      let isDateAvailable = coupon.status === 'UNUSED' ? true : false;
      if (atLeastAmt <= mLoanAmt && atLeastDay <= mLoanSpan && isDateAvailable) { // 满足满减条件，生效日期。
        return true;
      } else {
        return false;
      }
    },
    goInvalidCoupons() {
      this.$router.push({ path: '/invaildCouponsList' });
    },
    // 返回
    myBackFun() {
      let vm = this;
      if (vm.fromName === 'index' || vm.fromName === 'repaymentIndex') { // 其他页面正常返回。
        vm.$router.back(-1);
      } else {
        gotoHomeActivity('couponsList');
      }
    },
    // 获取当前用户的总借款列表。
    queryLoanList4nc() {
      let vm = this;
      return new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryLoanList4nc, {
          data:{
              "loanStatus":"p",
              "term":0
          },
          page:{
              "isNext":false,
              "pageNum":0,
              "pageSize":10000,
              "startIndex":0,
              "totalPage":0,
              "totalRecord":0
          }
        }).then(res => {
          if (res && res.loanList && res.loanList.length > 0) {
            vm.hasLoan = true;
          }
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    },
    couponReport(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'couponsList',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    stopUseCoupons() {
      let vm = this;
      if (vm.fromName === 'index') {
        vm.$store.commit('SET_COUPON', {});
        vm.$router.go(-1);
      } else if (vm.fromName === 'repaymentIndex') { // 还款
        vm.$store.commit('SET_REPAYCOUPON', {});
        vm.$router.go(-1);
      }
    },
    onLoad() {},
    // 访问faq
    goCouponFaq() {
      this.$router.push({ path: '/couponsFaq'});
    },
    beforeRouteEnter (to, from) {
      const vm = this;
      console.log('from', from)
      console.log('ponyKashConnect', window.ponyKashConnect);
      vm.fromName = from.name;
      console.log('vm.fromName', vm.fromName, 'vm.productSource', vm.productSource);
      if (vm.$route.query && vm.$route.query.from === 'withdraw') { // 提现
        console.log(vm.$route.query);
        vm.listRequest.data.useScene = '1';
        const query = vm.$route.query;
        vm.mLoanSpan = query.mLoanSpan;
        vm.mLoanAmt = query.mLoanAmt;
        vm.term = query.term;
        vm.availCouponsNum = query.availCouponsNum;
        vm.queryCouponList4nc().then(res => {
          vm.couponsList = res.couponList;
        });
      } else if (from.name === 'repaymentIndex') {
        vm.listRequest.data.useScene = '2,4';
        vm.queryCouponList4nc().then(res => {
          vm.couponsList = res.couponList;
        });
      } else {
        vm.queryLoanList4nc();
        vm.listRequest.data.useScene = '3,5'; // 包含了提现和还款逻辑
        vm.queryCouponList4nc().then(res => {
          vm.couponsList = res.couponList;
        });
      }
    }
  },
  mounted() {
    let vm = this;
    vm.beforeRouteEnter(vm.$parent.to, vm.$parent.from);
    const headerEl = document.querySelector('.coupons-list')
    if (headerEl) {
      const headerHeight = headerEl.getBoundingClientRect().height
      vm.containerHeight = window.innerHeight - headerHeight - 56 + 'px'
    }
    if (vm.deviceType !== 'AC') {
      getCustId().then(custId => {
        vm.custId = custId;
      });
    }
    vm.couponReport('coupons_page_view');
    window.scrollTo(0, 0);
    console.log('ponyKashConnect', window.ponyKashConnect);
  }
};


