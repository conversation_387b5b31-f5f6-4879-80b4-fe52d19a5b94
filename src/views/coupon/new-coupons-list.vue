
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'couponsList',
  components: {
    baseCouponsList: () => import(/* webpackChunkName: "basecouList" */ './page/couponsListBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "couponsListWayacredit" */ './page/couponsListWayacredit.vue'),
    couponsListCommon1: () => import(/* webpackChunkName: "couponsListCommon1" */ './page/couponsListCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseCouponsList',
        to: '',
        from: '',
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.couponsList) {
      this.componentTag = globalConfig.pageStyleSetting.couponsList
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
    })
  }
}
</script>
<style scoped>
</style>
