<template>
  <div>
    <c-header titleName="My Coupons" :backFun="myBackFun">
      <template #right>
        <img class="coupons-faq" @click="goCouponFaq()" :src="require(`@/assets/images/${productSource}/btn-list-qa.png`)" alt="">
      </template>
    </c-header>
    <div class="coupons-list">
      <van-list v-model="isLoading" :finished="finished" :immediate-check="false" :finished-text="finishedText" @load="onLoad" :offset="10">
        <ul class="coupons-list-ul" v-if="couponsList.length > 0">
          <li class="coupon" :class="{'can-not-use': !cp.canUse}" v-for="cp in couponsList" :key="cp.id">
            <img v-if="coupon.couponId === cp.couponId || repayCoupon.couponId === cp.couponId" class="coupons-choosed" :src="require(`@/assets/images/${productSource}/bg-coupons-choose.png`)" alt="" />
            <div class="c-content" :class="{'new-content': cp.useScene === '5'}">
              <!-- 判断类型待确定couponMethod -->
              <div class="detail" :class="{'use-scene-5': cp.useScene === '5', 'single': !(cp.limitUpAmount > 0 && cp.amt > 0) }">
                  <template v-if="cp.useScene === '5'">
                    <div class="other">
                      <div class="type" v-if="cp.limitUpAmount > 0">
                        <CMoney v-if="cp.limitUpType === 'A'" :currencyNum="cp.limitUpAmount"></CMoney>
                        <div class="amount" v-else>
                          <span class="num" v-text="cp.limitUpAmount * 100"></span>
                          <span class="percent">%</span>
                        </div>
                        <div class="tips px-10">Limit increase</div>
                      </div>
                      <div class="type" v-if="cp.amt > 0">
                        <CMoney v-if="cp.couponMethod === 'A'" :currencyNum="cp.amt"></CMoney>
                        <div class="amount" v-else>
                          <span class="num" v-text="cp.amt * 100"></span>
                          <span class="percent">%</span>
                        </div>
                        <div class="tips px-10">Increase relief</div>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <CMoney :class="{'black': cp.isBlack}" v-if="cp.couponMethod === 'A'" :currencyNum="cp.amt"></CMoney>
                    <div class="other" :class="{'black': cp.isBlack}" v-if="cp.couponMethod === 'R'" >
                      <span class="item" v-text="cp.amt * 100"></span>
                      <span class="per">%</span>
                    </div>
                    <div class="other" :class="{'black': cp.isBlack}" v-if="cp.couponMethod === 'D'">
                      <span class="item" v-text="cp.amt"></span>
                      <span class="day" v-text="cp.amt > 1 ? 'Days' : 'Day'"></span>
                    </div>
                    <span v-if="cp.couponMethod === 'A'"  class="tips px-10" :class="{'black': cp.isBlack}">Interest amount</span>
                    <span v-if="cp.couponMethod === 'R'"  class="tips px-10" :class="{'black': cp.isBlack}">Discount</span>
                    <span v-if="cp.couponMethod === 'D'" class="tips px-10"  :class="{'black': cp.isBlack}">Days Interest Free</span>
                  </template>
              </div>
              <div class="des">
                <div class="infor" v-text="cp.content"></div>
                <ul class="limit px-10" :class="{'black': cp.isBlack}">
                  <li  v-for="li in cp.limit" :key="li" v-text="li"></li>
                </ul>
              </div>
            </div>
            <div class="validity-period" :class="{'new-content': cp.useScene === '5'}">
              <div class="time" v-text="'Validity Period:' + cp.effectiveDate + '~' + cp.expiredDate"></div>
              <div class="use" @click="queryCouponUsageRecord4nc(cp)" :class="{'black': cp.isBlack}" v-if="cp.status === 'UNUSED' && cp.canUse">Use</div>
              <div class="left-cirlcle"></div>
              <div class="right-cirlcle"></div>
            </div>
          </li>
        </ul>
        <div class="not-data" v-else>
          <img class="bg-nocoupon" :src="require(`@/assets/images/bg-nocoupon.png`)" alt="">
          <div class="tips">You have no coupon to uesd yet</div>
        </div>
        <div v-if="!(fromName === 'index' || fromName === 'repaymentIndex')" class="invalid-coupons" @click="goInvalidCoupons">View invalid coupons</div>
      </van-list>
      <CButton v-if="fromName === 'index' || fromName === 'repaymentIndex'"  @buttonClick="stopUseCoupons()" className="stop-use" name="Stop Using Coupons"></CButton>
    </div>
  </div>
</template>

<script>
import publicMixns from '../couponsListMixins.js'
export default {
    name: 'couponsListBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.coupons-faq{
    width: 20px;
    height: 20px;
    margin-right: 13px;
  }
  .coupons-list{
    height: 100vh;
    padding: 9px 12px;
    background: #f2f3f8;
    margin-top: 56px;
    overflow: scroll;
    .coupon{
      width: 100%;
      height: 117px;
      background: #ffffff;
      border-radius: 10px;
      margin-top: 8px;
      position: relative;
      margin-bottom: 10px;
      &.can-not-use{
        .c-content{
          border-bottom: 1px dashed #e3e5e9;
          .detail{
            color: #c4cad5;
            ::v-deep .c-money{
              .monetary-unit{
                color: #c4cad5 !important;
              }
              .currency-num{
                color: #c4cad5 !important;
              }
            }

          }
          .des{
            .infor{
              color: #c4cad5;
            }
            .limit{
              li{
                border: 1px solid #c4cad5;
                color: #c4cad5;
              }
            }
          }
        }
        .validity-period{
          color: #919db3;
          background: #fafafa;
        }
      }
      .coupons-choosed{
        position: absolute;
        right: 0px;
        height: 40px;
        width: 40px;
        top: -1px;
      }
      .c-content{
        display: flex;
        padding: 7px 0px;
        height: 70px;
        border-bottom: 1px dashed #e8eaed;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        .detail{
          font-family: DINAlternate, DINAlternate-Bold;
          font-weight: 700;
          color: $themeColor;
          width: 101px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          ::v-deep .c-money{
            font-family: DINAlternate, DINAlternate-Bold;
            font-weight: 700;
            text-align: center;
            width: 101px;
            .monetary-unit{
              color: $themeColor !important;
              font-size: 16px;
            }
            .currency-num{
              color: $themeColor !important;
              font-size: 25px;
              margin-left: 0;
            }
            &.black{
              .monetary-unit{
                color: black !important;
              }
              .currency-num{
                color: black !important;
              }
            }
          }
          .other{
            .item{
              font-size: 25px;
            }
            .day{
              font-size: 14px;
            }
            .per{
              font-size: 14px;
            }
            &.black{
              .item{
                color: black !important;
              }
              .day{
                color: black !important;
              }
              .per{
                color: black !important;
              }
            }
          }
          .tips{
            font-weight: 500;
            transform: scale(0.8);
            display: inline-block;
            &.black{
              color: black !important;
            }
          }
          &.use-scene-5{
            width: 150px;
            .other{
              flex-direction: row;
              display: flex;
              justify-content: center;
              height: 57px;
              .type{
                width: 80px;
                height: 57px;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                ::v-deep .c-money{
                  width: 80px;
                  .currency-num{
                    font-size: 24px;
                    font-weight: 600;
                  }
                }
                .tips{
                  width: 105px;
                  height: 27px;
                  transform: scale(0.75);
                  margin-left: -14px;
                  text-align: center;
                }
                .amount{
                  height: 30px;
                  line-height: 30px;
                  text-align: center;
                  .num{
                    font-size: 24px;
                    font-family: DINAlternate, DINAlternate-Bold;
                    font-weight: 700;
                  }
                  .percent{
                    font-size: 16px;
                    font-family: DINAlternate, DINAlternate-Bold;
                    font-weight: 800;
                  }
                }
              }
            }
          }
          &.single{
            width: 110px;
          }
        }
        .des{
          width: 171px;
          position: relative;
          .infor{
            font-size: 14px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #1b3155;
            line-height: 19px
          }
          .limit{
            display: flex;
            position: absolute;
            left: -16px;
            bottom: -5px;
            li{
              height: 13px;
              background: #ffffff;
              border: 1px solid $themeColor;
              border-radius: 3px;
              color: $themeColor;
              padding: 2px 0px;
              margin-right: 8px;
              width: 95px;
            }
            &.black{
              li{
                color: black !important;
                border: 1px solid black;
              }
            }
          }
        }
      }
      .new-content{
        background: #fff2b4;
        .amount{
          color: #ff6100;
        }
        .tips{
          color: #ff6100;
        }
        &.validity-period{
          background: #fff2b4 !important;
          border-bottom-right-radius: 10px;
          border-bottom-left-radius: 10px;
          .use{
            background: #ff6100;
          }
        }
      }
      .validity-period{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #919db3;
        display: flex;
        height: 35px;
        align-items: center;
        justify-content: space-between;
        background: #fafafa;
        position: relative;
        .time{
          margin-left: 9px;
        }
        .use{
          width: 43px;
          height: 23px;
          background: $themeColor;
          border-radius: 6px;
          text-align: center;
          line-height: 23px;
          color: #ffffff;
          margin-right: 6px;
          &.black{
            background: black;
          }
        }
        .left-cirlcle{
          background: #f2f3f8;
          width: 10px;
          height: 10px;
          border-radius: 10px;
          position: absolute;
          left: -6px;
          top: -5px;
        }
        .right-cirlcle{
          background: #f2f3f8;
          width: 10px;
          height: 10px;
          border-radius: 10px;
          position: absolute;
          right: -6px;
          top: -5px;
        }
      }
    }
    .invalid-tips{
      height: 20px;
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: $themeColor;
      line-height: 20px;
      text-align: center;
      position: absolute;
      bottom: 11px;
      left: 113px;
    }
    .not-data{
      .bg-nocoupon{
        width: 174px;
        height: 160px;
        margin-top: 17px;
      }
      .tips{
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #919db3;
        line-height: 18px;
        margin-top: 13px;
      }
    }
    .stop-use{
      position: fixed;
      bottom: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: $themeColor;
    }
    .invalid-coupons{
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: $themeColor;
      line-height: 19px;
      text-align: center;
      margin-top: 18px;
    }
  }
</style>
