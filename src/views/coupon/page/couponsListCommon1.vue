
<template>
  <div>
    <c-header titleName="My Coupons" :backFun="myBackFun" :styType="'c'">
      <template #right>
        <img class="coupons-faq" @click="goCouponFaq()" :src="require(`@/assets/images/${productSource}/btn-list-qa.png`)" alt="">
      </template>
    </c-header>
    <div class="coupons-list">
      <van-list v-model="isLoading" :finished="finished" :immediate-check="false" :finished-text="finishedText" @load="onLoad" :offset="10">
        <ul class="coupons-list-ul" v-if="couponsList.length > 0">
          <li class="coupon" :class="{'can-not-use': !cp.canUse}" v-for="cp in couponsList" :key="cp.id">
            <img v-if="coupon.couponId === cp.couponId || repayCoupon.couponId === cp.couponId" class="coupons-choosed" :src="require(`@/assets/images/${productSource}/bg-coupons-choose.png`)" alt="" />
            <div class="c-content">
                <!-- 判断类型待确定couponMethod -->
                <div class="detail" :class="{'use-scene-5': cp.useScene === '5', 'single': !(cp.limitUpAmount > 0 && cp.amt > 0) }">
                  <template v-if="cp.useScene === '5'">
                    <div class="other">
                      <div class="type" v-if="cp.limitUpAmount > 0">
                        <CMoney v-if="cp.limitUpType === 'A'" :currencyNum="cp.limitUpAmount"></CMoney>
                        <div class="amount" v-else>
                          <span class="num" v-text="cp.limitUpAmount * 100"></span>
                          <span>%</span>
                        </div>
                        <div class="tips px-10">Limit increase</div>
                      </div>
                      <div class="type" v-if="cp.amt > 0">
                        <CMoney v-if="cp.couponMethod === 'A'" :currencyNum="cp.amt"></CMoney>
                        <div class="amount" v-else>
                          <span class="num" v-text="cp.amt * 100"></span>
                          <span>%</span>
                        </div>
                        <div class="tips px-10">Increase relief</div>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <CMoney v-if="cp.couponMethod === 'A'" :currencyNum="cp.amt"></CMoney>
                    <div class="other" v-if="cp.couponMethod === 'R'" >
                      <span class="item" v-text="cp.amt * 100"></span>
                      <span class="per">%</span>
                    </div>
                    <div class="other" v-if="cp.couponMethod === 'D'">
                      <span class="item" v-text="cp.amt"></span>
                      <span class="day">Days</span>
                    </div>
                    <span v-if="cp.couponMethod === 'A'"  class="tips px-10">Interest amount</span>
                    <span v-if="cp.couponMethod === 'R'"  class="tips px-10">Discount</span>
                    <span v-if="cp.couponMethod === 'D'" class="tips px-10">Days Interest Free</span>
                  </template>
                </div>
              <div class="des">
                <div class="infor" v-text="cp.content"></div>
                <ul class="limit px-10">
                  <li  v-for="li in cp.limit" :key="li" v-text="li"></li>
                </ul>
              </div>
            </div>
            <div class="validity-period">
              <div class="time" v-text="'Validity Period:' + cp.effectiveDate + '~' + cp.expiredDate"></div>
              <div class="use" @click="queryCouponUsageRecord4nc(cp)" v-if="cp.status === 'UNUSED' && cp.canUse">Use</div>
            </div>
          </li>
        </ul>
        <div class="not-data" v-else>
          <img class="bg-nocoupon" :src="require(`@/assets/images/bg-nocoupon.png`)" alt="">
          <div class="tips">You have no coupon to uesd yet</div>
        </div>
        <div v-if="!(fromName === 'index' || fromName === 'repaymentIndex')" class="invalid-coupons" @click="goInvalidCoupons">View invalid coupons</div>
      </van-list>
      <CButton v-if="fromName === 'index' || fromName === 'repaymentIndex'"  @buttonClick="stopUseCoupons()" className="stop-use" name="Stop Using Coupons"></CButton>
    </div>
  </div>
</template>

<script>
import publicMixns from '../couponsListMixins.js'
export default {
    name: 'couponsListPalmcreditpro',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
  .coupons-faq{
    width: 20px;
    height: 20px;
    margin-right: 13px;
  }
  .coupons-list{
    height: 100vh;
    padding: 9px 12px;
    margin-top: 56px;
    overflow: scroll;
    .coupon{
      width: 100%;
      height: 117px;
      border-radius: 18px;
      border: 0.5px solid #666;
      border-color: $color;
      margin-top: 8px;
      position: relative;
      margin-bottom: 10px;
      &.can-not-use{
        .c-content{
          border-bottom: 1px dashed #e8eaed;
          .detail{
            color: $color !important;
            ::v-deep .c-money{
              .monetary-unit{
                color: $color !important;
              }
              .currency-num{
                color: $color !important;
              }
            }
          }
          .des{
            .infor{
              color: $color !important;
            }
            .limit{
              li{
                border: 1px solid;
                border-color: $color;
                color: $color !important;
              }
            }
          }
        }
        .validity-period{
          color: $color !important;
        }
      }
      .coupons-choosed{
        position: absolute;
        right: 0px;
        height: 40px;
        width: 40px;
        top: -1px;
      }
      .c-content{
        display: flex;
        padding: 7px 0px;
        height: 70px;
        border-bottom: 1px dashed #e8eaed;
        .detail{
          font-family: DINAlternate, DINAlternate-Bold;
          font-weight: 700;
          color: $color !important;
          width: 101px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          ::v-deep .c-money{
            font-family: DINAlternate, DINAlternate-Bold;
            font-weight: 700;
            text-align: center;
            width: 101px;
            .monetary-unit{
              color: $color !important;
              font-size: 16px;
            }
            .currency-num{
              color: $color !important;
              font-size: 25px;
              margin-left: 0;
            }
          }
          .other{
            .item{
              font-size: 25px;
            }
            .day{
              font-size: 14px;
            }
            .per{
              font-size: 14px;
            }
          }
          .tips{
            font-weight: 500;
            transform: scale(0.8);
            display: inline-block;
          }
          &.use-scene-5{
            width: 171px;
            .other{
              flex-direction: row;
              display: flex;
              .type{
                width: 80px;
                ::v-deep .c-money{
                  width: 80px;
                  .currency-num{
                    font-size: 24px;
                    font-weight: 600;
                  }
                }
                .tips{
                  width: 92px;
                  transform: scale(0.75);
                  margin-left: -7px;
                }
                .amount{
                  .num{
                    font-size: 24px;
                    font-family: DINAlternate, DINAlternate-Bold;
                    font-weight: 700;
                  }
                  .percent{
                    font-size: 16px;
                    font-family: DINAlternate, DINAlternate-Bold;
                    font-weight: 800;
                  }
                }
              }
            }
          }
          &.single{
            width: unset;
          }
        }
        .des{
          width: 171px;
          position: relative;
          .infor{
            font-size: 14px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: $color !important;
            line-height: 19px
          }
          .limit{
            display: flex;
            position: absolute;
            left: -16px;
            bottom: -5px;
            li{
              height: 13px;
              border: 1px solid;
              border-color: $color;
              border-radius: 3px;
              color: $color !important;
              padding: 2px 0px;
              margin-right: 8px;
              width: 95px;
            }
          }
        }
      }
      .validity-period{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: $color !important;
        display: flex;
        height: 35px;
        align-items: center;
        justify-content: space-between;
        .time{
          margin-left: 9px;
        }
        .use{
          width: 43px;
          height: 23px;
          border: 1px solid;
          border-color: $color !important;
          border-radius: 6px;
          text-align: center;
          line-height: 23px;
          color: #1E1E20;
          margin-right: 6px;
        }
      }
    }
    .invalid-tips{
      height: 20px;
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: $color !important;
      line-height: 20px;
      text-align: center;
      position: absolute;
      bottom: 11px;
      left: 113px;
    }
    .not-data{
      .bg-nocoupon{
        width: 174px;
        height: 160px;
        margin-top: 17px;
      }
      .tips{
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #919db3;
        line-height: 18px;
        margin-top: 13px;
      }
    }
    .stop-use{
      position: fixed;
      bottom: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: $background;
      color: #fff;
    }
    .invalid-coupons{
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: $color;
      line-height: 19px;
      text-align: center;
      margin-top: 18px;
    }
  }
</style>