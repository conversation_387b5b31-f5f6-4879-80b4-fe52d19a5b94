import CMoney from '@/components/c-money.vue';
import icUsed from '@/assets/images/ic-used.png';
import { mapState } from 'vuex';
import api from "@/api/interface";

export default {
  name: 'invaildCouponsList',
  components: {
    CMoney
  },
  computed: {
    ...mapState(['productSource'])
  },
  data() {
    return {
      icUsed,
      couponsList: [], // 优惠列表
      listRequest: {
        data:{
            "status":"EXPIRED,USED",
            "useScene":"" //
        },
        page:{
            "isNext":false,
            "pageNum":0,
            "pageSize":1000,
            "startIndex":0,
            "totalPage":0,
            "totalRecord":0
        }
      },
      tipsObj: {},
      isLoading: false, // 是否正在加载
      containerHeight: '', // 滑动的高度
      finished: true,
      finishedText: '',
      couponTips: 'All loans are available \nSuper invincible coupons',
      mLoanSpan: 0,
      mLoanAmt: 0,
      term: 0,
      fromName: '' // 页面来源
    };
  },

  methods: {
    //获取coupon列表
    queryCouponList4nc() {
      let self = this;
      self.isLoading = true;
      return  new Promise((resolve, reject) => {
        self.$http(api.coupon.queryCouponList4nc, self.listRequest).then(res => {
          console.log('优惠券', res);
          self.isLoading = false;
          self.finished = true;
          if (res && res.couponList && res.couponList.length > 0) {
            res.couponList.forEach(item => {
              let limit = [];
              if (item.reducedDays != 0) {
                limit.push(self.atLeastCal('days', item.reducedDays));
              } else if (item.reducedAmount != null && item.reducedAmount != 0) {
                limit.push(self.atLeastCal('num', item.reducedAmount));
              }
              if (self.calculateCouponCanUse(item, self.mLoanSpan, self.mLoanAmt) || self.fromName === 'repaymentIndex') {
                item.canUse = true;
              } else {
                item.canUse = false;
              }
              if (item.displayName) {
                item.content = item.displayName
              } else {
                if (item.luckyFlag === 'Y') {
                  item.content = 'Lucky coupon-will increase randomly';
                } else if (item.useScene === 4) {
                  item.content = 'Repay and Re-loan';
                } else if (item.useScene === 2) {
                  item.content = 'Repay';
                } else {
                  item.content = 'All loans are available \nSuper invincible coupons';
                }
              }
              item.limit = limit;
            });
            console.log('res.couponList', res.couponList);
            self.couponsList = res.couponList;
          }
          resolve();
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          self.isLoading = false;
          self.finished = true;
          self.$toast({
            message: `${e.code}:${e.msg}`
          });
          reject(e)
        });
      })
    },
    atLeastCal(type, value) {
      if (type == 'days') {
        return `At least ${value} days`;
      } else {
        return `At least ₦${value}`;
      }
    },
    /**
     * 计算优惠券是否可用
     * @param {Number} mLoanSpan 借款天数（目前分期没有使用优惠券）。
     * @param {Number} mLoanAmt 借款数额。
     * **/
    calculateCouponCanUse(coupon, mLoanSpan, mLoanAmt) {
      if (!coupon) {
        return false;
      }
      let atLeastAmt = 0;
      if (coupon.reducedAmount != null) {
        atLeastAmt = coupon.reducedAmount;
      }
      let atLeastDay = coupon.reducedDays;
      let isDateAvailable = coupon.status === 'UNUSED' ? true : false;
      if (atLeastAmt <= mLoanAmt && atLeastDay <= mLoanSpan && isDateAvailable) { // 满足满减条件，生效日期。
        return true;
      } else {
        return false;
      }
    },
    // 访问faq
    goCouponFaq() {
      this.$router.push({ path: '/couponsFaq'});
    },
    onLoad() {}
  },
  mounted() {
    let self = this;
    const headerEl = document.querySelector('.coupons-list')
    if (headerEl) {
      const headerHeight = headerEl.getBoundingClientRect().height
      self.containerHeight = window.innerHeight - headerHeight - 56 + 'px'
    }
    self.queryCouponList4nc();
    window.scrollTo(0, 0);
  }
};