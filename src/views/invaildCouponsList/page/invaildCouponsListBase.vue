
<template>
  <div>
    <c-header titleName="Invalid Coupons">
      <template #right>
        <img class="coupons-faq" @click="goCouponFaq()" :src="require(`@/assets/images/${productSource}/btn-list-qa.png`)" alt="">
      </template>
    </c-header>
    <div class="coupons-list">
      <van-pull-refresh v-model="isLoading" :style="{ height: containerHeight }">
        <van-list v-model="isLoading" :finished="finished" :immediate-check="false" :finished-text="finishedText" @load="onLoad" :offset="10">
          <ul class="coupons-list-ul" v-if="couponsList.length > 0">
            <li class="coupon can-not-use" v-for="cp in couponsList" :key="cp.id">
              <div class="flag" v-text="cp.status === 'USED' ? 'USED' : 'EXPIRED'"></div>
              <div class="c-content">
                <!-- 判断类型待确定couponMethod -->
                <div class="detail" :class="{'use-scene-5': cp.useScene === '5'}">
                  <template v-if="cp.useScene === '5'">
                    <div class="other">
                      <div class="type">
                        <CMoney v-if="cp.limitUpType === 'A'" :currencyNum="cp.limitUpAmount"></CMoney>
                        <div class="amount" v-else>
                          <span class="num" v-text="cp.limitUpAmount * 100"></span>
                          <span>%</span>
                        </div>
                        <div class="tips px-10">Limit increase</div>
                      </div>
                      <div class="type">
                        <CMoney v-if="cp.couponMethod === 'A'" :currencyNum="cp.amt"></CMoney>
                        <div class="amount" v-else>
                          <span class="num" v-text="cp.amt * 100"></span>
                          <span>%</span>
                        </div>
                        <div class="tips px-10">Increase relief</div>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <CMoney v-if="cp.couponMethod === 'A'" :currencyNum="cp.amt"></CMoney>
                    <div class="other" v-if="cp.couponMethod === 'R'" >
                      <span class="item" v-text="cp.amt * 100"></span>
                      <span class="per">%</span>
                    </div>
                    <div class="other" v-if="cp.couponMethod === 'D'">
                      <span class="item" v-text="cp.amt"></span>
                      <span class="day">Days</span>
                    </div>
                    <span v-if="cp.couponMethod === 'A'"  class="tips px-10">Interest amount</span>
                    <span v-if="cp.couponMethod === 'R'"  class="tips px-10">Discount</span>
                    <span v-if="cp.couponMethod === 'D'" class="tips px-10">Days Interest Free</span>
                  </template>
                </div>
                <div class="des">
                  <div class="infor" v-text="cp.content"></div>
                  <ul class="limit px-10">
                    <li  v-for="li in cp.limit" :key="li" v-text="li"></li>
                  </ul>
                </div>
                <img class="is-use" v-if="cp.status === 'USED'" :src="icUsed" alt="">
              </div>
              <div class="validity-period">
                <div class="time" v-text="'Validity Period:' + cp.effectiveDate + '~' + cp.expiredDate"></div>
              </div>
            </li>
          </ul>
          <div class="not-data" v-else>
            <img class="bg-nocoupon" :src="require(`@/assets/images/bg-nocoupon.png`)" alt="">
            <div class="tips">You have no coupon to uesd yet</div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import publicMixns from '../invaildCouponsListMixins.js'
export default {
    name: 'invaildCouponsListBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>

  #app {
    background: #f2f3f8;
  }
  .coupons-faq{
    width: 20px;
    height: 20px;
    margin-right: 13px;
  }
  .coupons-list{
    height: calc(100vh - 56px);
    padding: 9px 12px;
    margin-top: 56px;
    overflow: scroll;
    padding-top: 0px;
    .coupon{
      width: 100%;
      height: 140px;
      border-radius: 5px;
      margin-top: 8px;
      &.can-not-use{

        .flag{
          text-align: left;
          font-size: 12px;
          height: 20px;
          line-height: 20px;
          color: #c4cad5;
        }
        .c-content{
          background: #ffffff;
          border-bottom: 1px dashed #e8eaed;
          position: relative;
          .detail{
            color: #c4cad5;
            ::v-deep .c-money{
              .monetary-unit{
                color: #c4cad5 !important;
              }
              .currency-num{
                color: #c4cad5 !important;
              }
            }
          }
          .des{
            .infor{
              color: #c4cad5;
            }
            .limit{
              li{
                border: 1px solid #c4cad5;
                color: #c4cad5;
              }
            }
          }
          .is-use{
            width: 76px;
            height: 46px;
            position: absolute;
            right: 0px;
            bottom: 0px;
          }
        }
        .validity-period{
          color: #919db3;
          background: #fafafa;
        }
      }
      .c-content{
        display: flex;
        padding: 7px 0px;
        height: 70px;
        border-bottom: 1px dashed #e8eaed;
        .detail{
          font-family: DINAlternate, DINAlternate-Bold;
          font-weight: 700;
          color: $themeColor;
          width: 101px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          ::v-deep .c-money{
            font-family: DINAlternate, DINAlternate-Bold;
            font-weight: 700;
            text-align: center;
            width: 101px;
            .monetary-unit{
              color: $themeColor !important;
              font-size: 16px;
            }
            .currency-num{
              color: $themeColor !important;
              font-size: 25px;
              margin-left: 0;
            }
          }
          .other{
            .item{
              font-size: 25px;
            }
            .day{
              font-size: 14px;
            }
            .per{
              font-size: 14px;
            }
          }
          .tips{
              font-weight: 500;
              transform: scale(0.8);
              display: inline-block;
          }
          &.use-scene-5{
            width: 171px;
            .other{
              flex-direction: row;
              display: flex;
              .type{
                width: 80px;
                ::v-deep .c-money{
                  width: 80px;
                  .currency-num{
                    font-size: 24px;
                    font-weight: 600;
                  }
                }
                .tips{
                  width: 92px;
                  transform: scale(0.75);
                  margin-left: -7px;
                }
                .amount{
                  .num{
                    font-size: 24px;
                    font-family: DINAlternate, DINAlternate-Bold;
                    font-weight: 700;
                  }
                }
              }
            }
          }
        }
        .des{
          width: 171px;
          position: relative;
          .infor{
            font-size: 14px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #1b3155;
            line-height: 19px
          }
          .limit{
            display: flex;
            position: absolute;
            left: -16px;
            bottom: -5px;
            li{
              height: 13px;
              background: #ffffff;
              border: 1px solid $themeColor;
              border-radius: 3px;
              color: $themeColor;
              padding: 2px 0px;
              margin-right: 8px;
              width: 95px;
            }
          }
        }
      }
      .validity-period{
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #919db3;
        display: flex;
        height: 35px;
        align-items: center;
        justify-content: space-between;
        .time{
          margin-left: 9px;
        }
        .use{
          width: 43px;
          height: 23px;
          background: $themeColor;
          border-radius: 6px;
          text-align: center;
          line-height: 23px;
          color: #ffffff;
          margin-right: 6px;
        }
      }
    }
    .invalid-tips{
      height: 20px;
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: $themeColor;
      line-height: 20px;
      text-align: center;
      position: absolute;
      bottom: 11px;
      left: 113px;
    }
    .not-data{
      .bg-nocoupon{
        width: 174px;
        height: 160px;
        margin-top: 17px;
      }
      .tips{
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #919db3;
        line-height: 18px;
        margin-top: 13px;
      }
      .invalid-coupons{
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #02b17b;
        line-height: 19px;
        text-align: center;
        margin-top: 18px;
      }
    }
  }
</style>
