<template>
  <div class="help-center">
    <c-header titleName="Help Center"></c-header>
    <div class="help-center-main">
      <ul class="question">
        <li v-for="(item, index) in questionList" :key="item.question">
          <div class="title" @click="hideControl(index)">
            <div v-text="item.question"></div>
            <img :class="{ 'reverse': item.isHide}" src="@/assets/images/helpCenter/ic_up.png" class="ic-up" alt="" srcset="">
          </div>
          <div v-show="!item.isHide" class="text" v-for="text in item.answer" :key="text">
            <p v-text="text"></p>
          </div>
          <!-- <div v-show="!item.isHide" class="cancel-loan" v-if="index === 0" @click="cancelLoan">Cancel Loan</div> -->
        </li>
      </ul>
      <div class="bottom">
        <div class="wrap">
          <div class="item" @click="toContactUs">
            <img src="@/assets/images/helpCenter/contact_us.png" alt="" srcset="">
            <div class="name">Contact Customer Service</div>
          </div>
          <div class="item" v-if="device !== 'IOS'" @click="toComplaints">
            <img src="@/assets/images/helpCenter/feedback.png" alt="" srcset="">
            <div class="name">Complaints & Feedback</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 取消借据弹窗 -->
    <CancelLoan :time="cancelLoanInfo.cancelLoanEndTime" @close="closeCancelLoan" @confirm="confirmCancelLoan" :show="cancelLoanPopVisible"></CancelLoan>
    <!-- 提示弹窗 -->
    <Note @close="closeNote" :show="notePopVisible"></Note>
  </div>
</template>

<script>
import helpCenterMixins from "../helpCenterMixins.js";
  export default {
    name: "helpCenterCommon",
    mixins: [helpCenterMixins]
  };
</script>

<style scoped lang="scss">
.help-center{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding-top: 68px;
  ::v-deep .header {
    .title {
      color: #fff;
      text-align: left;
      font-family: 'HarmonyOS Sans SC';
      font-size: 19px;
      font-style: normal;
      font-weight: 700;
      line-height: 53px;
      color: $themeColor !important;
    }
    .header_back{
      background: $themeColor !important;
    }
    .header_box{
      background: $themeColor !important;
    }
  }
  .help-center-main {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .question{
      margin-left: 18px;
      margin-right: 18px;
      height: calc(100vh - 138px);
      overflow-y: auto;
      margin-bottom: 125px;
      .title{
        color: #1B3155;
        font-family: Avenir;
        font-size: 16px;
        font-style: normal;
        font-weight: 800;
        line-height: 21px;
        position: relative;
        margin-top: 18px;
        margin-right: 20px;
        div{
          text-align: left;
        }
        .ic-up{
          width: 20px;
          height: 20px;
          position: absolute;
          top: 0;
          right: -20px;
          &.reverse{
            transform: rotate(180deg);
          }
        }
      }
      .text{
        color: #919DB3;
        font-family: Avenir;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin-top: 9px;
        p {
          text-align: left;
        }
      }
      .cancel-loan{
        color: $themeColor;
        font-family: Avenir;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
        text-align: left;
        margin-top: 9px;
      }
    }
    .bottom{
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      padding-bottom: 55px;
      background: #ffffff;
      border: none;
      .wrap{
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 27px;
        margin-right: 26px;
        .item{
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 50%;
          img{
            width: 28px;
            height: 28px;
          }
          .name{
            color: #536887;
            text-align: center;
            font-family: Avenir;
            font-size: 11px;
            font-style: normal;
            font-weight: 500;
            line-height: 13px;
            margin-top: 9px;
          }
        }
      }
      .tabber_bottom_container {
        margin-top: 10px;
        margin-bottom: -55px;
      }
    }
  }
}
</style>
