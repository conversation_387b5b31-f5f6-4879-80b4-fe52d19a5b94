<!-- 取消借款 -->
<template>
  <div v-if="show" class="popup">
    <div class="popup-content">
      <div class="title">Cancel loan</div>
      <div class="content">
        <div class="item">If you want to cancel the loan, please pay back the received amount first, then our operation staff will cancel the loan for you after verification.</div>
        <div class="item">Note: Valid only if returned the received amount before {{ time }}</div>
      </div>
      <div class="button-group">
        <button @click="confirm">Return now</button>
        <button @click="close">No, thanks</button>
      </div>
    </div>
  </div>
</template>

<script>
import cancelLoanMixins from "./cancelLoanMixins.js";
export default {
  name: 'cancelLoan',
  mixins: [cancelLoanMixins],
  props: {
    time: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 154px;
  .popup-content {
    width: 304px;
    min-height: 293px;
    height: auto;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 21px 0px 13px 0px;
    position: relative;
    box-sizing: border-box;
    .title{
      color: #000;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      line-height: 20px;
      margin-bottom: 10px;
      margin-left: 27px;
    }
    .content{
      height: auto;
      min-height: 175px;
      margin-left: 27px;
      margin-right: 27px;
      .item{
        color: #536887;
        font-family: Avenir;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin-bottom: 20px;
      }
    }
    .button-group{
      display: flex;
      justify-content: space-between;
      padding: 0 19px;
      button{
        width: 125px;
        height: 42px;
        flex-shrink: 0;
        border-radius: 8px;
        border: 1px solid #536887;
        color: #536887;
        text-align: center;
        font-family: Avenir;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
    }
  }
}
</style>
