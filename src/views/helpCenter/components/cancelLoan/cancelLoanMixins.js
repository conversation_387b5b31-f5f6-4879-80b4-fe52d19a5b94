import CButton from '@/components/c-button.vue';
export default {
   props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  watch: {
  },
  data() {
    return {
      productSource: ''
    }
  },
  components: {
      CButton
  },
  mounted() {
    this.productSource = localStorage.getItem('productSource')
  },
  methods: {
    confirm() {
      this.$emit('confirm');
    },
    close() {
      this.$emit('close');
    }
  }
}
