<!-- 取消借款 -->
<template>
  <div v-if="show" class="popup">
    <div class="popup-content">
      <div class="title">Note</div>
      <div class="content">
        <div class="item">Sorry, you are not eligible to cancel your loan at this time.</div>
      </div>
      <button @click="close">OK</button>
    </div>
  </div>
</template>

<script>
import noteMixins from "./note.js";
export default {
  name: 'note',
  mixins: [noteMixins],
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 154px;
  .popup-content {
    width: 304px;
    min-height: 195px;
    height: auto;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 21px 0px 25px 0px;
    position: relative;
    box-sizing: border-box;
    .title{
      color: #000;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      line-height: 20px;
      margin-bottom: 10px;
      margin-left: 27px;
    }
    .content{
      height: auto;
      min-height: 57px;
      margin-left: 27px;
      margin-right: 27px;
      margin-bottom: 27px;
      .item{
        color: #536887;
        font-family: Avenir;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin-bottom: 20px;
      }
    }
    button{
      width: 268px;
      height: 42px;
      flex-shrink: 0;
      border-radius: 8px;
      background: $themeColor;
      color: #FFF;
      text-align: center;
      font-family: Avenir;
      font-size: 18px;
      font-style: normal;
      font-weight: 800;
      line-height: normal;
      margin: 0 18px;
    }
  }
}
</style>
