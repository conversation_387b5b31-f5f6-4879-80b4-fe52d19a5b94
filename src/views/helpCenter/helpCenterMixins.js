import { getCustId } from "@/assets/js/native";
// import api from "@/api/interface"
import CancelLoan from './components/cancelLoan/cancelLoan.vue'
import Note from './components/note/note.vue'
import { judgeClient } from '@/assets/js/common';

export default {
  name: "complaint",
  components: {
    CancelLoan,
    Note
  },
  data() {
    return {
      cancelLoanPopVisible: false,
      notePopVisible: false,
      popAPPConfigObj: {},
      cancelLoanInfo: {},
      questionList: [],
      custId: '',
      device: ''
    }
  },
  created() {
    this.report('help_center_page_view');
    getCustId().then(res => {
      this.custId = res
    }).catch(err => console.log(err))
    this.initData();
    this.device = judgeClient();
  },
  methods: {
    myBackFun() {
      this.$router.go(-1);
    },
    initData() {
      const arr = [{
        question: "Q1. Can I not take this loan?",
        answer: [
          "If this loan does not meet your expectations, you can find our customer service team to cancel the loan. Please return the loan to our designated repayment account within 48 hours after receiving the loan amount, and send the repayment voucher to our official customer service team to cancel the order to avoid causing you new troubles.",
          "Important note: We do not allow repayments and refunds to be received through any personal account, please be wary of fraud."
        ]
      }, {
        question: "Q2. What can you do if our staff gives you a bad experience?",
        answer: [
          "We clearly require every employee to treat every one of our customers in a friendly manner and prohibit any bad behavior. If you find that our employees have given you a bad experience, please contact our customer service team and tell us about your experience. If you have relevant information to assist us in the internal audit of the team, please also provide us with relevant information so that we can punish the corresponding employees and their leaders."
        ]
      }, {
        question: "Q3. APP does not update the bill after repayment?",
        answer: [
          "Under normal circumstances, the APP will update your bill information within 2-24 hours after repayment. If you find that the bill is still not updated after 24 hours of repayment, please contact our customer service team. Please provide us with your deduction voucher so that we can help you follow up and refresh the bill."
        ]
      }, {
        question: "Q4. I didn't apply for a loan but received a loan or repayment reminder?",
        answer: [
          "If you didn't apply for a loan but received a loan or repayment reminder, please relax. You don't have to pay any fees for a loan that doesn't belong to you. Please contact the official customer service as soon as possible and report your situation to our customer service, and provide the necessary information to assist us in the inquiry. We will investigate and give you a satisfactory answer."
        ]
      }, {
        question: "Q5. No money received after withdrawal",
        answer: [
          "After the withdrawal is successful, your loan will be issued to your account within 2-24 hours. If you still don't receive the loan after 24 hours, please contact our customer service team so that we can confirm whether the loan transaction is successful."
        ]
      }, {
        question: "Q6. App cannot be used or other system problems",
        answer: [
          "If you encounter problems related to the APP, please try to record the situation you encounter by recording the screen or taking a screenshot, and contact our customer service team. We are very grateful for providing us with the possibility to improve the APP."
        ]
      }];
      if (arr.length > 0) {
        arr.forEach((item, index) => {
          this.$set(this.questionList, index, {
            ...item,
            isHide: true
          });
        })
      }
    },
    closeCancelLoan() {
      this.cancelLoanPopVisible = false;
      this.report('cancelloan_pop_nothanks_click');
    },
    cancelLoan() {
      this.popAPPConfig();
    },
    startCancelLoan() {
      this.report('help_center_cancelloan_click');
      // 用户当前是否有在途借据且放款时间处于可取消状态
      if (this.popAPPConfigObj.cancelLoanFlag === 'Y') {
        this.cancelLoanPopVisible = true;
        this.report('cancelloan_pop_view');
      } else {
        this.notePopVisible = true;
        this.report('help_center_note_pop_view');
      }
    },
    closeNote() {
      this.notePopVisible = false;
    },
    popAPPConfig() {
      // const dataSdk = {
      //   url: api.repayment.popAPPConfig,
      //   params: {},
      //   loading: false
      // };
      // axiosSdk(dataSdk)
      //   .then((res) => {
      //     console.log('popAPPConfig', res.data)
      //     if (res.data) {
      //       this.popAPPConfigObj = res.data;
      //       this.cancelLoanInfo = res.data.cancelLoanInfo || {};
      //       /*{
      //         "cancelLoanEndTime": "2025-01-19 07:53:16",
      //         "requiredRepaymentAmt": 49500,
      //         "cancelLoanId": "5260201",
      //       }*/
      //       this.startCancelLoan();
      //     }
      //   })
      //   .catch((err) => {
      //     console.log(err)
      //   })
    },
    confirmCancelLoan() {
      this.cancelLoanPopVisible = false;
      this.submitCancelLoanFlag();
      this.report('cancelloan_pop_returnnow_click');
      this.$router.push({
        path: '/cancelLoanRepayment',
        query: {
          amount: this.cancelLoanInfo.requiredRepaymentAmt,
          loanId: this.cancelLoanInfo.cancelLoanId,
        }
      })
    },
    // 提交后台标记
    submitCancelLoanFlag() {
      // const dataSdk = {
      //   url: api.repayment.cancelLoan,
      //   params: {
      //     loanId: this.cancelLoanInfo.cancelLoanId
      //   },
      //   loading: false
      // };
      // axiosSdk(dataSdk)
      //   .then((res) => {
      //     console.log('cancelLoan', res);
      //   })
      //   .catch((err) => {
      //     console.log(err)
      //   })
    },
    toContactUs() {
      this.report('help_center_contact_click');
      this.$router.push({path: '/loadUrlPage', query: {
        url: process.env.VUE_APP_CONTACT_US_URL,
        title: 'Contact Us'
      }});
    },
    toComplaints() {
      this.report('help_center_complaint_service_click');
      this.$router.push({
        path: '/complaint'
      })
    },
    hideControl(index) {
      this.questionList[index].isHide = !this.questionList[index].isHide;
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
          page: 'helpCenter',
          eventName: eventName,
          eventData: {
          event_time: Date.now()
          }
      });
    }
  }
}
