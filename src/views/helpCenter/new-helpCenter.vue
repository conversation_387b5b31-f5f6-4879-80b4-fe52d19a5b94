<template>
  <div class="container">
    <component :is="componentTag"></component>
  </div>
</template>
<script>
export default {
  name: "complaint",
  components: {
    common: () =>
      import(
        /* webpackChunkName: "helpCenterCommon" */ "./page/common.vue"
      )
  },
  data() {
    return {
      componentTag: "common"
    };
  }
};
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
