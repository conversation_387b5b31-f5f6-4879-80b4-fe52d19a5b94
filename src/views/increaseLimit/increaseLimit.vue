<template>
  <div class="increase-limit">
    <c-header titleName="lncrease Loan Limit" :backFun="myBackFun"></c-header>
    <div class="top">
      <div class="sub-title">Congratulations on being our guest subscriber!</div>
      <div class="tips">We invite you to upload more information in order to provide you with a higher loan limit. Banke account balance.</div>
      <div class="tips">1.SIM information.(Necessary)</div>
      <div class="tips">2.Banke account balance. (Recommended)</div>
      <div class="tips">3.Operator balance.</div>
      <img src="@/assets/images/common/limit-icon.png" alt="">
    </div>
    <div class="upload" v-show="androidVersion > 28">
      <div class="title"><span>*</span>Upload SIM information</div>
      <div class="upload-p">You need to go to the operator's app that corresponds to your registered cell phone number to check the relevant information and take a screenshot to submit. <span @click="simInformation">Click to view how to get the SIM information >></span></div>
      <div class="upload-item" :class="{'upload-item-len' : haveSimFiles.length > 0}" v-for="(file, index) in haveSimFiles" :key="index"  :style="{ 'background-image': `url(${file.img})`}" @click="goPre(haveSimFiles, index)">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg" @click.stop="chooseFiles(index, 'haveSimFiles')">
          <circle cx="40" cy="40" r="40" fill="#02B17B"/>
          <path d="M24 26.6763C24.0004 26.4126 24.0795 26.1549 24.2272 25.9362C24.3748 25.7175 24.5843 25.5477 24.829 25.4484C25.0736 25.3491 25.3424 25.3249 25.6008 25.3788C25.8593 25.4327 26.0959 25.5623 26.2804 25.7511L28.9907 28.4253C31.9336 25.6515 35.9335 24 40 24C48.8266 24.0332 56 31.1992 56 40.0166C55.9967 44.2546 54.3099 48.3182 51.3101 51.3149C48.3102 54.3117 44.2425 55.9967 40 56C35.2066 56 30.7103 53.9191 27.6698 50.2199C27.4725 49.9232 27.4725 49.5601 27.7362 49.3278L30.6127 46.4544C30.7527 46.3411 30.9289 46.2822 31.109 46.2884C31.2752 46.3216 31.5057 46.388 31.6054 46.5519C32.5981 47.8413 33.8751 48.8846 35.3372 49.6006C36.7993 50.3167 38.407 50.6862 40.0353 50.6805C42.8663 50.6761 45.58 49.5507 47.5817 47.551C49.5835 45.5513 50.71 42.8404 50.7144 40.0125C50.7144 34.1348 45.8879 29.3444 40.0041 29.3444C37.2607 29.3444 34.6812 30.4004 32.731 32.2178L35.6075 35.0913C35.7941 35.2706 35.9222 35.5019 35.9752 35.7551C36.0282 36.0082 36.0036 36.2715 35.9045 36.5104C35.8066 36.7571 35.6387 36.9699 35.4214 37.1225C35.204 37.2751 34.9468 37.3609 34.6813 37.3693H25.3583C25.0096 37.3624 24.6771 37.2209 24.4305 36.9746C24.1839 36.7282 24.0423 36.3961 24.0354 36.0477L24.0001 26.6763H24Z" fill="white"/>
        </svg>
      </div>
      <!-- 选择 -->
      <div class="upload-item" :class="{'upload-item-len' : haveSimFiles.length > 0}"  v-if="haveSimFiles.length !== 2" @click="chooseFiles(-1, 'haveSimFiles')">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="40" cy="40" r="40" fill="#02B17B"/>
          <path d="M24.7051 51.7257H55.2933V55.2943H24.7051V51.7257ZM48.7729 32.856L41.7432 25.4425C41.3071 24.9961 40.6913 24.7061 39.9992 24.7061C39.3071 24.7061 38.6915 24.9961 38.2625 25.4471L31.2181 32.8507C30.8505 33.2499 30.6163 33.7655 30.6163 34.3373C30.6163 35.5646 31.667 36.5598 32.9621 36.5598C32.9802 36.5598 32.9978 36.5575 33.0156 36.557V36.571H36.1757V48.7874H43.8227V36.571H46.8971V36.5531C46.9435 36.5557 46.9891 36.5603 47.0365 36.5603C48.3319 36.5603 49.3824 35.5649 49.3824 34.3373C49.3824 33.7655 49.1479 33.2501 48.7729 32.856Z" fill="white"/>
        </svg>
      </div>
    </div>
    <div class="upload">
      <div class="title">Sample</div>
      <ul class="sample-list sim-list">
        <li class="sample" v-for="(sam, index) in simList" :key="sam.id" @click="goPre(simList, index)">
          <div class="show-img" :style="{ 'background-image': `url(${sam.img})`}">
            <img v-if="sam.recommended" src="@/assets/images/common/recommended.png" alt="">
          </div>
          <div class="content" v-text="sam.content"></div>
        </li>
      </ul>
    </div>

    <div class="upload" v-show="androidVersion > 28">
      <div class="title">Upload your balance(Optional)</div>
      <div class="upload-item" :class="{'upload-item-len' : haveUploadFiles.length > 0}" v-for="(file, index) in haveUploadFiles" :key="index"  :style="{ 'background-image': `url(${file.img})`}" @click="goPre(haveUploadFiles, index)">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg" @click.stop="chooseFiles(index, 'haveUploadFiles')">
          <circle cx="40" cy="40" r="40" fill="#02B17B"/>
          <path d="M24 26.6763C24.0004 26.4126 24.0795 26.1549 24.2272 25.9362C24.3748 25.7175 24.5843 25.5477 24.829 25.4484C25.0736 25.3491 25.3424 25.3249 25.6008 25.3788C25.8593 25.4327 26.0959 25.5623 26.2804 25.7511L28.9907 28.4253C31.9336 25.6515 35.9335 24 40 24C48.8266 24.0332 56 31.1992 56 40.0166C55.9967 44.2546 54.3099 48.3182 51.3101 51.3149C48.3102 54.3117 44.2425 55.9967 40 56C35.2066 56 30.7103 53.9191 27.6698 50.2199C27.4725 49.9232 27.4725 49.5601 27.7362 49.3278L30.6127 46.4544C30.7527 46.3411 30.9289 46.2822 31.109 46.2884C31.2752 46.3216 31.5057 46.388 31.6054 46.5519C32.5981 47.8413 33.8751 48.8846 35.3372 49.6006C36.7993 50.3167 38.407 50.6862 40.0353 50.6805C42.8663 50.6761 45.58 49.5507 47.5817 47.551C49.5835 45.5513 50.71 42.8404 50.7144 40.0125C50.7144 34.1348 45.8879 29.3444 40.0041 29.3444C37.2607 29.3444 34.6812 30.4004 32.731 32.2178L35.6075 35.0913C35.7941 35.2706 35.9222 35.5019 35.9752 35.7551C36.0282 36.0082 36.0036 36.2715 35.9045 36.5104C35.8066 36.7571 35.6387 36.9699 35.4214 37.1225C35.204 37.2751 34.9468 37.3609 34.6813 37.3693H25.3583C25.0096 37.3624 24.6771 37.2209 24.4305 36.9746C24.1839 36.7282 24.0423 36.3961 24.0354 36.0477L24.0001 26.6763H24Z" fill="white"/>
        </svg>
      </div>
      <!-- 选择 -->
      <div class="upload-item" :class="{'upload-item-len' : haveUploadFiles.length > 0}" v-if="haveUploadFiles.length !== 2" @click="chooseFiles(-1, 'haveUploadFiles')">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="40" cy="40" r="40" fill="#02B17B"/>
          <path d="M24.7051 51.7257H55.2933V55.2943H24.7051V51.7257ZM48.7729 32.856L41.7432 25.4425C41.3071 24.9961 40.6913 24.7061 39.9992 24.7061C39.3071 24.7061 38.6915 24.9961 38.2625 25.4471L31.2181 32.8507C30.8505 33.2499 30.6163 33.7655 30.6163 34.3373C30.6163 35.5646 31.667 36.5598 32.9621 36.5598C32.9802 36.5598 32.9978 36.5575 33.0156 36.557V36.571H36.1757V48.7874H43.8227V36.571H46.8971V36.5531C46.9435 36.5557 46.9891 36.5603 47.0365 36.5603C48.3319 36.5603 49.3824 35.5649 49.3824 34.3373C49.3824 33.7655 49.1479 33.2501 48.7729 32.856Z" fill="white"/>
        </svg>
      </div>
    </div>
    <div class="upload">
      <div class="title">Sample</div>
      <ul class="sample-list">
        <li class="sample" v-for="(sam, index) in sampleList" :key="sam.id" @click="goPre(sampleList, index)">
          <div class="show-img" :style="{ 'background-image': `url(${sam.img})`}">
            <img v-if="sam.recommended" src="@/assets/images/common/recommended.png" alt="">
          </div>
          <div class="content" v-text="sam.content"></div>
        </li>
      </ul>
    </div>
    <c-button name="Submit" @buttonClick="submitForm"></c-button>
    <Popup v-model="showSimInformation" position="bottom">
      <img class="pup-close" @click="showSimInformation = false" src="@/assets/images/common/close.png" alt="png">
      <div class="pup-sim-information">
        <div class="pup-title">
          <span v-for="(item, index) in pupTitle" :key="index" :class="{ 'on': active === index }" @click="titleHandle(index)">{{ item }}</span>
        </div>
        <section class="pup-main">
          <div class="pup-header">
            <h1>How to get the SIM information?</h1>
            <div class="step">
              <div>
                <i></i><em>Step 1</em><span>Click to open the {{ active === 0 ? 'MyMTN' : 'MyAirtel' }} website or APP</span>
              </div>
            </div>
            <div class="website-download">
              <a @click="websiteDownload(0)">Click to open website ></a>
              <a @click="websiteDownload(1)">Click to download APP ></a>
            </div>
            <p ref="copyContent">* If clicking the button fails to redirect,please copy this URL and open it in your browser: {{ active === 0 ? 'https://mymtn.com.ng/' : 'https://www.bd.airtel.com/en/auth/login' }}</p>
            <span class="copy" @click="copy">Copy</span>
          </div>
          <div class="pup-main-box" v-show="active === 0">
            <div class="step">
              <div>
                <i></i><em>Step 2</em><span>Log in with registered mobile number and Click the button in the upper right corner of the page</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-0-2.png" alt="png">
            <div class="step">
              <div>
                <i></i><em>Step 3</em><span>Click "My ProFile" to enter the details page</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-0-3.png" alt="png">
            <div class="step">
              <div>
                <i></i><em>Step 4</em><span>Screenshot of "My ProFile" details page and save it, please do not format or obscure any information in the screenshot</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-0-4.png" alt="png">
          </div>
          <div class="pup-main-box"  v-show="active === 1">
            <div class="step">
              <div>
                <i></i><em>Step 2</em><span>Log in with registered mobile number and Click "Manage Account"</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-1-2.png" alt="png">
            <div class="step">
              <div>
                <i></i><em>Step 3</em><span>Click "Sim Settings"</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-1-3.png" alt="png">
            <div class="step">
              <div>
                <i></i><em>Step 4</em><span>Click "Sim Registration"</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-1-4.png" alt="png">
            <div class="step">
              <div>
                <i></i><em>Step 5</em><span>Screenshot of "Sim Registration" details page and save it, please do not format or obscure any information in the screenshot</span>
              </div>
            </div>
            <img src="@/assets/images/common/step-1-5.png" alt="png">
          </div>
        </section>
      </div>
    </Popup>
  </div>
</template>

<script>
import sample1 from '@/assets/images/common/sample1.png';
import sample2 from '@/assets/images/common/sample2.png';
import MTN1 from '@/assets/images/common/MTN-1.jpg';
import MTN2 from '@/assets/images/common/MTN-2.jpg';
import MTN3 from '@/assets/images/common/MTN-3.jpg';
import Airtel1 from '@/assets/images/common/Airtel-1.jpg';
import Airtel2 from '@/assets/images/common/Airtel-2.jpg';
import {
  openFeedBackFile,
  getOsVersionCode,
  getCustId,
  gotoHomeActivity
} from "@/assets/js/native";
import { decode } from "js-base64";
import api from "@/api/interface";
import { mapState } from 'vuex';
import { ImagePreview } from 'vant';
import { Popup } from 'vant';
import {judgeClient} from "@/assets/js/common";
import Clipboard from "clipboard";
export default {
  name: 'increaseLimit',
  components: {
    Popup
  },
  data() {
    return {
      simList: [{
        id: 0,
        img: MTN1,
        recommended: true,
        content: 'MTN 1'
      }, {
        id: 1,
        img: MTN2,
        recommended: false,
        content: 'MTN 2'
      }, {
        id: 2,
        img: MTN3,
        recommended: false,
        content: 'MTN 3'
      }, {
        id: 3,
        img: Airtel1,
        recommended: false,
        content: 'Airtel1 1'
      }, {
        id: 4,
        img: Airtel2,
        recommended: false,
        content: 'Airtel 2'
      }],
      sampleList: [{
        id: 0,
        img: sample1,
        recommended: true,
        content: 'Bank Account Balance'
      }, {
        id: 1,
        img: sample2,
        recommended: false,
        content: 'Carrier balance'
      }],
      androidVersion: 0,
      acceptType: 'image/*',
      haveUploadFiles: [], // 已上传的附件
      uploadFilesList: [], // 未上传的附件
      fromName: '',
      haveImg: false,
      custId: '',
      chooseFilesIndex: -1,
      showSimInformation: false,
      active: 0,
      pupTitle: ['MTN', 'Airtel'],
      device: '',
      haveSimFiles: [],
      simFilesList: [],
    };
  },
  computed: {
    ...mapState(['uid', 'productSource'])
  },
  mounted() {
    this.report('increase_limit_view');
    this.androidVersion = getOsVersionCode();
    getCustId().then(custId => {
      console.log('getCustId', custId);
      this.custId = custId;
    });
    this.device = judgeClient();
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.fromName = from;
    })
  },

  methods: {
    myBackFun() {
      if (this.fromName) {
        this.$router.go(-1);
      } else {
        gotoHomeActivity('increaseLimit');
      }
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'increaseLimit',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    chooseFiles(index, list) {
      this.chooseFilesIndex = index;
      if (list === 'haveUploadFiles') {
        this.report('increase_limit_upload_click');
      } else {
        this.report('increase_limit_upload_sim_click');
      }
      openFeedBackFile('uploadFilesCallBack', this.acceptType, this.acceptType, true)
      window.uploadFilesCallBack = (res) => {
        const result = JSON.parse(decode(res))
        console.log(result)
        if (result.length > 0) {
          if (this.chooseFilesIndex === -1) {
            let fileCount = this[list].length;
            fileCount += result.length
            if (fileCount > 2) {
              this.$toast('The number of files cannot exceed 2.')
              return
            }
          } else {
            if (result.length > 1) {
              this.$toast('The chosen of files cannot exceed 1.')
              return
            }
          }
          let fileSize = 0
          result.forEach(item => {
            fileSize += parseFloat(item.fileSize)
          })
          if (fileSize > 10485760) {
            this.$toast('The file size cannot exceed 10M.')
            return
          }
          this.fileSize = fileSize;

          let uploadFilesList = ''
          list === 'haveSimFiles' ? uploadFilesList = 'simFilesList' : uploadFilesList = 'uploadFilesList'
          this[uploadFilesList].push(...result);

          this.uploadFile(list, uploadFilesList);
        }
      }
    },
    uploadFile(list, uploadFilesList) {
      this.$loading();
      if (this[uploadFilesList].length > 0) {
        const queryArr = []
        this[uploadFilesList].forEach(item => {
          const query = new Promise((resolve, reject) => {
            this.$http(api.bigDataUrl.uploadFile, {
              data: {
                filePath: item.filePath,
                type: 'complaint',
                uid: this.uid
              },
              addHeader: {
                isUploadFile: true,
                'X-DX-Country': 'NG',
                requestType: 'uploadFile'
              }
            }).then((res) => {
              console.log(res)
              resolve(res.data.id)
            }).catch(() => {
              reject(new Error('Upload failed. Please try again later.'))
            })
          })
          queryArr.push(query)
        })
        Promise.all(queryArr).then(res => {
          console.log(res)
          this.getImgPath(res, list, uploadFilesList)
        }).catch(res => {
          this.$hideLoading();
          this.$toast(res)
        })
      } else {
        this.$toast('Please upload this information!')
      }
    },
    // 获取预览路径（路径有效期为一天）
    getImgPath(ids, list, uploadFilesList) {
      this.$http(api.bigDataUrl.getUrls, {
        data: {
          keys: ids
        },
        addHeader: {
          isBigdata: true,
          requestType: 'complaintUpload'
        }
      }).then((res) => {
        console.log('getImgPath', res.data);
        this.$hideLoading();
        console.log('getImgPath', 'chooseFilesIndex', this.chooseFilesIndex)
        if (this.chooseFilesIndex === -1) {
          ids.forEach((id, index) => {
            this[list].push({
              id: id,
              img: res.data[index]
            })
          })
        } else { // 替换现有图片
          this[list][this.chooseFilesIndex] = {
            id: ids[0],
            img: res.data[0]
          }
          console.log(res.data[0])
          this.$forceUpdate();
        }
        console.log('文件上传显示方法', this[list]);
        this.chooseFilesIndex = -1;
        this[uploadFilesList] = [];
      }).catch((e) => {
        console.log(e);
        if (e.msg) {
          this.$toast(e.msg);
        }
        this.$hideLoading();
      })
    },
    // 提交增信
    submitForm() {
      this.report('increase_limit_submit_click');
      if (this.fileCount < 1) {
        this.$toast('Please upload this information!');
        return;
      }
      if (this.haveSimFiles.length === 0) {
        this.$toast('Please upload the SIM information!');
        return;
      }

      const haveSimFiles = this.haveSimFiles.map((item) => (item.id))
      const haveUploadFiles = this.haveUploadFiles.map((item) => (item.id))
      const attachment_infos = [
        { credit_investigation_type: 'SIM', attachments: haveSimFiles },
        { credit_investigation_type: 'balance', attachments: haveUploadFiles },
      ]
      // 当前用户除了BVN类型外，还会有NIN的用户类型。需要传用户类型给大数据，他们根据类型对idNum做加密入库。
      const customerType = localStorage.getItem('customerType');
      const customerId = localStorage.getItem('customerId');
      this.$http(api.bigDataUrl.creditInvestigationUpload, {
        data: {
          uid: this.uid,
          id_num: customerType === 'NIN' ? customerId : this.custId,
          id_type: customerType === 'NIN' ? 'NIN' : 'BVN',
          product: this.productSource,
          attachment_infos: attachment_infos || []
        },
        addHeader: {
          isBigdata: true,
          requestType: 'complaintUpload'
        }
      }).then((res) => {
        console.log(res);
        this.report('increase_limit_submit_success');
        this.$hideLoading();
        // 退出页面
        setTimeout(() => {
          this.myBackFun();
        }, 2500)
      }).catch((e) => {
        console.log(e);
        if (e.msg) {
          this.$toast(e.msg);
        }
        this.$hideLoading();
      })
    },
    goPre(list, index) {
      let imgList = []
      list.forEach((item) => {
        imgList.push(item.img);
      });
      console.log('imgList', imgList);
      if (imgList.length > 0) {
        ImagePreview({
          images: imgList,
          startPosition: index,
          onClose() {
          },
        });
      }
    },
    simInformation() {
      this.showSimInformation = true
      this.report('increase_limit_get_sim_pop_view');
    },
    titleHandle(index) {
      this.active = index
    },
    websiteDownload(index) {
      if (index === 0) {
        if (this.active === 0) {
          location.href = 'https://mymtn.com.ng/'
        } else {
          location.href = 'https://www.bd.airtel.com/en/auth/login'
        }
      } else {
        if (this.active === 0) {
          if (this.device === 'Android') {
            location.href = 'https://play.google.com/store/apps/details?id=com.mtn1app&hl=en'
          } else {
            location.href = 'https://apps.apple.com/us/app/mymtn/id977968085'
          }
        } else {
          if (this.device === 'Android') {
            location.href = 'https://play.google.com/store/apps/details?id=com.airtel.africa.selfcare&hl=en'
          } else {
            location.href = 'https://apps.apple.com/us/app/my-airtel/id1028593633'
          }
        }
      }
    },
    copy() {
      const clipboard = new Clipboard('.copy', {
        text: () => {
          return this.active === 0 ? 'https://mymtn.com.ng/' : 'https://www.bd.airtel.com/em/auth/login';
        }
      });
      clipboard.on('success', () => {
        this.$toast('copy successful')
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.increase-limit{
  min-height: 100%;
  background: #F9FAFF;
  padding-bottom: 80px;
  .c-header{
    left: 0;
  }
  .top{
    padding: 76px 20px 20px 20px;
    background: #ffffff;
    position: relative;
    margin-bottom: 12px;
    .sub-title{
      color: #1B3155;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-transform: capitalize;
      text-align: left;
      margin-bottom: 12px;
    }
    .tips{
      color: #919DB3;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
    }
    img{
      width: 45px;
      height: 45px;
      position: absolute;
      right: 0px;
      bottom: 20px;
    }
  }
  .upload{
    padding: 0 20px 20px 20px;
    background: #ffffff;
    .title{
      color: #1B3155;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      > span {
        color: red;
        padding-right: 2px;
      }
    }
    .upload-p {
      color: #919DB3;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      margin: 5px 0 15px;
      span {
        color: #29ABE2;
      }
    }
    .upload-item{
      height: 95px;
      width: 320px;
      padding: 15px 12px;
      justify-content: space-between;
      align-items: center;
      border-radius: 6px;
      border: 1px solid $themeColor;
      margin-top: 10px;
      justify-content: center;
      align-items: center;
      display: flex;
      // background-position:center center;
      background-size:320px 95px;
      background-repeat: no-repeat;
      box-sizing: border-box;
      svg{
        width: 40px;
        height: 40px;
        circle{
          fill: $themeColor;
        }
      }
    }
    .upload-item-len {
      width: 162px;
      display: inline-flex;
      &:last-child {
        margin-left: 10px;
      }
    }
    .uploaded-img{
      width: 100px;
      width: 200px;
    }
    .sample-list{
      display: flex;
      margin-top: 12px;
      justify-content: space-around;
      .sample{
        width: 100px;
        .show-img{
          width: 100px;
          height: 73px;
          background-size: 100px 73px;
          position: relative;
          img{
            width: 64px;
            height: 18px;
            position: absolute;
            right: -4px;
            top: -14px;
          }
        }
        .content{
          color: #858585;
          text-align: center;
          font-family: Noto Sans;
          font-size: 12px;
          transform: scale(.84);
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-top: 12px;
        }
      }
    }
    .sim-list {
      flex-wrap: wrap;
      justify-content: left;
      .sample {
        margin-left: 10px;
      }
    }
  }
  .c-button{
    position: fixed;
    bottom: 40px;
  }
  .van-popup{
    background: rgba(120,120,120,0);
    .pup-close{
      width: 35px;
      height: 35px;
      position: absolute;
      right: 10px;
      top: 0;
      z-index: 999;
    }
    .pup-sim-information {
      padding-top: 40px;
    }
    .pup-title {
      position: fixed;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #979797;
      font-family: Avenir;
      font-size: 14px;
      font-weight: 500;
      background: #F5F5F5;
      z-index: 2;
      span {
        flex: 1;
        line-height: 40px;
        &.on {
          color: #02B17B;
          background: #fff;
        }
      }
    }
    .pup-main {
      background: #ffffff;
      padding: 50px 20px 0;
      overflow-y: scroll;
      overflow-x: hidden;
      height: 550px;
      .pup-header {
        overflow: hidden;
        margin-bottom: 25px;
        h1 {
          color: #555;
          text-align: center;
          font-family: Roboto;
          font-size: 16px;
          font-weight: 500;
          margin: 14px auto;
        }
        .website-download {
          display: flex;
          justify-content: space-between;
          padding: 0 15px;
          a {
            font-family: Roboto;
            font-size: 12px;
            font-weight: 600;
            color: #02B17B;
            text-decoration: underline;
          }
        }
        p {
          color: #919DB3;
          font-family: Roboto;
          font-size: 12px;
          font-weight: 400;
          margin: 20px 0 10px;
          text-align: left;
        }
        .copy {
          float: right;
          padding: 4px 15px;
          border-radius: 4px;
          border: 1px solid #02B179;
          background: #FFF;
          color: #02B179;
          text-align: center;
          font-family: Roboto;
          font-size: 12px;
          font-weight: 500;
        }
      }
      .step {
        margin: 0 0 14px;
        position: relative;
        min-height: 35px;
        > div {
          overflow: hidden;
          > i {
            float: left;
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background-color: #41CFA3;
          }
          > em {
            float: left;
            color: #1B3155;
            font-family: Roboto;
            font-size: 14px;
            font-weight: 600;
            margin-left: -5px;
            margin-top: -2px;
          }
          > span {
            color: #536887;
            font-family: Roboto;
            font-size: 12px;
            font-weight: 500;
            text-align: left;
            position: absolute;
            left: 65px;
          }
        }
      }

      .pup-main-box {
        > img {
          width: 200px;
          margin: 20px 0 20px 20px;
        }
      }
    }
  }
}

</style>
