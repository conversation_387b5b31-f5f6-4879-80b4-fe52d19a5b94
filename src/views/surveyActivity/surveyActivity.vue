<template>
  <div class="survey-activity">
    <c-header titleName="" :backFun="myBackFun"></c-header>
    <img src="@/assets/images/surveyActivity/1.jpg" alt="jpg">
    <img src="@/assets/images/surveyActivity/2.jpg" alt="jpg">
    <img src="@/assets/images/surveyActivity/3.jpg" alt="jpg">
    <img src="@/assets/images/surveyActivity/4.jpg" alt="jpg">
    <img src="@/assets/images/surveyActivity/5.jpg" alt="jpg">
    <img @click="withdrawNow" src="@/assets/images/surveyActivity/6.jpg" alt="jpg">
    <section class="amount-box">
      <main class="amount-list">
        <i>Amount increase list</i>
        <div id="list">
          <section  :class="{'animate-up': animateUp}">
            <div v-for="(item, index) in amountList" :key="index"><span>{{ item.phone }}</span><em>₦ {{ item.amount | singleToThousands }}</em></div>
          </section>
        </div>
      </main>
    </section>
  </div>
</template>
<script>
import {gotoHomeActivity} from "@/assets/js/native";

export default {
  name: 'surveyActivity',
  components: {

  },
  data() {
    return {
      amountList: [
        { phone: '712****155', amount: 20 },
        { phone: '819****280', amount: 200 },
        { phone: '908****012', amount: 2000 },
        { phone: '903****318', amount: 200 },
        { phone: '811****599', amount: 200 },
        { phone: '914****208', amount: 2000 },
        { phone: '716****070', amount: 200 },
        { phone: '912****052', amount: 20000 },
        { phone: '802****842', amount: 200 },
        { phone: '902****064', amount: 20 },
        { phone: '803****955', amount: 2000 },
        { phone: '901****066', amount: 200000 },
        { phone: '906****843', amount: 20000 },
        { phone: '709****920', amount: 2000 },
        { phone: '905****977', amount: 20 },
        { phone: '703****930', amount: 2000 },
        { phone: '708****405', amount: 20 },
        { phone: '902****789', amount: 20000 },
        { phone: '901****712', amount: 200000 },
        { phone: '905****911', amount: 2000 },
        { phone: '913****521', amount: 20 },
        { phone: '713****956', amount: 200000 },
        { phone: '715****906', amount: 2000 },
        { phone: '716****669', amount: 200 },
        { phone: '803****322', amount: 20 },
        { phone: '909****244', amount: 2000 },
        { phone: '816****332', amount: 20000 },
        { phone: '911****911', amount: 20 },
        { phone: '916****507', amount: 2000 },
        { phone: '903****955', amount: 20 },
        { phone: '913****545', amount: 200000 },
        { phone: '715****436', amount: 20 },
        { phone: '916****167', amount: 20 },
        { phone: '900****789', amount: 2000 },
        { phone: '703****944', amount: 20 },
        { phone: '715****702', amount: 200000 },
        { phone: '904****954', amount: 2000 },
        { phone: '904****179', amount: 20 },
        { phone: '908****197', amount: 20000 },
        { phone: '808****469', amount: 200 },
        { phone: '906****262', amount: 20 },
        { phone: '905****154', amount: 20 },
        { phone: '902****228', amount: 2000 },
        { phone: '903****138', amount: 2000 },
        { phone: '903****181', amount: 20 },
        { phone: '908****881', amount: 2000 },
        { phone: '707****223', amount: 20 },
        { phone: '906****512', amount: 200 },
        { phone: '902****644', amount: 200 },
        { phone: '908****608', amount: 20 },
        { phone: '706****155', amount: 2000 },
        { phone: '702****033', amount: 20 },
        { phone: '707****979', amount: 200000 },
        { phone: '703****934', amount: 20000 },
        { phone: '909****923', amount: 20 },
        { phone: '912****999', amount: 20 },
        { phone: '816****208', amount: 20 },
        { phone: '919****267', amount: 20000 },
        { phone: '916****977', amount: 20 },
        { phone: '813****287', amount: 20 },
        { phone: '819****243', amount: 20000 },
        { phone: '916****966', amount: 200 },
        { phone: '815****977', amount: 20 },
        { phone: '915****681', amount: 2000 },
        { phone: '915****284', amount: 200 },
        { phone: '917****254', amount: 200 },
        { phone: '910****609', amount: 200000 },
        { phone: '910****154', amount: 2000 },
        { phone: '902****767', amount: 2000 },
        { phone: '909****787', amount: 20 },
        { phone: '702****745', amount: 200000 },
        { phone: '700****934', amount: 200000 },
        { phone: '700****966', amount: 200 },
        { phone: '706****970', amount: 200 },
        { phone: '903****237', amount: 20 },
        { phone: '905****273', amount: 20 },
        { phone: '803****247', amount: 20 },
        { phone: '804****845', amount: 20000 },
        { phone: '804****188', amount: 20000 },
        { phone: '906****131', amount: 200 },
        { phone: '908****754', amount: 200000 },
        { phone: '908****870', amount: 20 },
        { phone: '904****166', amount: 2000 },
        { phone: '812****969', amount: 20000 },
        { phone: '813****145', amount: 20 },
        { phone: '911****936', amount: 200 },
        { phone: '711****218', amount: 2000 },
        { phone: '903****208', amount: 20 },
        { phone: '703****288', amount: 20 },
        { phone: '903****797', amount: 2000 },
        { phone: '903****808', amount: 2000 },
        { phone: '903****907', amount: 2000 },
        { phone: '903****803', amount: 20000 },
        { phone: '904****941', amount: 200 },
        { phone: '807****444', amount: 200 },
        { phone: '902****977', amount: 200 },
        { phone: '703****956', amount: 200 },
        { phone: '911****366', amount: 2000 },
        { phone: '801****933', amount: 20 },
        { phone: '912****852', amount: 20 },
        { phone: '711****789', amount: 200 },
        { phone: '901****232', amount: 20000 },
        { phone: '915****256', amount: 200 },
      ],
      animateUp: false,
      timer: null
    }
  },
  created() {
    this.withdrawReport('withdraw_retainactive_view');
  },
  mounted() {
    this.timer = setInterval(this.scrollAnimate, 1500);
  },
  destroyed() {
    clearInterval(this.timer)
  },
  methods: {
    withdrawNow() {
      this.withdrawReport('withdraw_retainactive_withdrawnow_click');
      this.$router.push({ path: '/', query: { surveyActivity: 'surveyActivity'}});
    },
    myBackFun() {
      this.withdrawReport('withdraw_retainactive_back_click');
      gotoHomeActivity('surveyActivity');
    },
    withdrawReport(eventName) {
      this.$store.dispatch('reportEvent', {
        page: 'index',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    scrollAnimate() {
      this.animateUp = true
      setTimeout(() => {
        this.amountList.push(this.amountList[0])
        this.amountList.shift()
        this.animateUp = false
      }, 500)
    }
  }
}
</script>
<style lang="scss" scoped>
@keyframes listAnt {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, -100%, 0);
  }
}
.survey-activity {
  padding-top: 55px;
   img {
    width: 100%;
    max-width: 100%;
    display: block;
  }
  .amount-box {
    position: relative;
    padding: 15px 20px 40px;
    background: linear-gradient(180deg,#FFEAE2 18.54%,  #FFF7E7 100%);
    .amount-list {
      padding: 0 15px 5px;
      background-color: #fff;
      border-radius: 9px;
      > i {
        width: 220px;
        height: 40px;
        line-height: 35px;
        color: #FFF5D9;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 700;
        font-style: inherit;
        display: block;
        margin: 0 auto 15px;
        background-image: url("../../assets/images/surveyActivity/amount.png");
        background-size: 100% 100%;
      }
      > div {
        overflow: hidden;
        height: 180px;
        > section {
          animation: listAnt 0s linear infinite;
          > div {
            display: flex;
            opacity: .3;
            font-size: 14px;
            justify-content: space-between;
            margin-bottom: 20px;
            span {
              color: #1B3155;
              font-family: Roboto;
              font-weight: 400;
            }
            em {
              color: #FF1C1C;
              font-family: Roboto;
              font-weight: 700;
            }
          }
          > div:nth-of-type(2) {
            opacity: 1;
            font-size: 19px;
            background: linear-gradient(270deg, rgba(255, 247, 238, 0.13) 14.41%, #FFF7EE 45.93%, rgba(255, 247, 238, 0.12) 89.06%);
          }
        }
      }
    }
    .animate-up {
      transition: all 0.5s ease-in-out;
      transform: translateY(-40px);
    }
  }
}
</style>
