
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
    name: 'forgetPin',
    components: {
      baseFP: () => import(/* webpackChunkName: "baseFp" */ './page/forgetPinBase.vue'),
      wayacredit: () => import(/* webpackChunkName: "forgetPinWayacredit" */ './page/forgetPinWayacredit.vue'),
      forgetPinCommon1: () => import(/* webpackChunkName: "forgetPinCommon1" */ './page/forgetPinCommon1.vue'),
    },
    data() {
        return {
          componentTag: 'baseFP',
          to: '',
          from: '',
        }
    },
    created() {
      if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.forgetPin) {
        this.componentTag = globalConfig.pageStyleSetting.forgetPin
      }
    },
    beforeRouteEnter (to, from, next) {
      next(vm => {
        vm.to = to;
        vm.from = from;
        if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
          vm.$refs.child.beforeRouteEnter(to, from);
        }
      })
    }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
