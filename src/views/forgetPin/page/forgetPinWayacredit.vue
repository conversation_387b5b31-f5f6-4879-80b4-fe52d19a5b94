<template>
  <div class="forget-pin" @click="hide($event)">
    <c-header titleName="Reset your PIN" :styType="'b'"></c-header>
    <div class="des">
      Please input your BVN, we will send SMS OTP to your phone number linked to your BVN.
    </div>
    <div class="detail">
      <div class="set">
        <div class="title">BVN</div>
        <div class="bvn">
          <div class="warn-flag" v-show="bvn.length == 0">*</div>
          <input :class="{'have-input': bvn.length !== 0}" placeholder="BVN" @keyup="BVNUp($event)" v-model="bvn" class="input" type="tel">
          <img class="cancel" v-show="bvn.length > 0" @click="cancel()" :src="cancelButton" alt="">
          <img class="warn" v-show="startWarn" @click="showWarn()" :src="loanNotification" alt="">
          <div class="tips" v-show="showWarnTips">Please enter the correct bank verification number</div>
        </div>
        <div class="title verification">Verification</div>
        <div class="ver">
          <input class="otp" @keyup="OTPUp($event)"  v-model="otp" type="number">
          <div class="get-otp" :class="{'input-otp': bvn.length > 0 && !sending}" @click="getValidCode4nc()" v-text="otpContent"></div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="next" :class="{'start-next': otp.length === 6}" @click="checkValidCodeBVN4nc()">Next</div>
    </div>
  </div>
</template>

<script>
import publicMixns from '../forgetPinMixins.js'
export default {
    name: 'forgetPinWayacredit',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.forget-pin{
  height: 100%;
  background: #1E1E20;
  padding-top: 56px;
  box-sizing: border-box;
  .des{
    color: rgba(255, 255, 255, 0.50);
    font-family: HarmonyOS Sans SC;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    padding: 0 18px;
    text-align: left;
  }
  .detail{
    border-radius: 18px;
    border: 0.5px solid #FFF;
    background: rgba(40, 42, 48, 0.80);
    padding: 12px 16px;
    height: 135px;
    margin-left: 22px;
    margin-right: 22px;
    margin-top: 22px;
    .set {
      height: 124px;
    }
    .title{
      padding-top: 10px;
      text-align: left;
      color: #00FFAD;
      font-size: 12px;
      height: 12px;
    }
    .bvn{
      position: relative;
      height: 35px;
      background: #141416;
      display: flex;
      align-items: center;
      border-radius: 5px;
      margin-top: 5px;
      .warn-flag{
        color: red;
        font-size: 12px;
        width: 20px;
        height: 35px;
        text-align: center;
        line-height: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .input{
        width: calc(100% - 80px);
        height: 30px;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        background: #141416;
        color: #fff;
        &.have-input{
          padding-left: 7px;
        }
        &::placeholder{
          color: rgba(255, 255, 255, 0.30);
        }
      }
      .cancel{
        width: 20px;
        height: 20px;
        position: absolute;
        right: 7px;
        bottom: 6px;
      }
      .warn{
        width: 20px;
        height: 20px;
        position: absolute;
        right: 7px;
        bottom: 6px;
        z-index: 2;
      }
      .tips{
        position: absolute;
        background: #ffe999;
        z-index: 10;
        color: red;
        border-radius: 4px;
        left: 19px;
        top: 28px;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        padding: 0 5px;
      }
    }
    .ver{
      position: relative;
      background: #141416;
      height: 35px;
      display: flex;
      align-items: center;
      border-radius: 5px;
      justify-content: space-between;
      margin-top: 5px;
      input {
        background: #141416;
        color: #fff;
        &::placeholder{
          color: rgba(255, 255, 255, 0.30);
        }
      }
      .otp{
        width: calc(100% - 80px);
        height: 30px;
        border: none;
        border-radius: 4px;
        padding-left: 7px;
        font-size: 12px;
      }
      .get-otp{
        font-size: 14px;
        background: #00FFAD;
        width: 65px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #1E1E20;
        margin-right: 5px;
        border-radius: 5px;
        &.input-otp{
          background: $themeColor;
        }
      }
    }
  }
  .bottom{
    position: fixed;
    bottom: 0;
    height: 46px;
    display: flex;
    justify-content: flex-end;
    width: 100%;
    align-items: center;
    border-top: 1px solid #e2e4e7;
    .next{
      font-size: 16px;
      background: #00FFAD;
      width: 65px;
      height: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #1E1E20;
      border-radius: 5px;
      margin-right: 15px;
      &.start-next{
        background: $themeColor;
      }
    }
  }
}
</style>
