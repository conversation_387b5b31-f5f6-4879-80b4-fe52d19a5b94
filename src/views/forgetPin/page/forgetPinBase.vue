<template>
  <div class="forget-pin" @click="hide($event)">
    <c-header titleName="Reset your PIN"></c-header>
    <div class="des">
      Please input your BVN and then get OTP, we will send SMS OTP to your registered phone number.
    </div>
    <div class="detail">
      <div class="set">
        <div class="title">BVN</div>
        <div class="bvn">
          <div class="warn-flag" v-show="bvn.length == 0">*</div>
          <input :class="{'have-input': bvn.length !== 0}" placeholder="BVN" @keyup="BVNUp($event)" v-model="bvn" class="input" type="tel">
          <img class="cancel" v-show="bvn.length > 0" @click="cancel()" :src="cancelButton" alt="">
          <img class="warn" v-show="startWarn" @click="showWarn()" :src="loanNotification" alt="">
          <div class="tips" v-show="showWarnTips">Please enter the correct bank verification number</div>
        </div>
        <div class="title verification">Verification</div>
        <div class="ver">
          <input class="otp" @keyup="OTPUp($event)"  v-model="otp" type="number" maxlength="4">
          <div class="get-otp" :class="{'input-otp': bvn.length > 0 && !sending}" @click="getValidCode4nc()" v-text="otpContent"></div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="next" :class="{'start-next': otp.length === 4}" @click="checkValidCodeBVN4nc()">Next</div>
    </div>
  </div>
</template>

<script>
import publicMixns from '../forgetPinMixins.js'
export default {
    name: 'forgetPinBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.forget-pin{
  .des{
    margin-top: 56px;
    font-size: 12px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: left;
    color: #919db3;
    line-height: 18px;
    padding: 0 18px;
  }
  .detail{
    background: #d5d8ed;
    margin: 0 10px;
    margin-top: 10px;
    border-radius: 5px;
    padding: 0 10px;
    height: 135px;
    .set {
      height: 124px;
    }
    .title{
      padding-top: 10px;
      text-align: left;
      color: #67bed5;
      font-size: 12px;
      height: 12px;
      &.verification{
        // padding-top: 20px;
      }
    }
    .bvn{
      position: relative;
      height: 35px;
      background: #ffffff;
      display: flex;
      align-items: center;
      border-radius: 5px;
      margin-top: 5px;
      .warn-flag{
        color: red;
        font-size: 12px;
        width: 20px;
        height: 35px;
        text-align: center;
        line-height: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .input{
        width: calc(100% - 80px);
        height: 30px;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        &.have-input{
          padding-left: 7px;
        }
        &::placeholder{
          color: #919db3;
          // padding-left: 7px;
        }
      }
      .cancel{
        width: 20px;
        height: 20px;
        position: absolute;
        right: 7px;
        bottom: 6px;
      }
      .warn{
        width: 20px;
        height: 20px;
        position: absolute;
        right: 7px;
        bottom: 6px;
        z-index: 2;
      }
      .tips{
        position: absolute;
        background: #ffe999;
        z-index: 10;
        color: red;
        border-radius: 4px;
        left: 19px;
        top: 28px;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        padding: 0 5px;
      }
    }
    .ver{
      position: relative;
      background: #ffffff;
      height: 35px;
      display: flex;
      align-items: center;
      border-radius: 5px;
      justify-content: space-between;
      margin-top: 5px;
      .otp{
        width: calc(100% - 80px);
        height: 30px;
        border: none;
        border-radius: 4px;
        padding-left: 7px;
        // margin-top: 5px;
        font-size: 12px;
      }
      .get-otp{
        font-size: 14px;
        background: #91c4b7;
        width: 65px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        margin-right: 5px;
        border-radius: 5px;
        &.input-otp{
          background: $themeColor;
        }
      }
    }
  }
  .bottom{
    position: fixed;
    bottom: 0;
    height: 46px;
    display: flex;
    justify-content: flex-end;
    width: 100%;
    align-items: center;
    border-top: 1px solid #e2e4e7;
    .next{
      font-size: 16px;
      background: #91c4b7;
      width: 65px;
      height: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ffffff;
      border-radius: 5px;
      margin-right: 15px;
      &.start-next{
        background: $themeColor;
      }
    }
  }
}
</style>
