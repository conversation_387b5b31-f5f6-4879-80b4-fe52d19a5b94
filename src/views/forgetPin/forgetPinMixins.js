import loanNotification from '@/assets/images/loan-notification.png';
import cancelButton from '@/assets/images/cancel-button.png';
import { getNetwork, getOsName, getDefaultTimezone, getOsVersion, getCurrentAppVersionName, getCurrentAppVersion } from "@/assets/js/native";
import api from "@/api/interface";

export default {
  name: 'forgetPin',
  data() {
    return {
      loanNotification,
      cancelButton,
      bvn: '',
      otp: '',
      otpContent: 'Get OTP',
      sending: false,
      timer: '',
      allTime: 60,
      showWarnTips: false,
      startWarn: false,
      network: '',
      osName: '',
      defaultTimezone: '',
      osVersion: '',
      currentAppVersionName: ''
    };
  },

  methods: {
    // 获取验证码
    getValidCode4nc() {
      let self = this;
      if (self.bvn.length < 10) {
        self.startWarn = true;
        return;
      }
      console.log(self.sending, self.timer);
      if (self.sending || self.timer) {
        return;
      } else {
        self.sending = true;
      }
      self.$toast({
        message: 'The verification code has been sent to your registered phone number',
        position: 'top'
      });
      return new Promise((resolve, reject) => {
        self.$http(api.withdraw.getValidCode4nc, {
          data:{
              "appVersion": self.currentAppVersionName,
              "osVersion": self.osVersion,
              "timezone": self.defaultTimezone,
              "receiverMobile": self.bvn,
              "reasonType":"7",
              "osName": self.osName,
              "network": self.network
          }
      }).then(res => {
          self.sending = false;
          console.log('getValidCode4nc---success', res);
          self.timer = setInterval(function() {
            self.allTime--;
            // console.log('self.allTime', self.allTime);
            if (self.allTime !== 0) {
              self.otpContent = `${self.allTime}s`;
            } else {
              self.resetStatus();
            }
          }, 1000);
          resolve();
        }).catch(e => {
          console.log('getValidCode4nc---err', e);
          self.sending = false;
          reject();
        });
      });
    },
    resetStatus() {
      let self = this;
      clearInterval(self.timer);
      self.allTime = 60;
      self.otpContent = 'Get OTP';
      self.sending = false;
      self.timer = ''
    },
    // 判断验证码是否有效。
    checkValidCodeBVN4nc() {
      let self = this;
      if (self.bvn.length < 10 || self.otp.length < 4) {
        return;
      }
      return new Promise((resolve, reject) => {
        self.$http(api.withdraw.checkValidCodeBVN4nc, {
          data:{
              "appVersion": self.currentAppVersionName,
              "osVersion": self.osVersion,
              "validCode": self.otp,
              "timezone": self.defaultTimezone,
              "receiverMobile": self.bvn,
              "reasonType":"7",
              "osName": self.osName,
              "network": self.network
          }
        }).then(res => {
          console.log(res);
          self.resetStatus();
          self.$router.push({ path:'/setPin' });
          resolve();
        }).catch(e => {
          console.log(e);
          self.resetStatus();
          reject();
        });
      });
    },
    BVNUp(e) {
      const self = this;
      e.target.value = self.bvn = self.bvn.replace(/\D+/g, '');
      if (self.bvn.length > 11) {
        self.bvn = `${self.bvn}`.slice(0, 11);
        e.target.value = self.bvn;
        e.preventDefault();
      }
    },
    OTPUp(e) {
      const self = this;
      let value = e.target.value;
      if (value === "" || value === " " || isNaN(value)) { // 处理.+-这类符号
        e.target.value = '';
      }
      if (self.otp.length > 4) {
        self.otp = `${self.otp}`.slice(0, 4);
        e.target.value = self.otp;
        e.preventDefault();
      }
    },
    showWarn() {
      this.showWarnTips = !this.showWarnTips;
    },
    hide(e) {
      if (e.srcElement._prevClass != 'warn') {
        this.showWarnTips = false;
      }
    },
    cancel() {
      this.bvn = ''
    }
  },
  mounted() {
    let self = this;
    getNetwork().then(network => {
      self.network = network;
    });
    getOsName().then(osName => {
      self.osName = osName;
    });
    getDefaultTimezone().then(defaultTimezone => {
      self.defaultTimezone = defaultTimezone;
    });
    getOsVersion().then(osVersion => {
      self.osVersion = osVersion;
    });
    getCurrentAppVersionName().then(currentAppVersionName => {
      self.currentAppVersionName = currentAppVersionName;
    });
    getCurrentAppVersion().then(appVersion => {
      console.log('appVersion', appVersion);
    });
    window.scrollTo(0, 0);
  }
};