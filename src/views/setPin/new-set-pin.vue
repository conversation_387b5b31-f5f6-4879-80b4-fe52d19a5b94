
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
    name: 'setPin',
    components: {
      baseSP: () => import(/* webpackChunkName: "baseSP" */ './page/setPinBase.vue'),
      wayacredit: () => import(/* webpackChunkName: "setPinWayacredit" */ './page/setPinWayacredit.vue'),
      setPinCommon1: () => import(/* webpackChunkName: "setPinCommon1" */ './page/setPinCommon1.vue'),
    },
    data() {
        return {
          componentTag: 'baseSP',
          to: '',
          from: '',
        }
    },
    created() {
      if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.setPin) {
        this.componentTag = globalConfig.pageStyleSetting.setPin
      }
    },
    beforeRouteEnter (to, from, next) {
      next(vm => {
        vm.to = to;
        vm.from = from;
        if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
          vm.$refs.child.beforeRouteEnter(to, from);
        }
      })
    }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
