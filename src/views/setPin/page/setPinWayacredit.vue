<template>
  <div class="pin-container">
    <c-header titleName="" :backFun="myBackFun" :styType="'b'"></c-header>
      <CPage className="set-pin">
        <div class="title" v-text="`Set your ${channel} PIN`"></div>
        <div class="re-input-tips" v-show="reEnter">Please re-enter your PIN to confirm</div>
        <ul class="pin-list">
          <li v-for="(pin, index) in pinList" :key="index + 'key'">
            <input v-model="pinList[index].value" :ref="'input' + index" @keyup="inputPin($event, index)" type="tel" />
            <div class="hide-control" :class="{'control': pin.value !== '' || !pin.showInput}">
              <div v-if="pin.value !== ''" class="hide"></div>
            </div>
          </li>
        </ul>
        <div class="tips">
          Please set 4-digit PIN for your loan disbursement password. Keep your PIN privately, don't tell anyone else.
        </div>
      </CPage>
  </div>
</template>

<script>
import publicMixns from '../setPinMixins.js'
export default {
    name: 'setPinWayacredit',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.pin-container {
  height: 100%;
  background: #1E1E20;
}
  .set-pin{
    &.c-page{
      height: 100%;
      background: transparent;
      margin-top: 0;
      min-height: auto;
      padding-top: 56px;
      box-sizing: border-box;
    }
    .title{
      height: 18px;
      font-size: 18px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      color: #fff;
      line-height: 18px;
      text-align: center;
    }
    .re-input-tips{
      margin-top: 10px;
      font-size: 12px;
    }
    .pin-list{
      display: flex;
      margin-top: 23px;
      padding: 0 45px;
      li {
        width: 50px;
        height: 51px;
        margin-right: 21px;
        position: relative;
        .hide-control{
          width: 50px;
          height: 50px;
          border-bottom: 1px solid #bfc4ca;
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          top: 0px;
          background: #ffffff;
          z-index: -1;
          &.control {
            z-index: 2;
          }
          .hide{
            width: 12px;
            height: 12px;
            background: #00FFAD;
            border-radius: 12px;
          }
        }
        input{
          width: 50px;
          height: 50px;
          border: none;
          border-bottom: 1px solid #bfc4ca;
          font-size: 25px;
          text-align: center;
        }
      }
    }
    .tips{
      height: 36px;
      font-size: 12px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: center;
      color: #919db3;
      line-height: 18px;
      margin-top: 37px;
    }
  }
</style>