<template>
  <div class="pin-container">
    <c-header titleName="" :backFun="myBackFun" :styType="'c'"></c-header>
      <CPage className="set-pin">
        <div class="title" v-text="`Set your ${channel} PIN`"></div>
        <div class="re-input-tips" v-show="reEnter">Please re-enter your PIN to confirm</div>
        <ul class="pin-list">
          <li v-for="(pin, index) in pinList" :key="index + 'key'">
            <input v-model="pinList[index].value" :ref="'input' + index" @keyup="inputPin($event, index)" type="tel" />
            <div class="hide-control" :class="{'control': pin.value !== '' || !pin.showInput}">
              <div v-if="pin.value !== ''" class="hide"></div>
            </div>
          </li>
        </ul>
        <div class="tips">
          Please set 4-digit PIN for your loan disbursement password. Keep your PIN privately, don't tell anyone else.
        </div>
      </CPage>
  </div>
</template>

<script>
import publicMixns from '../setPinMixins.js'
export default {
    name: 'setPinCommon1',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.pin-container {
  height: 100%;
  background: $color;
}
  .set-pin{
    &.c-page{
      height: 100%;
      background: transparent;
      margin-top: 0;
      min-height: auto;
      padding-top: 56px;
      box-sizing: border-box;
    }
    .title{
      height: 18px;
      font-size: 18px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      color: #fff;
      line-height: 18px;
      text-align: center;
    }
    .re-input-tips{
      margin-top: 10px;
      color: rgba(255, 255, 255, 0.70);
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }
    .pin-list{
      display: flex;
      margin-top: 23px;
      padding: 0 45px;
      li {
        width: 50px;
        height: 51px;
        margin-right: 21px;
        position: relative;
        .hide-control{
          width: 50px;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          top: 0px;
          background: rgba(255, 255, 255, 0.20);
          z-index: -1;
          &.control {
            z-index: 2;
          }
          .hide{
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
          }
        }
        input{
          width: 50px;
          height: 50px;
          border: none;
          background: rgba(255, 255, 255, 0.20);
          font-size: 25px;
          text-align: center;
          caret-color: #fff;
          color: transparent;
        }
      }
    }
    .tips{
      height: 36px;
      font-size: 12px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: center;
      color: #919db3;
      line-height: 18px;
      margin-top: 37px;
    }
  }
</style>