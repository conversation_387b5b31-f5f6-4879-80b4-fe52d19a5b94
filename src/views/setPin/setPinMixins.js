import CPage from '@/components/c-page.vue';
import { getAesString, setExistedPin } from "@/assets/js/native";
import { mapState } from 'vuex';
import api from "@/api/interface";


export default {
  name: 'setPin',
  components: {
    CPage
  },
  computed: {
    ...mapState(['channel', 'productSource'])
  },
  data() {
    return {
      pinList: [{ // pinList渲染列表
        value: '',
        showInput: true
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }, {
        value: '',
        showInput: false
      }],
      backupList: [], // pin数组备份
      reEnter: false, // 控制提示
      fromName: ''
    };
  },

  mounted() {
    let self = this;
    window.scrollTo(0, 0);
    self.beforeRouteEnter(self.$parent.to, self.$parent.from);
    self.$nextTick(function() {
      self.$refs['input0'][0].focus();
    });
  },

  methods: {
    // 整体输入控制
    inputPin(e, index) {
      const key = e.keyCode;
      const self = this;
      let value = self.pinList[index].value;
      if (value === "" || value === " " || isNaN(value)) { // 处理.+-这类符号
        e.target.value = '';
      }
      if (key === 8 && index > 0) {
        self.pinList[index].showInput = false;
        self.pinList[index - 1].value = '';
        self.pinList[index - 1].showInput = true;
        self.$refs['input' + (index - 1)][0].focus();
      } else if (self.pinList[index] !== '' && index < 3) {
        if (self.pinList[index].value !== '') {
          self.$refs['input' + (index + 1)][0].focus();
          self.pinList[index].showInput = false;
          self.pinList[index + 1].showInput = true;
        }
      } else if (index === 3) {
        if (self.pinList[index].value !== '') {
          if (self.backupList.length == 0) {
            self.backupList = JSON.parse(JSON.stringify(self.pinList));
            self.pinList = [{ // pinList渲染列表
              value: '',
              showInput: true
            }, {
              value: '',
              showInput: false
            }, {
              value: '',
              showInput: false
            }, {
              value: '',
              showInput: false
            }];
          self.$refs['input0'][0].focus();
          self.pinList[0].showInput = true;
          self.reEnter = true;
          } else {
            if (JSON.stringify(self.backupList) == JSON.stringify(self.pinList)) {
              self.setPin();
            } else { // 两次不一致，重新输入
              self.$toast({
                message: 'Inconsistent transaction password input'
              });
              self.reEnter = false;
              self.$refs['input0'][0].focus();
              self.pinList = [{ // pinList渲染列表
                value: '',
                showInput: true
              }, {
                value: '',
                showInput: false
              }, {
                value: '',
                showInput: false
              }, {
                value: '',
                showInput: false
              }];
              self.backupList = [];
            }
          }
        }
      }
    },
    setPin() {
      let self = this;
      let transactionPassword = '';
      self.pinList.forEach((item, index) => {
        let value = item.value.slice(0, 1);
        // 防止输入多个数值
        self.pinList[index].value = value;
        transactionPassword += value;
      })
      console.log('transactionPassword', self.pinList, transactionPassword);
      getAesString(transactionPassword).then(password => {
        self.$http(api.withdraw.setTransactionPassword4nc, {
          data:{
              "transactionPassword": password
          }
        }).then(res => {
          console.log('setTransactionPassword4nc', res);
          setExistedPin(true); // 保存给客户端
          self.$router.push({ path: '/pinResetStatus', query: {
            fromName: self.fromName
          } });
        }).catch(e => {
          console.log(e);
        });
      });
    },
    myBackFun() {
      let self = this;
      self.$router.back(-1);
    },
    beforeRouteEnter (to, from) {
      this.fromName = from.name;
    }
  }
};