
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
export default {
  name: 'complaint',
  components: {
    baseComplaint: () => import(/* webpackChunkName: "basecoml" */ './page/complaintBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "complaintWayacredit" */ './page/complaintWayacredit.vue'),
  },
  data() {
      return {
        componentTag: 'baseComplaint',
        to: '',
        from: '',
      }
  },
  created() {
    const productSource = localStorage.getItem('productSource')
    if (productSource === 'ponykash') {
      // this.componentTag = 'sasa'
    } else if (productSource === 'wayacredit') {
      this.componentTag = 'wayacredit'
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
