<template>
  <div class="complaint">
    <c-header titleName="Complaint & Feedback" :backFun="myBackFun" :styType="'b'" />
    <div class="contents">
      <div class="form_title"><span>*</span>Complaint Type</div>
      <div class="checkbox" v-for="(item,index) in complaintList" :key="index" @click="chooseReason(index)">
        <img class="check-icon" :src="index === chooseIndex ? require(`@/assets/images/${productSource}/index-paid.png`) : require('@/assets/images/un-paid.png')" alt="">
        <div class="text">{{item}}</div>
      </div>
      <div class="form_title text"><span>*</span>Complaint Details</div>
      <div class="word_wrap">
        <textarea maxlength="2000" v-model.trim="complaintText"></textarea>
        <div>{{complaintText.length}}/2000</div>
      </div>
      <div class="form_title files" v-show="androidVersion > 28"><span>*</span>Attachment</div>
      <div class="upload" v-show="androidVersion > 28">
        <div class="file" @click="uploadFiles" v-show="uploadFilesList.length < 5"></div>
        <div class="list_wrap" v-show="uploadFilesList.length > 0">
          <div class="list" v-for="(item, index) in uploadFilesList" :key="index">{{item.fileName}}<span @click="toDelete(index)"></span></div>
        </div>
      </div>
    </div>
    <div class="btn" @click="submitComplaint" :class="{'disable': disable}">Comfirm</div>
  </div>
</template>

<script>
import publicMixns from '../complaintMixins.js'
export default {
    name: 'complaintWayacredit',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style scoped lang="scss">
  .complaint{
    background: #1E1E20;
    height: 100%;
    .contents{
      padding: 56px 35px 85px 26px;
      height: calc(100% - 150px);
      overflow: scroll;
      font-family: Avenir-Medium;
    }
    .form_title{
      font-size: 14px;
      color: #fff;
      font-weight: 500;
      padding-bottom: 9px;
      text-align: left;
      span{
        color: #ff454f;
        margin-right: 3px;
      }
    }
    .form_title.text{
      padding-top: 16px;
    }
    .form_title.files{
      padding-top: 14px;
      span{
        opacity: 0;
      }
    }
    .checkbox{
      display: flex;
      align-items: center;
      padding: 5px 0;
      .check-icon{
        width: 20px;
        height: 20px;
      }
      .text{
        font-size: 12px;
        color: rgba(255, 255, 255, 0.70);
        font-weight: 500;
        margin-left: 10px;
      }
    }
    .word_wrap{
      height: 82px;
      width: calc(100% - 9px);
      margin-left: 9px;
      border: 1px solid rgba(196,202,213,1);
      box-sizing: border-box;
      textarea{
        border: 0;
        display: block;
        resize: none;
        width: 100%;
        box-sizing: border-box;
        padding: 2px 2px 0;
        height: 58px;
        font-size: 12px;
        color: #fff;
        background: transparent;
      }
      div{
        height: 15px;
        line-height: 15px;
        padding: 0 5px 8px 0;
        font-size: 12px;
        color: #C4CAD5;
        text-align: right;
        font-weight: 500;
      }
    }
    .upload{
      width: calc(100% - 9px);
      margin-left: 9px;
      border: 1px dashed #C4CAD5;
      box-sizing: border-box;
      .file{
        height: 82px;
        width: 100%;
        background: url(../../../assets/images/attachment_ic.png) no-repeat center center;
        background-size: 23px 23px;
      }
      .list_wrap{
        width: 100%;
        padding: 5px 8px;
        box-sizing: border-box;
        border-top: 1px dashed #C4CAD5;
        background: rgba(145, 157, 179, 0.08);
      }
      .list{
        font-size: 12px;
        width: 100%;
        box-sizing: border-box;
        word-break: break-all;
        color: #919DB3;
        font-weight: 500;
        padding: 5px 20px 5px 0;
        position: relative;
        span{
          position: absolute;
          right: 0;
          top: 50%;
          width: 15px;
          height: 15px;
          background: url(../../../assets/images/files.png) no-repeat right center;
          background-size: 15px 15px;
          margin-top: -7px;
        }
      }
    }
    .btn{
      width: calc(100% - 44px);
      height: 42px;
      line-height: 42px;
      text-align: center;
      border-radius: 8px;
      font-size: 18px;
      color: #FFFFFF;
      font-weight: 500;
      background: $themeColor;
      position: fixed;
      left: 22px;
      bottom: 23px;
      &.disable{
        background: #c4cad5;
      }
    }
  }
</style>
