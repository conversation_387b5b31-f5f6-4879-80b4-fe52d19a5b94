import { openFeedBackFile, getOsVersionCode, gotoHomeActivity } from "@/assets/js/native";
import { decode } from "js-base64"
import { mapState } from 'vuex';
import api from "@/api/interface";

export default {
  name: "complaint",
  data() {
    return {
      complaintList: [
        'High interest rate',
        'Disbursement problems',
        'Repayment problems',
        'App crashes',
        'Other systemic problems',
        'Loan rejection',
        'Automatic cash withdrawal',
        'Tricked',
        'Complaints staff',
        'Recommendation',
        'Other'
      ],
      chooseIndex: -1,
      complaintChecked: '',
      complaintText: '',
      uploadFilesList: [],
      fileCount: 0,
      fileSize: 0,
      fromName: '',
      acceptType: 'application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|application/vnd.ms-excel|application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|application/pdf|image/*',
      androidVersion: 0
    }
  },
  computed: {
    ...mapState(['uid', 'productSource']),
    disable() {
      if (this.complaintChecked && this.complaintText) {
        return false
      } else {
        return true
      }
    }
  },
  created() {
    const vm = this;
    vm.androidVersion = getOsVersionCode();
    console.log('vm.androidVersion', vm.androidVersion);
  },
  mounted() {
    this.report('complaint_activity_view')
    this.beforeRouteEnter(this.$parent.to, this.$parent.from);
  },
  destroyed() {
    this.report('complaint_activity_leave')
  },
  methods: {
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'complaint',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    myBackFun() {
      if (this.fromName) {
        this.$router.go(-1);
      } else {
        gotoHomeActivity('complaint');
      }
    },
    uploadFiles() {
      openFeedBackFile('uploadFilesCallBack', this.acceptType, this.acceptType, true)
      window.uploadFilesCallBack = (res) => {
        const result = JSON.parse(decode(res))
        console.log(result)
        if (result.length > 0) {
          let fileCount = this.fileCount
          fileCount += result.length
          if (fileCount > 5) {
            this.$toast('The number of files cannot exceed 5.')
            return
          }
          let fileSize = this.fileSize
          result.forEach(item => {
            fileSize += parseFloat(item.fileSize)
          })
          if (fileSize > 10485760) {
            this.$toast('The file size cannot exceed 10M.')
            return
          }
          this.fileCount = fileCount
          this.fileSize = fileSize
          this.uploadFilesList.push(...result)
        }
      }
    },
    toDelete(index) {
      this.fileCount -= 1
      this.fileSize -= parseFloat(this.uploadFilesList[index].fileSize)
      this.uploadFilesList.splice(index, 1)
    },
    submitComplaint() {
      this.report('complaint_submit_click')
      if (this.disable) {
        if (!this.complaintChecked) {
           this.$toast('Please fill in the complaint type.')
            return
        }
        if (!this.complaintText) {
          this.$toast('Please fill in the complaint details.')
          return
        }
      }
      this.$loading();
      if (this.uploadFilesList.length > 0) {
        const queryArr = []
        this.uploadFilesList.forEach(item => {
          const query = new Promise((resolve, reject) => {            
            this.$http(api.bigDataUrl.uploadFile, {
              data: {
                filePath: item.filePath,
                type: 'complaint',
                uid: this.uid
              },
              addHeader: {
                isUploadFile: true,
                'X-DX-Country': 'NG',
                requestType: 'uploadFile'
              }
            }).then((res) => {
              console.log(res)
              resolve(res.data.id)
            }).catch(() => {
              reject(new Error('Upload failed. Please try again later.'))
            })
          })
          queryArr.push(query)
        })
        Promise.all(queryArr).then(res => {
          console.log(res)
          this.submitForm(res)
        }).catch(res => {
          this.$hideLoading();
          this.$toast(res)
        })
      } else {
        this.submitForm()
      }
    },
    submitForm(ids) {
      this.$http(api.bigDataUrl.complaintUpload, {
        data: {
          product: this.productSource,
          uid: this.uid,
          submitTime: this.forMatDate(new Date().getTime()),
          complaintType: this.complaintChecked,
          complaintDetails: this.complaintText,
          attachment: ids || []
        },
        addHeader: {
          isBigdata: true,
          requestType: 'complaintUpload'
        }
      }).then((res) => {
        console.log(res);
        this.$hideLoading();
        this.complaintChecked = ''
        this.complaintText = ''
        this.uploadFilesList = []
        this.fileCount = 0
        this.fileSize = 0
        this.$toast('Thank you. We will reach out to you within 24 working hours.');
        // 退出页面
        setTimeout(() => {
          this.myBackFun();
        }, 2500)
      }).catch((e) => {
        console.log(e);
        if (e.msg) {
           this.$toast(e.msg);
        }
        this.$hideLoading();
      })
    },
    forMatDate(timestamp) {
      const date = new Date(parseInt(timestamp))
      const Year = date.getFullYear()
      const Moth = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
      const Day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate())
      const Hour = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours())
      const Minute = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes())
      const Sechond = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
      const GMT = Year + '-' + Moth + '-' + Day + ' ' + Hour + ':' + Minute + ':' + Sechond
      return GMT
    },
    chooseReason(index) {
      this.chooseIndex = index;
      this.complaintChecked = this.complaintList[this.chooseIndex];
    },
    beforeRouteEnter (to, from) {
      this.fromName = from.name;
    }
  },
}