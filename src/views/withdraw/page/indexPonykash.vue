<template>
  <div class="wrap">
      <c-header titleName="Withdraw Loan" :backFun="myBackFun"></c-header>
      <CPage className="home">
        <div class="increase-limit-tips" v-if="remindUpLimit > 0">
          <img src="@/assets/images/common/increase-limit-tips.png" />
          <div class="tip-content">Congratulations! You have received ₦<span v-text="common.singleToThousands(remindUpLimit)"></span> credit limit increase.</div>
        </div>
        <div class="loan-amount">
          <div class="title">Loan Amount</div>
          <div class="input-amount">
            <div class="currency">₦</div>
            <input @click="withdrawReport('withdraw_amount_click')" type="tel" @blur="startBlurLoanCalculate()" :placeholder="placeholderTips" @keydown="keyDown($event)" @keyup="keyUp()" v-model="loanDetail.amount">
              <div class="amount-add" v-if="installAmt > 0">
                <div class="wrap">
                  <div class="num" v-text="`+ ₦${common.singleToThousands(installAmt)} only for installment`"></div>
                  <div class="arrow"></div>
                </div>
              </div>
              <img class="edit" :src="require(`@/assets/images/ponykash/edit.png`)" alt="">
          </div>
          <!-- <div class="min-limit-warn" v-if="showMinAlert" v-text="minAlertTips"></div> -->
        </div>
        <div class="range" v-text="rangeTips"></div>

        <div class="new-limit" v-if="chooseTypeData.maxAmount > 2000">
          <div class="base index">
            <img src="@/assets/images/common/index-amount.png" alt="">
            <div class="amount">
              <div class="currency">₦</div>
              <div class="num" v-text="common.singleToThousands(chooseTypeData.maxAmount)"></div>
            </div>
          </div>
          <div class="base second">
            <img src="@/assets/images/common/lock.png" alt="">
            <div class="amount" @click="chooseAmount">
              <div class="currency">₦</div>
              <div class="num" v-text="common.singleToThousands(chooseTypeData.maxAmount * 3)"></div>
            </div>
          </div>
          <div class="base third">
            <img src="@/assets/images/common/lock.png" alt="">
            <div class="amount" @click="chooseAmount">
              <div class="currency">₦</div>
              <div class="num" v-text="common.singleToThousands(chooseTypeData.maxAmount * 10)"></div>
            </div>
          </div>
        </div>

        <loanTerms customType="withdraw" class="test" v-model="longActive" @chooseType="chooseLoanTermType" :productSource="productSource" :productList="loanDetail.productList" :proIndex="proIndex"></loanTerms>

        <div class="choose-others">
          <!-- 选择优惠券以及目的 -->
          <div class="select-list">
            <div class="custom coupon">
              <div class="item-title">Coupons</div>
              <div class="right" @click="chooseCoupons()">
                <template v-if="coupon.couponId">
                  <template v-if="coupon.useScene !== '5'">
                    <div class="tips-content">Interest Relief</div>
                    <CMoney v-if="coupon.couponMethod === 'A'" :currencyNum="common.thousandsToFixed(coupon.denominations)"></CMoney>
                    <CMoney v-if="coupon.couponMethod === 'D'" :currencyNum="common.thousandsToFixed(loanDetail.repayment.interest / loanDetail.productList[longActive].borrowCount * coupon.denominations)"></CMoney>
                    <CMoney v-if="coupon.couponMethod === 'R'" :currencyNum="common.thousandsToFixed(coupon.denominations * loanDetail.repayment.interest)"></CMoney>
                  </template>
                  <template v-else>
                    <div class="use-scene-5">
                      <div class="item" v-if="useScene5Coupon.limitIncrease > 0">
                        <div class="tips-content">Limit increase</div>
                        <CMoney :currencyNum="common.singleToThousands(useScene5Coupon.limitIncrease)"></CMoney>
                      </div>
                      <div class="item" v-if="useScene5Coupon.interestRelief > 0">
                        <div class="tips-content">Interest relief</div>
                        <CMoney :currencyNum="common.thousandsToFixed(useScene5Coupon.interestRelief)"></CMoney>
                      </div>
                    </div>
                  </template>
                </template>
                <div class="no-selected available" :class="{'no-data': availCouponsNum === 0}" v-else v-text="'Available:' + availCouponsNum"></div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
            <div class="custom" @click="choosePurpose()">
              <div class="item-title">Loan Purpose</div>
              <div class="right">
                <div class="no-selected" :class="{'selected': loanDetail.purpose}" v-text="loanDetail.purpose ? loanDetail.purpose : 'Select'"></div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
            <div class="custom" v-if="loanDetail.repayment.showInsurance">
              <div class="acc-pro-ser">
                <img class="icon" src="@/assets/images/protection-service.png" alt="">
                <div class="item-title">Credit Protection Service</div>
                <CPopover :tipContent="'In the event of death or disability, we will waive your loan balance'">
                  <template v-slot:reference>
                    <img class="question" :src="require(`@/assets/images/ponykash/question.png`)" alt="">
                  </template>
                </CPopover>
              </div>
              <div class="right">
                <div class="after check-acc" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value" v-text="common.thousandsToFixed(loanDetail.repayment.insuranceFee)"></span>
                  <img class="check" :src="checkShow ? require(`@/assets/images/ponykash/index-paid.png`) : require('@/assets/images/un-paid.png')"
                  alt="" @click="checkAcc()">
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <div class="change-bank show-bank-acct" v-if="loanDetail.bankAcctList.length > 0">
              <div class="wrap">
                <img class="icon" :src="(loanDetail.bankAcctList[bankIndex] && loanDetail.bankAcctList[bankIndex].bankLogoUrl) || defaultBankImg" />
                <div class="detail">
                  <div class="name">Disbursement Account</div>
                  <div class="num" v-text="disbursementAccount"></div>
                </div>
                <template v-if="!isWalletWithdrawalUser && deviceType !== 'AC'">
                  <div class="change" @click="changeBank()" v-if="identityType !== 'NIN'">Change</div>
                  <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
                </template>
                <template v-else-if="isWalletWithdrawalUser">
                  <div class="faq" @click="openFaq">Where disbursed?</div>
                </template>
              </div>
              <div class="agree" v-if="showAgree">
                <img src="@/assets/images/common/agree-choose.png" alt="">
                <div>Agree <span @click="goAgree">Authorisation for Disclosure of Personal Information</span> and open Goldman Bank account</div>
              </div>
            </div>
            <div class="add-bank change-bank" @click="addBank(2)" v-if="loanDetail.bankAcctList.length == 0 && !isWalletWithdrawalUser">
              <div class="detail">Add a Bank Account</div>
              <div class="right">
                <div>To get a mysterious gift</div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 借款信息 -->
        <div class="repayment-schedule" v-if="longActive > -1">
          <div class="repayment-list">
            <div class="custom" v-if="(loanDetail.repayment.platformFee && loanDetail.repayment.platformFee) > 0">
              <div class="item-title">Platform Fee</div>
              <div class="right">
                <div class="after" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value">{{ common.thousandsToFixed(loanDetail.repayment.platformFee && loanDetail.repayment.platformFee)}}</span>
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <div class="custom">
              <div class="item-title">Received</div>
              <div class="right">
                <div class="after" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value">{{ common.thousandsToFixed(loanDetail.repayment.actualTotalAmount && loanDetail.repayment.actualTotalAmount - (loanDetail.repayment.platformFee && loanDetail.repayment.platformFee))}}</span>
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <!-- 单期显示 -->
            <template  v-if="loanDetail.repaymentDetailList.length == 1">
              <div class="repayment-plan custom">
                <div class="item-title">Due Date</div>
                <div class="right">
                  <div class="after" v-if="caculateStatus">
                    <!-- <span class="currency">₦</span> -->
                    <span class="value">{{ (loanDetail.repayment.deadline && loanDetail.repayment.deadline)}}</span>
                  </div>
                  <div class="after" v-else>
                    <span class="cal">Calculating</span>
                  </div>
                </div>
              </div>
              <div class="repayment-plan custom">
                <div class="item-title">Repayment Amount</div>
                <div class="right">
                  <div class="after" v-if="caculateStatus">
                    <span class="currency">₦</span>
                    <span class="value">{{ common.thousandsToFixed(loanDetail.repaymentDetailList[0].termAmount && loanDetail.repaymentDetailList[0].termAmount)}}</span>
                  </div>
                  <div class="after" v-else>
                    <span class="cal">Calculating</span>
                  </div>
                </div>
              </div>
            </template>
            <!-- 非还款再借产品 -->
            <template v-if="loanDetail.repaymentDetailList.length > 1">
              <div class="repayment-plan custom" @click="showPlan()">
                <div class="item-title">Repayment Plan</div>
                <div class="right">
                  <i class="van-icon van-icon-arrow van-cell__right-icon" v-if="caculateStatus"></i>
                  <div class="after" v-else>
                    <span class="cal">Calculating</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div v-if="longActive != -1" class="contract" @click="viewContract()">View The Contract</div>
        <p class="bottom-text" v-if="bottomText" v-html="bottomText"></p>
        <CButton @buttonClick="goWithdraw(0)" className="withdraw" name="Withdraw"></CButton>
        <div class="bottom-text-below" v-if="isNew">
          <img src="@/assets/images/ic.png" alt="" />
          <div v-text="maxUsableLmtAmt >= 20000 ? 'Bind bank card, interest rate -2%' : 'Withdraw to get up to ₦500,000 credit limit'"></div>
        </div>
        <!-- 还款详情 -->
        <repaymentDetailPopup :repaymentDetailList="loanDetail.repaymentDetailList" @close="closePlan()" :showRepayDetailPop="show" />
        <!-- 验证pin -->
        <Popup closeable close-icon-position="top-left" v-model="showPin" position="bottom" title="test" :style="{ height: '80%' }" class="input-pin">
          <div class="title" v-text="`${channel} PIN`"></div>
          <ul class="pin-list">
            <li v-for="(pin, index) in pinList" :key="index + 'key'">
              <input v-model="pinList[index].value" :ref="'input' + index" @keyup="inputPin($event, index)" type="number" />
              <div class="hide-control" :class="{'control': pin.value !== '' || !pin.showInput}" >
                <div v-if="pin.value !== ''" class="hide"></div>
              </div>
            </li>
          </ul>
          <div v-if="deviceType !== 'AC'" class="forget-pin" @click="forgetPin()">I forgot my pin</div>
        </Popup>
        <!-- 显示借款目的 -->
        <Popup v-model="show1" position="bottom" @close="closePurpose()">
          <ul class="purpose-list">
            <li class="cancel">Please select the loan purpose</li>
            <li class="van-hairline--bottom" v-for="(purpose, index) in loanDetail.purposeList" :key="purpose" @click="selectedPurpose(index)">
              <span :class="{'choosed': index === purposeActive}" v-text="purpose"></span>
              <img v-if="index === purposeActive" :src="pick" alt="">
            </li>
          </ul>
        </Popup>
        <!-- 显示银行账户 -->
        <Popup closeable close-icon-position="top-left" v-model="showCardList" position="bottom" title="test" :style="{ height: '55%' }" class="bankcard-list">
          <div class="title">Select the Disbursement Account to use</div>
          <div class="wrap" @click="addBank(2)">
            <img class="add-bank-icon" :src="addBankIcon" alt="">
          </div>
          <ul class="bank-list">
            <li v-for="(card, index) in loanDetail.bankAcctList" :key="index + 'key'">
              <img class="logo" :src="card.bankLogoUrl || defaultBankAcctImg(card)" alt="">
              <div class="name">
                <div class="bank-name">
                  {{ card.bankName + (card.bankAcctNo ? '(' + card.bankAcctNo.slice(-4, card.bankAcctNo.length) + ')' : '') }}
                  <img v-if="card.bankCode === '090574'" src="@/assets/images/common/bank-recommand.png" alt="">
                </div>
                <div v-if="card.bankCode === '090574'" class="recommand-tips">Balance can enjoy higher deposit interest</div>
              </div>
              <img class="choose" :src="index === bankIndex ? require(`@/assets/images/${productSource}/index-paid.png`) : unchecked" alt="" @click="changeBankCard(index)">
            </li>
          </ul>
        </Popup>
        <Popup v-model="showRelonTips" :close-on-click-overlay='false' class="reloan-tips">
            <div class="control">
                <div class="wrap">
                    <div class="title">Warm Tips</div>
                    <div class="content">Repay and Re-loan: this function allows you to repay and apply for another loan while payment is pending. Loan will be disbursed after the payment is successful.</div>
                    <div class="confirm" @click="tipConfirm()">OK</div>
                </div>
            </div>
        </Popup>
        <van-dialog v-model="showDialog" className="dialog-control" confirmButtonText="To add one" @confirm="confirmAddBankAcc()">
            <div class="tips">Kindly add your bank account to receive loans.</div>
        </van-dialog>
        <div class="back-tips">
            <Popup v-model="showBackDialog" :close-on-click-overlay='false'>
              <img class="alert" @click="getNow" :src="withdrawAlert" />
              <img class="cancel"  @click="cancelBack" :src="cancel" />
            </Popup>
        </div>
        <!-- 提交报错提示 -->
        <van-dialog v-model="showSubmitDialog" show-cancel-button className="dialog-after-withdraw" :showConfirmButton="false" :showCancelButton="false">
            <div class="title">Note!</div>
            <div class="tips" v-text="showMsg"></div>
            <div class="dia-button">
                <div class="ok" @click="hideDialog()">OK</div>
            </div>
        </van-dialog>
        <div class="note-tips">
            <Popup v-model="showTips" :close-on-click-overlay='false'>
                <div class="control">
                    <img :src="tips" />
                    <div class="wrap">
                        <div class="title">Note!</div>
                        <div class="content" v-text="freezeMsg"></div>
                        <div class="confirm" @click="hideShowTips()">OK</div>
                    </div>
                </div>
            </Popup>
        </div>
      </CPage>
      <reasonPopup @close="hideReasonPopup" :showReason="showReasonPopup" :reasonObj="reasonObj[reasonType]" mainButton="Submit" sideButton="Cancel" :productSource="productSource"></reasonPopup>
      <CDialog title="Note!" :content="popTips" @mainAction="hideProDialog" :show="showNoProDialog" mainButton="OK"></CDialog>
      <giftPopup :show="showGiftPopup" :step="stepGiftPopup" :usableLmtAmt="maxUsableLmtAmt" :purpose="loanDetail.purpose" @hidePopup="hideGiftPopup" @choose="startPurpose"></giftPopup>
      <faqPopup ref="faqPopupDom"></faqPopup>
      <Question @question="startQuestion"></Question>
      <NinCheck @close="closeNinCheck" ref="ninCheck"></NinCheck>
  </div>
</template>

<script>
import publicMixns from '../indexMixins.js'
export default {
    name: 'ponykashWithdraw',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>
<style lang="scss" scoped>
  .wrap{
      height: 100%;
      .home{
      padding-bottom: 120px;
      background: #ffffff;
      padding-left: 0px;
      padding-right: 0px;

      .increase-limit-tips{
      margin: 12px 12px -17px 12px;
      background: rgba(255, 229, 0, 0.10);
      height: 44px;
      color: #FF8A00;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      text-align: left;
      padding-left: 13px;
      position: relative;
      img{
        width: 24px;
        height: 25px;
        margin-right: 2px;
        margin-top: 10px;
      }
      .tip-content{
        transform: scale(.85);
        display: inline-block;
        position: absolute;
        left: 19px;
        top: 8px;
        width: 336px;
        span{
          font-weight: 900;
        }
      }
    }
      .loan-amount {
        text-align: left;
        padding: 0 12px;
        background: #ffffff;
        padding-bottom: 10px;
        border: 1px solid #E3E5E9;
        margin: 12px 12px 0 12px;
        border-radius: 8px;
        height: 70px;
        transform: perspective(10px);
        .title{
          font-size: 12px;
          font-weight: 500;
          text-align: left;
          color: #919DB3;
          line-height: 19px;
          font-family: Avenir, Avenir-Medium;
          margin-top: 3px;
        }
        .input-amount{
          height: 45px;
          display: flex;
          align-items: flex-end;
          position: relative;
          .currency{
            font-weight: 700;
            color: #1B3155;
            font-size: 32px;
          }
          input{
            border: none;
            height: 40px;
            color: #1B3155;
            font-size: 32px;
            font-family: DINAlternate, DINAlternate-Bold;
            line-height: 45px;
            font-weight: 700;
            margin-left: 5px;
            width: calc(100% - 19px);
            // background: #fcfcfd;
            outline: none;
            position: relative;
            &::placeholder{
              font-size: 12px;
              font-weight: 600;
              color: rgb(177, 176, 176);
              position: absolute;
              top: 22px;
            }
          }
          .amount-add{
            position: absolute;
            left: 154px;
            top: -2px;
            transform: scale(.85);
            .wrap{
              position: relative;
              .num{
                background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
                color: #ffffff;
                width: auto;
                white-space: nowrap;
                padding: 2px 4px;
                font-size: 12px;
              }
              .arrow{
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 5px 0 5px 7px;
                border-color: transparent transparent transparent #C78900;
                position: absolute;
                left: 0px;
                top: 12px;
              }
            }
          }
          .edit{
            width: 24px;
            height: 24px;
            position: absolute;
            right: -4px;
            top: 2px;
          }
        }

        .min-limit-warn{
          margin-top: 5px;
          font-size: 12px;
          color: red;
        }
        .limit{
          margin-top: 10px;
          .tip{
            width: 104px;
            height: 18px;
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #919db3;
            line-height: 18px;
          }
          .currency{
            width: 10px;
            height: 12px;
            font-size: 12px;
            font-family: Montserrat, Montserrat-Bold;
            font-weight: 700;
            color: #1b3155;
            line-height: 12px;
            margin-left: 5px;
            margin-bottom: 2px;
          }
          .num {
            width: 37px;
            height: 16px;
            font-size: 14px;
            font-family: DINAlternate, DINAlternate-Bold;
            font-weight: 700;
            color: #099bfa;
            line-height: 16px;
            margin-left: 5px;
          }
        }
      }
      .range{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #F0AE2C;
        letter-spacing: 0;
        font-weight: 500;
        transform: scale(.84);
        margin-left: -7px;
        margin-top: 3px;
        text-align: left;
      }
      .new-limit{
        display: flex;
        align-items: center;
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 8px;
        .base{
          width: 105px;
          height: 48px;
          background: $themeColor;
          color: #FFF;
          text-align: center;
          font-family: DINPro;
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
          img{
            width: 19px;
            height: 19px;
            margin-top: 5px;
          }
          .amount{
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -3px;
            .currency{
              margin-right: 2px;
            }
          }
          &.index{
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
          }
          &.second{
            margin: 0 7.5px;
          }
          &.second, &.third{
            background: #E7E7E7;
            color: #999;
            img{
              width: 16px;
              height: 16px;
              margin-top: 8px;
            }
            .amount{
              margin-top: -3px;
            }
          }
          &.third{
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
          }
        }
      }
      .loan-terms{
        margin-top: 18px;
        margin-left: 12px;
        margin-right: 12px;
        ::v-deep .select-loan-terms {
          height: 80px;
          .left{
            border: 1px solid #F0AE2C;
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
            padding-left: 12px;
            .title{
              color: #F0AE2C;
              font-size: 12px;
              font-family: Avenir;
            }
            .days{
              color: #1B3155;
              font-size: 16px;
              font-weight: 900;
              font-family: DINPro;
            }
            .detail{
              font-family: Avenir;
              font-size: 12px;
              margin-left: -24px;
            }
          }
        }
      }
      .choose-others{
        background: #ffffff;
        border-radius: 8px;
        text-align: left;
        padding: 4px 0 0 12px;
        margin-top: 14px;
        margin-left: 12px;
        margin-right: 12px;
        border: 1px solid #E3E5E9;
        .title{
          width: 125px;
          height: 19px;
          font-size: 14px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #536887;
          line-height: 19px;
        }
        .staging{
          margin-top: 7px;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          li{
            display: flex;
            margin-top: 10px;
            background: #f3f5f6;
            align-items: center;
            justify-content: center;
            margin-right: 6px;
            color: #536887;
            min-width: 60px;
            height: 32px;
            border: 1px solid #c4cad5;
            border-radius: 23px;
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            color: #c4cad5;
            padding-left: 5px;
            padding-right: 5px;
            &.active {
              background: #F0AE2C;
              color: #ffffff;
              border: 1px solid #F0AE2C;
            }
            .time{
              text-align: left;
            }
            .day{
              text-align: left;
              margin-left: 2px;
            }
          }
        }
        .select-list{
          .custom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 42px;
            position: relative;
            .acc-pro-ser{
              display: flex;
              align-items: center;
              .icon{
                width: 14px;
                height: 14px;
                margin-top: -2px;
                margin-right: 2px;
              }
              .question{
                width: 14px;
                height: 14px;
              }
              ::v-deep .c-popover{
                margin-left: 2px;
                .popover-tips{
                  right: 46px;
                  top: -45px;
                }
                .wrap{
                  width: 14px;
                  height: 14px;
                  display: flex;
                  align-items: center;
                }
              }
            }
            .item-title{
              font-size: 12px;
              font-family: Avenir;
              font-weight: 500;
              color: #536887;
            }
            .right{
              display: flex;
              align-items: center;
              margin-right: 10px;
              height: 40px;
              .tips-content{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                text-align: left;
                color: #c4cad5;
                margin-right: 3px;
                color: $themeColor;
                height: 20px;
                line-height: 20px;
              }
              ::v-deep .c-money{
                text-align: right;
                display: flex;
                align-items: center;
                .monetary-unit{
                  font-size: 12px;
                  color: $themeColor;
                  transform: scale(1) !important;
                }
                .currency-num{
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  color: $themeColor;
                  margin-left: 0px;
                }
              }
              .no-selected{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                color: #c4cad5;
              }
              .available{
                color: #F0AE2C;
              }
              .selected{
                color: #1b3155;
              }
              .no-data{
                color: #c4cad5;
              }
              .amount-fee{
                font-family: Avenir-Medium;
                font-size: 12px;
                color: #02B17B;
                text-align: right;
              }
              .after{
                display: flex;
                align-items: center;
                &.check-acc{
                  display: flex;
                  align-items: center;
                  img{
                    width: 18px;
                    height: 18px;
                    margin-left: 5px;
                  }
                }
                .currency{
                  font-size: 12px;
                  font-family: Montserrat, Montserrat-Bold;
                  text-align: right;
                  color: #1b3155;
                  line-height: 12px;
                }
                .value{
                  height: 23px;
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  text-align: right;
                  color: #1b3155;
                  line-height: 23px;
                }

                .cal{
                  font-size: 12px;
                  color: #c4cad5;
                  font-weight: 500;
                }
              }
              .van-icon-arrow:before{
                color: #D8D8D8;
              }
            }
            .use-scene-5{
              height: 40px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              .item{
                display: flex;
                justify-content: center;
                color: $themeColor;
                justify-content: flex-end;
                height: 20px;
                ::v-deep .c-money{
                  .monetary-unit{
                    transform: scale(1) !important;
                  }
                }
              }
            }
          }
          .change-bank{
            height: 40px;
            font-weight: 600;
            border-radius: 12px;
            background: #ffffff;
            display: flex;
            align-items: center;
            padding: 10px 0px;
            margin-right: 10px;
            justify-content: space-between;
            &.add-bank {
              justify-content: space-between;
              .detail {
                color: #F0AE2C;
                font-size: 12px;
                width: 120px;
              }
              .right{
                margin-bottom: 2px;
                display: flex;
                align-items: center;
                font-family: DINAlternate-Bold;
                font-size: 12px;
                color: #FF8A51;
                text-align: left;
                font-weight: 700;
              }
            }
            .van-icon-arrow:before{
              color: #D8D8D8;
            }
            .change{
              color: #C4CAD5;
              font-size: 12px;
              height: 40px;
              display: flex;
              align-items: center;
              font-weight: normal;
            }
            .faq{
              flex: none;
              color: #F0AE2C;
              font-size: 12px;
              height: 40px;
              display: flex;
              align-items: center;
              text-decoration: underline;
            }
            img {
              flex: none;
              width: 40px;
              height: 40px;
              margin-right: 5px;
            }
            .detail{
              width: 196px;
              margin-bottom: 10px;
              .name{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                text-align: left;
                color: #1b3155;
                line-height: 19px;
                width: 100%;
              }
              .num{
                font-size: 14px;
                // font-family: Avenir, Avenir-Medium;
                font-weight: 700;
                text-align: left;
                color: #1b3155;
                line-height: 16px;
                width: 100%;
              }
            }
            &.show-bank-acct{
              display: block;
              height: auto;
              .wrap{
                height: 40px;
                font-weight: 600;
                border-radius: 12px;
                display: flex;
                align-items: center;
                padding: 10px 0px;
                margin-right: 10px;
                justify-content: space-between;
              }
              .agree{
                display: flex;
                align-items: flex-start;
                padding: 4px 8px;
                background: #F2F3F8;
                img{
                  width: 15px;
                  height: 15px;
                }
                div{
                  color: #989898;
                  font-family: Avenir;
                  font-size: 11px;
                  font-style: normal;
                  font-weight: 600;
                  span{
                    color: $themeColor;
                    text-decoration-line: underline;
                  }
                }
              }
            }
          }
          .add-bank{
            .detail{
              margin-bottom: 0;
            }
          }
        }
      }
      .repayment-schedule{
        background: #ffffff;
        text-align: left;
        padding: 8px 0 0 0px;
        margin-left: 25px;
        margin-right: 25px;
        .title{
          height: 20px;
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #919db3;
          line-height: 20px;
        }
        .custom{
            display: flex;
            justify-content: space-between;
            height: 32px;
            align-items: center;
            position: relative;
            &.interest{
              .right{
                align-items: flex-end;
                flex-direction: column-reverse;
              }
            }
            &.repayment-plan{
              .right{
                display: flex;
                align-items: center;
                width: 40px;
                height: 40px;
                text-align: right;
                justify-content: flex-end;
              }
            }
            .item-title{
              height: 19px;
              font-size: 12px;
              font-family: Avenir;
              font-weight: 500;
              text-align: left;
              color: #536887;
              line-height: 19px;
            }
            .right{
              text-align: right;
              display: flex;
              align-items: center;
              .after{
                display: flex;
                align-items: center;
                &.check-acc{
                  display: flex;
                  align-items: center;
                  img{
                    width: 18px;
                    height: 18px;
                    margin-left: 5px;
                  }
                }
                .currency{
                  font-size: 12px;
                  font-family: Montserrat, Montserrat-Bold;
                  text-align: right;
                  color: #536887;
                  line-height: 12px;
                }
                .value{
                  height: 23px;
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  text-align: right;
                  color: #536887;
                  line-height: 23px;
                  .interest{
                    margin-left: 2px;
                  }
                }

                .cal{
                  font-size: 14px;
                  color: #c4cad5;
                  font-weight: 500;
                }
              }
              .before{
                &.reduce{
                  margin-top: 6px;
                  text-decoration: line-through;
                  margin-right: 5px;
                  .currency{
                    color: #919db3;
                    font-size: 14px;
                  }
                  .value{
                    margin-left: 0px;
                  }
                }
                .currency{
                  height: 12px;
                  font-size: 12px;
                  font-family: Montserrat, Montserrat-Bold;
                  text-align: right;
                  line-height: 12px;
                  color: #929eb4;
                }
                .value{
                  height: 14px;
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  text-align: right;
                  color: #919db3;
                  line-height: 14px;
                  margin-left: 4px;
                }
              }
            }
        }
      }
      .contract{
        height: 19px;
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #F0AE2C;
        line-height: 19px;
        margin-top: 18px;
        margin-left: 25px;
        margin-right: 12px;
      }
      .withdraw{
        position: fixed;
        bottom: 55px;
        left: calc((100% - 328px)/2);
        background: #F0AE2C;
      }
      .bottom-text {
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #989898;
        font-weight: 500;
        text-align: left;
        margin-top: 15px;
        line-height: 20px;
        margin-left: 25px;
        margin-right: 25px;
        span {
          color: #1B3155;
          font-weight: 500;
        }
      }
      .bottom-text-below{
        position: fixed;
        bottom: 0px;
        left: 0px;
        font-family: Avenir-Medium;
        text-align: left;
        line-height: 20px;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #FF7400;
        font-weight: 800;
        width: 100%;
        justify-content: center;
        height: 55px;
        background-image: linear-gradient(180deg, rgba(242,243,248,0.00) 0%, rgba(242,243,248,0.80) 43%, #F2F3F8 100%);
        img{
          width: 24px;
          height: 24px;
          margin-right: 4px;
        }
      }
      .van-popup{
        background: rgba(120,120,120,0);
        .purpose-list{
          margin: 0;
          padding: 0;
          list-style: none;
          height: 428px;
          background: #ffffff;
          overflow-y: scroll;
          overflow-x: hidden;
          li {
              height: 50px;
              text-align: left;
              line-height: 50px;
              padding: 0 15px;
              font-size: 14px;
              color: #000000;
              display: flex;
              justify-content: space-between;
              align-items: center;

              &.cancel{
                background: #ebebeb;
              }
              &.van-hairline--bottom::after{
                left: -44%;
              }
              img{
                width: 20px;
                height : 20px;
              }
              .choosed{
                color: #129ffa;
              }
          }
        }
        .product-list{
          padding: 25px 0 0 0px;
          .title{
            padding-left: 30px;
          }
          .list{
            margin-top: 15px;
            overflow-y: scroll;
            height: 440px;
            .product{
              display: flex;
              justify-content: space-between;
              padding-right: 19px;
              align-items: center;
              padding-left: 30px;
              padding-top: 10px;
              padding-bottom: 10px;
              &.active{
                background: #FAFAFA;
              }
              .left{
                text-align: left;
                .item {
                  margin-bottom: 3px;
                  position: relative;
                  .day{
                    font-family: DINAlternate-Bold;
                    font-size: 24px;
                    color: #1B3155;
                    letter-spacing: 0;
                  }
                  .amount-add{
                    position: absolute;
                    left: 96px;
                    top: -8px;
                    transform: scale(.85);
                    .wrap{
                      position: relative;
                      .num{
                        background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
                        color: #ffffff;
                        width: auto;
                        white-space: nowrap;
                        padding: 2px 4px;
                        font-size: 12px;
                      }
                      .arrow{
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width: 5px 0 5px 7px;
                        border-color: transparent transparent transparent #C78900;
                        position: absolute;
                        left: 0px;
                        top: 12px;
                      }
                    }
                  }
                }
                .detail{
                  font-family: Avenir-Medium;
                  font-size: 12px;
                  color: #919DB3;
                  letter-spacing: 0;
                  margin-bottom: 2px;
                }
                .rate{
                  font-family: Avenir-Medium;
                  font-size: 12px;
                  color: #919DB3;
                  letter-spacing: 0;
                }
              }
              img{
                width: 20px;
                height: 20px;
              }
            }
          }
        }
      }
      .input-pin{
        &.van-popup{
          background: #ffffff;
          .van-popup__close-icon--top-left{
            top: 12px;
          }
        }
        .title{
          height: 45px;
          line-height: 45px;
          font-size: 16px;
          font-family: Avenir, Avenir-Heavy;
          font-weight: 800;
          color: #1b3155;
          border-bottom: 1px solid #b5abab;
        }

        .pin-list{
          display: flex;
          margin-top: 23px;
          padding: 0 45px;
          li {
            width: 50px;
            height: 50px;
            margin-right: 21px;
            position: relative;
            .hide-control{
              width: 50px;
              height: 50px;
              border-bottom: 1px solid #bfc4ca;
              display: flex;
              justify-content: center;
              align-items: center;
              position: absolute;
              top: 0;
              background: #ffffff;
              z-index: -1;
              &.control {
                z-index: 2;
              }
              .hide{
                width: 12px;
                height: 12px;
                background: #1b3155;
                border-radius: 12px;
              }
            }
            input{
              width: 50px;
              height: 50px;
              border: none;
              border-bottom: 1px solid #bfc4ca;
              font-size: 25px;
              text-align: center;
            }
          }
        }
        .forget-pin{
          font-size: 16px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          color: #F0AE2C;
          margin-top: 25px;
          text-align: center;
        }
      }
      .bankcard-list{
        &.van-popup{
          background: #ffffff;
          .van-popup__close-icon--top-left{
            top: 12px;
            color: #3e6fb3;
          }
        }
        .title{
          margin-top: 45px;
          font-size: 24px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #1b3155;
          margin-left: 18px;
        }
        .wrap{
          position: absolute;
          z-index: 1;
          top: 3px;
          right: 4px;
          width: 50px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          .add-bank-icon{
            width: 25px;
            height: 25px;
          }
        }
        .bank-list{
          margin-top: 26px;
          li {
            display: flex;
            align-items: center;
            padding: 0 19px;
            margin-bottom: 20px;
            .logo{
              width: 32px;
              height: 32px;
            }
            .name{
              font-size: 14px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              text-align: left;
              color: #1b3155;
              margin-left: 17px;
              margin-right: 17px;
              width: 254px;
              .bank-name{
                color: #1B3155;
                font-family: Avenir;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 14px;
                text-align: left;
                img{
                  width: 16px;
                  height: 16px;
                }
              }
              .recommand-tips{
                color: #1B3155;
                font-family: Avenir;
                font-size: 10px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                text-align: left;
              }
            }
            .choose{
              width: 18px;
              height: 18px;
            }
          }
        }
      }
      .reloan-tips{
        &.van-popup{
            background-color: inherit;
            width: 304px;
            border-radius: 16px;
            box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);

            .control{
                position: relative;
                height: 275px;

                img {
                    width: 175px;
                    height: 94px;
                    position: absolute;
                    top: -25px;
                    right: 0;
                }
                .wrap{
                    background-color: #ffffff;
                    height: 100%;
                    .title{
                        font-size: 24px;
                        font-family: Avenir, Avenir-Heavy;
                        font-weight: 800;
                        text-align: left;
                        color: #1b3155;
                        line-height: 33px;
                        padding-top: 22px;
                        padding-left: 24px;
                        padding-bottom: 12px;
                    }
                    .content{
                        font-size: 16px;
                        font-family: Avenir, Avenir-Medium;
                        font-weight: 500;
                        text-align: left;
                        color: #536887;
                        line-height: 23px;
                        padding-left: 24px;
                    }
                    .confirm{
                        width: 272px;
                        height: 42px;
                        background: #F0AE2C;
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 18px;
                        font-family: Avenir, Avenir-Medium;
                        font-weight: 500;
                        text-align: center;
                        color: #ffffff;
                        line-height: 25px;
                        margin: 0 auto;
                        margin-top: 23px;
                    }
                }
            }
        }
      }
    }
  }
  .dialog-control{
    color: #536887;
    padding-bottom: 10px;
    width: 300px;
    ::v-deep .van-dialog__content{
      min-height: 70px;
      .tips {
        padding: 24px 13px;
        text-align: left;
      }
    }
    ::v-deep .van-button__content{
      span {
        background: #F0AE2C;
        display: inline-block;
        width: 90%;
        color: #ffffff;
        height: 36px;
        border-radius: 8px;
        line-height: 36px;
      }
    }
    .van-dialog__message--left {
        padding: 15px 24px;
    }
  }

  .dialog-after-withdraw{
      color: #536887;
      padding: 12px 15px;
      width: 254px;
      .van-dialog__content{
        .title{
            height: 40px;
            line-height: 40px;
            text-align: left;
            margin-bottom: 5px;
            color: #536887;
            font-weight: 700;
        }
        .tips{
            line-height: 20px;
            text-align: left;
        }
        .dia-button{
            margin-top: 15px;
            .ok{
                height: 40px;
                line-height: 40px;
                background: #F0AE2C;
                color: #ffffff;
                border-radius: 5px;
            }
            .cancel{
                height: 40px;
                line-height: 40px;
                background: #ffffff;
                color:  #F0AE2C;
                border-radius: 5px;
            }
        }
      }
      .van-dialog__content--isolated{
        min-height: 70px;
      }
      .van-dialog__message--left {
          padding: 15px 24px;
      }
    }
    .back-tips{
      .van-popup{
        .alert{
          width: 360px;
          height: 360px;
        }
        .cancel{
          width: 50px;
          height: 50px;
        }
      }
    }
    .note-tips{
      .van-popup{
          background-color: inherit;
          width: 304px;
          // box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);

          .control{
            position: relative;
            height: 225px;
            padding-top: 28px;

              img {
                  width: 175px;
                  height: 94px;
                  position: absolute;
                  top: 2px;
                  right: 0;
              }
              .wrap{
                  border-radius: 16px;
                  background-color: #ffffff;
                  height: 100%;
                  .title{
                      font-size: 24px;
                      font-family: Avenir, Avenir-Heavy;
                      font-weight: 800;
                      text-align: left;
                      color: #1b3155;
                      line-height: 33px;
                      padding-top: 36px;
                      padding-left: 24px;
                  }
                  .content{
                      font-size: 16px;
                      font-family: Avenir, Avenir-Medium;
                      font-weight: 500;
                      text-align: left;
                      color: #536887;
                      line-height: 23px;
                      padding-left: 24px;
                  }
                  .confirm{
                      width: 272px;
                      height: 42px;
                      background: #F0AE2C;
                      border-radius: 8px;
                      box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 18px;
                      font-family: Avenir, Avenir-Medium;
                      font-weight: 500;
                      text-align: center;
                      color: #ffffff;
                      line-height: 25px;
                      margin: 0 auto;
                      margin-top: 23px;
                  }
              }
          }
      }
    }
  .van-popup {
    ::v-deep .van-popover__action{
      height: auto;
    }
  }
</style>

