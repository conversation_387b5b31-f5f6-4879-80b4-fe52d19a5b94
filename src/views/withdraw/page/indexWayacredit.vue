<template>
  <div class="wrap">
    <c-header titleName="Withdraw Loan" :backFun="myBackFun" :styType="'b'"></c-header>
    <CPage className="home">
      <div class="increase-limit-tips" v-if="remindUpLimit > 0">
        <img src="@/assets/images/common/increase-limit-tips.png" />
        <div class="tip-content">Congratulations! You have received ₦<span v-text="common.singleToThousands(remindUpLimit)"></span> credit limit increase.</div>
      </div>
      <div class="loan-amount">
        <div class="title">Loan Amount</div>
        <div class="input-amount">
          <div class="currency">₦</div>
          <input
            @click="withdrawReport('withdraw_amount_click')"
            type="tel"
            @blur="startBlurLoanCalculate()"
            :placeholder="placeholderTips"
            @keydown="keyDown($event)"
            @keyup="keyUp()"
            v-model="loanDetail.amount"
          />
          <!-- <div class="amount-add" v-if="installAmt > 0">
            <div class="wrap">
              <div
                class="num"
                v-text="
                  `+ ₦${common.singleToThousands(
                    installAmt
                  )} only for installment`
                "
              ></div>
              <div class="arrow"></div>
            </div>
          </div> -->
          <img
            class="edit"
            :src="require(`@/assets/images/${productSource}/edit.png`)"
            alt=""
          />
        </div>
      </div>
      <div class="range" v-text="rangeTips"></div>

      <loanTermsWayacredit
        customType="withdraw"
        class="test"
        v-model="longActive"
        @chooseType="chooseLoanTermType"
        :productSource="productSource"
        :productList="loanDetail.productList"
        :proIndex="proIndex"
      ></loanTermsWayacredit>

      <div class="choose-others">
        <!-- 选择优惠券以及目的 -->
        <div class="select-list">
          <div class="custom coupon">
            <div class="item-title">Coupons</div>
            <div class="right" @click="chooseCoupons()">
              <template v-if="coupon.couponId">
                <template v-if="coupon.useScene !== '5'">
                  <div class="tips-content">Interest Relief</div>
                  <CMoney v-if="coupon.couponMethod === 'A'" :currencyNum="common.thousandsToFixed(coupon.denominations)"></CMoney>
                  <CMoney v-if="coupon.couponMethod === 'D'" :currencyNum="common.thousandsToFixed(loanDetail.repayment.interest / loanDetail.productList[longActive].borrowCount * coupon.denominations)"></CMoney>
                  <CMoney v-if="coupon.couponMethod === 'R'" :currencyNum="common.thousandsToFixed(coupon.denominations * loanDetail.repayment.interest)"></CMoney>
                </template>
                <template v-else>
                  <div class="use-scene-5">
                    <div class="item" v-if="useScene5Coupon.limitIncrease > 0">
                      <div class="tips-content">Limit increase</div>
                      <CMoney :currencyNum="common.singleToThousands(useScene5Coupon.limitIncrease)"></CMoney>
                    </div>
                    <div class="item" v-if="useScene5Coupon.interestRelief > 0">
                      <div class="tips-content">Interest relief</div>
                      <CMoney :currencyNum="common.thousandsToFixed(useScene5Coupon.interestRelief)"></CMoney>
                    </div>
                  </div>
                </template>
              </template>
              <div
                class="no-selected available"
                :class="{ 'no-data': availCouponsNum === 0 }"
                v-else
                v-text="'Available:' + availCouponsNum"
              ></div>
              <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
            </div>
          </div>
          <div class="custom" @click="choosePurpose()">
            <div class="item-title">Loan Purpose</div>
            <div class="right">
              <div
                class="no-selected"
                :class="{ selected: loanDetail.purpose }"
                v-text="loanDetail.purpose ? loanDetail.purpose : 'Select'"
              ></div>
              <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
            </div>
          </div>
          <div class="custom" v-if="loanDetail.repayment.showInsurance">
            <div class="acc-pro-ser">
              <img
                class="icon"
                src="@/assets/images/wayacredit/protect.png"
                alt=""
              />
              <div class="item-title">Credit Protection Service</div>
              <CPopover
                :tipContent="'In the event of death or disability, we will waive your loan balance'"
              >
                <template v-slot:reference>
                  <img
                    class="question"
                    :src="
                      require(`@/assets/images/${productSource}/question.png`)
                    "
                    alt=""
                  />
                </template>
              </CPopover>
            </div>
            <div class="right">
              <div class="after check-acc" v-if="caculateStatus">
                <span class="currency">₦</span>
                <span
                  class="value"
                  v-text="
                    common.thousandsToFixed(loanDetail.repayment.insuranceFee)
                  "
                ></span>
                <img
                  class="check"
                  :src="
                    checkShow
                      ? require(`@/assets/images/wayacredit/ic_circle_active.png`)
                      : require('@/assets/images/wayacredit/ic_circle_inactive.png')
                  "
                  alt=""
                  @click="checkAcc()"
                />
              </div>
              <div class="after" v-else>
                <span class="cal">Calculating</span>
              </div>
            </div>
          </div>
          <div class="change-bank show-bank-acct" v-if="loanDetail.bankAcctList.length > 0">
            <div class="wrap">
              <img
                class="icon"
                :src="
                  (loanDetail.bankAcctList[bankIndex] &&
                    loanDetail.bankAcctList[bankIndex].bankLogoUrl) ||
                  defaultBankImg
                "
              />
              <div class="detail">
                <div class="name">Disbursement Account</div>
                <div class="num" v-text="disbursementAccount"></div>
              </div>
              <template v-if="!isWalletWithdrawalUser && deviceType !== 'AC'">
                <div class="change" @click="changeBank()" v-if="identityType !== 'NIN'">Change</div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </template>
              <template v-else-if="isWalletWithdrawalUser">
                <div class="faq" @click="openFaq">Where disbursed?</div>
              </template>
            </div>
            <div class="agree" v-if="goldmanDebitScene">
              <img src="@/assets/images/common/agree-choose.png" alt="">
              <div>Agree <span @click="goAgree">Authorisation for Disclosure of Personal Information</span> and open Goldman Bank account</div>
            </div>
          </div>
          <div
            class="add-bank change-bank"
            @click="addBank(2)"
            v-if="loanDetail.bankAcctList.length == 0 && !isWalletWithdrawalUser"
          >
            <div class="detail">Add a Bank Account</div>
            <div class="right">
              <div>To get a mysterious gift</div>
              <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 借款信息 -->
      <div class="repayment-schedule" v-if="longActive > -1">
        <div class="repayment-list">
          <div
            class="custom"
            v-if="
              (loanDetail.repayment.platformFee &&
                loanDetail.repayment.platformFee) > 0
            "
          >
            <div class="item-title">Platform Fee</div>
            <div class="right">
              <div class="after" v-if="caculateStatus">
                <span class="currency">₦</span>
                <span class="value">{{
                  common.thousandsToFixed(
                    loanDetail.repayment.platformFee &&
                      loanDetail.repayment.platformFee
                  )
                }}</span>
              </div>
              <div class="after" v-else>
                <span class="cal">Calculating</span>
              </div>
            </div>
          </div>
          <div class="custom">
            <div class="item-title">Received</div>
            <div class="right">
              <div class="after" v-if="caculateStatus">
                <span class="currency">₦</span>
                <span class="value">{{
                  common.thousandsToFixed(
                    loanDetail.repayment.actualTotalAmount &&
                      loanDetail.repayment.actualTotalAmount -
                        (loanDetail.repayment.platformFee &&
                          loanDetail.repayment.platformFee)
                  )
                }}</span>
              </div>
              <div class="after" v-else>
                <span class="cal">Calculating</span>
              </div>
            </div>
          </div>
          <!-- 单期显示 -->
          <template v-if="loanDetail.repaymentDetailList.length == 1">
            <div class="repayment-plan custom">
              <div class="item-title">Due Date</div>
              <div class="right">
                <div class="after" v-if="caculateStatus">
                  <!-- <span class="currency">₦</span> -->
                  <span class="value">{{
                    loanDetail.repayment.deadline &&
                    loanDetail.repayment.deadline
                  }}</span>
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <div class="repayment-plan custom">
              <div class="item-title">Repayment Amount</div>
              <div class="right">
                <div class="after" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value">{{
                    common.thousandsToFixed(
                      loanDetail.repaymentDetailList[0].termAmount &&
                          loanDetail.repaymentDetailList[0].termAmount
                    )
                  }}</span>
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
          </template>
          <!-- 非还款再借产品 -->
          <template v-if="loanDetail.repaymentDetailList.length > 1">
            <div class="repayment-plan custom" @click="showPlan()">
              <div class="item-title">Repayment Plan</div>
              <div class="right">
                <span class="check" v-if="caculateStatus">Check</span>
                <i
                  class="van-icon van-icon-arrow van-cell__right-icon"
                  v-if="caculateStatus"
                ></i>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div v-if="longActive != -1" class="contract" @click="viewContract()">
        View The Contract
      </div>
      <p class="bottom-text" v-if="bottomText" v-html="bottomText"></p>

      <CButton
        @buttonClick="goWithdraw(0)"
        className="withdraw"
        name="Withdraw"
      ></CButton>
      <div class="bottom-text-below" v-if="isNew">
        <img src="@/assets/images/ic.png" alt="" />
        <div
          v-text="
            maxUsableLmtAmt >= 20000
              ? 'Bind bank card, interest rate -2%'
              : 'Withdraw to get up to ₦500,000 credit limit'
          "
        ></div>
      </div>
      <!-- 还款计划弹框 -->
      <repaymentPlanWayacredit
        :repaymentDetailList="loanDetail.repaymentDetailList"
        @close="closePlan()"
        :showRepayDetailPop="show"
      />
      <!-- 验证pin -->
      <Popup
        closeable
        close-icon-position="top-left"
        v-model="showPin"
        position="bottom"
        title="test"
        :style="{ height: '80%' }"
        class="input-pin"
      >
        <div class="title" v-text="`${channel} PIN`"></div>
        <ul class="pin-list">
          <li v-for="(pin, index) in pinList" :key="index + 'key'">
            <input
              v-model="pinList[index].value"
              :ref="'input' + index"
              @keyup="inputPin($event, index)"
              type="tel"
            />
            <div
              class="hide-control"
              :class="{ control: pin.value !== '' || !pin.showInput }"
            >
              <div v-if="pin.value !== ''" class="hide"></div>
            </div>
          </li>
        </ul>
        <div v-if="deviceType !== 'AC'" class="forget-pin" @click="forgetPin()">
          I forgot my pin
        </div>
      </Popup>
      <!-- 显示借款目的 -->
      <Popup v-model="show1" position="bottom" @close="closePurpose()">
        <ul class="purpose-list">
          <li class="title">Please select the loan purpose</li>
          <li
            class="van-hairline--bottom"
            v-for="(purpose, index) in loanDetail.purposeList"
            :key="purpose"
            @click="selectedPurpose(index)"
          >
            <span
              :class="{ choosed: index === purposeActive }"
              v-text="purpose"
            ></span>
            <img v-if="index === purposeActive" src="@/assets/images/wayacredit/ic_circle_active.png" alt="" />
          </li>
        </ul>
      </Popup>
      <!-- 显示银行账户 -->
      <Popup
        closeable
        close-icon-position="top-left"
        v-model="showCardList"
        position="bottom"
        title="test"
        :style="{ height: '55%' }"
        class="bankcard-list"
      >
        <div class="title">Select the Disbursement Account to use</div>
        <div class="wrap" @click="addBank(2)">
          <img class="add-bank-icon" src="@/assets/images/wayacredit/add-bank.png" alt="" />
        </div>
        <ul class="bank-list">
          <li v-for="(card, index) in loanDetail.bankAcctList" :key="index + 'key'">
            <img class="logo" :src="card.bankLogoUrl || defaultBankAcctImg(card)" alt="">
            <div class="name">
              <div class="bank-name">
                {{ card.bankName + (card.bankAcctNo ? '(' + card.bankAcctNo.slice(-4, card.bankAcctNo.length) + ')' : '') }}
                <img v-if="card.bankCode === '090574'" src="@/assets/images/common/bank-recommand.png" alt="">
              </div>
              <div v-if="card.bankCode === '090574'" class="recommand-tips">Balance can enjoy higher deposit interest</div>
            </div>
            <img class="choose" :src="index === bankIndex ? require(`@/assets/images/${productSource}/index-paid.png`) : unchecked" alt="" @click="changeBankCard(index)">
          </li>
        </ul>
      </Popup>
      <Popup
        v-model="showRelonTips"
        :close-on-click-overlay="false"
        class="reloan-tips"
      >
        <div class="control">
          <div class="wrap">
            <div class="title">Warm Tips</div>
            <div class="content">
              Repay and Re-loan: this function allows you to repay and apply for
              another loan while payment is pending. Loan will be disbursed
              after the payment is successful.
            </div>
            <div class="confirm" @click="tipConfirm()">OK</div>
          </div>
        </div>
      </Popup>
      <van-dialog
        v-model="showDialog"
        className="dialog-control"
        confirmButtonText="To add one"
        @confirm="confirmAddBankAcc()"
      >
        <div class="tips">Kindly add your bank account to receive loans.</div>
      </van-dialog>
      <div class="back-tips">
        <Popup v-model="showBackDialog" :close-on-click-overlay="false">
          <img class="alert" @click="getNow" :src="withdrawAlert" />
          <img class="cancel" @click="cancelBack" :src="cancel" />
        </Popup>
      </div>
      <!-- 提交报错提示 -->
      <van-dialog
        v-model="showSubmitDialog"
        show-cancel-button
        className="dialog-after-withdraw"
        :showConfirmButton="false"
        :showCancelButton="false"
      >
        <div class="title">Note!</div>
        <div class="tips" v-text="showMsg"></div>
        <div class="dia-button">
          <div class="ok" @click="hideDialog()">OK</div>
        </div>
      </van-dialog>
      <div class="note-tips">
        <Popup v-model="showTips" :close-on-click-overlay="false">
          <div class="control">
            <img :src="tips" />
            <div class="wrap">
              <div class="title">Note!</div>
              <div class="content" v-text="freezeMsg"></div>
              <div class="confirm" @click="hideShowTips()">OK</div>
            </div>
          </div>
        </Popup>
      </div>
    </CPage>
    <reasonPopup @close="hideReasonPopup" :showReason="showReasonPopup" :reasonObj="reasonObj[reasonType]" mainButton="Submit" sideButton="Cancel" :productSource="productSource"></reasonPopup>
    <CDialog
      title="Note!"
      :content="popTips"
      @mainAction="hideProDialog"
      :show="showNoProDialog"
      mainButton="OK"
    ></CDialog>
    <giftPopupWayacredit
      :show="showGiftPopup"
      :step="stepGiftPopup"
      :usableLmtAmt="maxUsableLmtAmt"
      :purpose="loanDetail.purpose"
      @hidePopup="hideGiftPopup"
      @choose="startPurpose"
    ></giftPopupWayacredit>
    <faqPopup ref="faqPopupDom"></faqPopup>
    <Question @question="startQuestion"></Question>
    <NinCheck @close="closeNinCheck" ref="ninCheck"></NinCheck>
  </div>
</template>

<script>
import publicMixns from "../indexMixins.js";
export default {
  name: "wayacreditWithdraw",
  mixins: [publicMixns],
  data() {
    return {

    };
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
.wrap {
  width: 100%;
  height: 100%;
  ::v-deep .c-header{
    background-color: #1E1E20;
    .title {
      color: #fff;
    }
  }
  .home {
    padding-bottom: 120px;
    background: #1E1E20;
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 12px;

    .increase-limit-tips{
      margin: 0 12px -17px 12px;
      background: rgba(255, 229, 0, 0.10);
      height: 44px;
      color: #FF8A00;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      text-align: left;
      padding-left: 13px;
      position: relative;
      img{
        width: 24px;
        height: 25px;
        margin-right: 2px;
        margin-top: 10px;
      }
      .tip-content{
        transform: scale(.85);
        display: inline-block;
        position: absolute;
        left: 19px;
        top: 8px;
        width: 336px;
        span{
          font-weight: 900;
        }
      }
    }
    .loan-amount {
      text-align: left;
      padding: 0 12px;
      // background: #ffffff;
      padding-bottom: 10px;
      border: 1px solid #00FFAD;
      margin: 12px 12px 0 12px;
      border-radius: 8px;
      height: 70px;
      transform: perspective(10px);
      .title {
        font-size: 12px;
        font-weight: 500;
        text-align: left;
        color: #fff;
        line-height: 19px;
        font-family: Avenir, Avenir-Medium;
        margin-top: 3px;
      }
      .input-amount {
        height: 45px;
        display: flex;
        align-items: flex-end;
        position: relative;
        .currency {
          font-weight: 700;
          color: #fff;
          font-size: 32px;
        }
        input {
          border: none;
          height: 40px;
          color: #fff;
          font-size: 32px;
          font-family: DINAlternate, DINAlternate-Bold;
          line-height: 45px;
          font-weight: 700;
          margin-left: 5px;
          width: calc(100% - 19px);
          background: transparent;
          outline: none;
          position: relative;
          &::placeholder {
            font-size: 12px;
            font-weight: 600;
            color: rgb(177, 176, 176);
            position: absolute;
            top: 22px;
          }
        }
        .amount-add {
          position: absolute;
          left: 154px;
          top: -2px;
          transform: scale(0.85);
          .wrap {
            position: relative;
            .num {
              background-image: linear-gradient(
                90deg,
                #c78900 0%,
                #f0c400 100%
              );
              color: #ffffff;
              width: auto;
              white-space: nowrap;
              padding: 2px 4px;
              font-size: 12px;
            }
            .arrow {
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 5px 0 5px 7px;
              border-color: transparent transparent transparent #c78900;
              position: absolute;
              left: 0px;
              top: 12px;
            }
          }
        }
        .edit {
          width: 24px;
          height: 24px;
          position: absolute;
          right: -4px;
          top: 2px;
        }
      }

      .min-limit-warn {
        margin-top: 5px;
        font-size: 12px;
        color: red;
      }
      .limit {
        margin-top: 10px;
        .tip {
          width: 104px;
          height: 18px;
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #919db3;
          line-height: 18px;
        }
        .currency {
          width: 10px;
          height: 12px;
          font-size: 12px;
          font-family: Montserrat, Montserrat-Bold;
          font-weight: 700;
          color: #fff;
          line-height: 12px;
          margin-left: 5px;
          margin-bottom: 2px;
        }
        .num {
          width: 37px;
          height: 16px;
          font-size: 14px;
          font-family: DINAlternate, DINAlternate-Bold;
          font-weight: 700;
          color: #099bfa;
          line-height: 16px;
          margin-left: 5px;
        }
      }
    }
    .range {
      font-family: Avenir-Medium;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.70);
      letter-spacing: 0;
      font-weight: 500;
      transform: scale(0.84);
      margin-left: -7px;
      margin-top: 3px;
      text-align: left;
    }
    .loan-terms {
      margin-top: 18px;
      margin-left: 12px;
      margin-right: 12px;
      ::v-deep .select-loan-terms {
        height: 80px;
        background: transparent;
        .left {
          padding-left: 12px;
          .title {
            color: $themeColor;
            font-size: 12px;
            font-family: Avenir;
            color: #fff;
          }
          .days {
            color: #00FFAD;
            font-size: 16px;
            font-weight: 900;
            font-family: DINPro;
          }
          .detail {
            font-family: Avenir;
            font-size: 12px;
            margin-left: -24px;
          }
        }
      }
    }
    .choose-others {
      border: 0.5px solid #FFF;
      background: rgba(40, 42, 48, 0.80);
      border-radius: 8px;
      text-align: left;
      padding: 4px 0 0 12px;
      margin-top: 14px;
      margin-left: 12px;
      margin-right: 12px;
      .title {
        width: 125px;
        height: 19px;
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #536887;
        line-height: 19px;
      }
      .staging {
        margin-top: 7px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        li {
          display: flex;
          margin-top: 10px;
          background: #f3f5f6;
          align-items: center;
          justify-content: center;
          margin-right: 6px;
          color: #536887;
          min-width: 60px;
          height: 32px;
          border: 1px solid #c4cad5;
          border-radius: 23px;
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          color: #c4cad5;
          padding-left: 5px;
          padding-right: 5px;
          &.active {
            background: $themeColor;
            color: #ffffff;
            border: 1px solid $themeColor;
          }
          .time {
            text-align: left;
          }
          .day {
            text-align: left;
            margin-left: 2px;
          }
        }
      }
      .select-list {
        .custom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 42px;
          position: relative;
          .acc-pro-ser {
            display: flex;
            align-items: center;
            .icon {
              width: 14px;
              height: 14px;
              margin-top: -2px;
              margin-right: 2px;
            }
            .question {
              width: 14px;
              height: 14px;
            }
            ::v-deep .c-popover {
              margin-left: 2px;
              .popover-tips {
                right: 46px;
                top: -45px;
              }
              .wrap {
                width: 14px;
                height: 14px;
                display: flex;
                align-items: center;
              }
            }
          }
          .item-title {
            font-size: 12px;
            font-family: Avenir;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.50);
          }
          .right {
            display: flex;
            align-items: center;
            margin-right: 10px;
            height: 40px;
            .tips-content{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                text-align: left;
                color: #c4cad5;
                margin-right: 3px;
                color: $themeColor;
                height: 20px;
                line-height: 20px;
              }
              ::v-deep .c-money{
                text-align: right;
                display: flex;
                align-items: center;
                .monetary-unit{
                  font-size: 12px;
                  color: $themeColor;
                  transform: scale(1) !important;
                }
                .currency-num{
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  color: $themeColor;
                  margin-left: 0px;
                }
            }
            .no-selected {
              font-size: 12px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              color: #c4cad5;
            }
            .available {
              color: $themeColor;
            }
            .selected {
              color: #00FFAD;
            }
            .no-data {
              color: #c4cad5;
            }
            .amount-fee {
              font-family: Avenir-Medium;
              font-size: 12px;
              color: #02b17b;
              text-align: right;
            }
            .after {
              display: flex;
              align-items: center;
              &.check-acc {
                display: flex;
                align-items: center;
                img {
                  width: 18px;
                  height: 18px;
                  margin-left: 5px;
                }
              }
              .currency {
                font-size: 12px;
                font-family: Montserrat, Montserrat-Bold;
                text-align: right;
                color: #fff;
                line-height: 12px;
              }
              .value {
                height: 23px;
                font-size: 12px;
                font-family: DINAlternate, DINAlternate-Bold;
                text-align: right;
                color: #fff;
                line-height: 23px;
              }

              .cal {
                font-size: 12px;
                color: #c4cad5;
                font-weight: 500;
              }
            }
            .van-icon-arrow:before {
              color: #d8d8d8;
            }
          }
          .use-scene-5{
            height: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .item{
              display: flex;
              justify-content: center;
              color: $themeColor;
              justify-content: flex-end;
              height: 20px;
              ::v-deep .c-money{
                .monetary-unit{
                  transform: scale(1) !important;
                }
              }
            }
          }
        }
        .change-bank {
          height: 40px;
          font-weight: 600;
          border-radius: 12px;
          display: flex;
          align-items: center;
          padding: 10px 0px;
          margin-right: 10px;
          justify-content: space-between;
          &.add-bank {
            justify-content: space-between;
            .detail {
              color: rgba(255, 255, 255, 0.50);
              font-size: 12px;
              width: 120px;
            }
            .right {
              margin-bottom: 2px;
              display: flex;
              align-items: center;
              font-family: DINAlternate-Bold;
              font-size: 12px;
              color: #ff8a51;
              text-align: left;
              font-weight: 700;
            }
          }
          .van-icon-arrow:before {
            color: #d8d8d8;
          }
          .change {
            color: #c4cad5;
            font-size: 12px;
            height: 40px;
            display: flex;
            align-items: center;
            font-weight: normal;
          }
          .faq {
            flex: none;
            color: $themeColor;
            font-size: 12px;
            height: 40px;
            display: flex;
            align-items: center;
            text-decoration: underline;
          }
          img {
            flex: none;
            width: 40px;
            height: 40px;
            margin-right: 5px;
          }
          .detail {
            width: 196px;
            margin-bottom: 10px;
            .name {
              font-size: 12px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              text-align: left;
              color: rgba(255, 255, 255, 0.50);
              line-height: 19px;
              width: 100%;
            }
            .num {
              font-size: 14px;
              font-weight: 700;
              text-align: left;
              color: #fff;
              line-height: 14px;
              width: 100%;
            }
          }
          &.show-bank-acct{
            display: block;
            height: auto;
            .wrap{
              height: 40px;
              font-weight: 600;
              border-radius: 12px;
              display: flex;
              align-items: center;
              padding: 10px 0px;
              margin-right: 10px;
              justify-content: space-between;
            }
            .agree{
              display: flex;
              align-items: flex-start;
              padding: 4px 8px;
              background: #F2F3F8;
              img{
                width: 15px;
                height: 15px;
              }
              div{
                color: #989898;
                font-family: Avenir;
                font-size: 11px;
                font-style: normal;
                font-weight: 600;
                span{
                  color: $themeColor;
                  text-decoration-line: underline;
                }
              }
            }
          }
        }
        .add-bank {
          .detail {
            margin-bottom: 0;
          }
        }
      }
    }
    .repayment-schedule {
      // background: #ffffff;
      text-align: left;
      padding: 8px 0 0 0px;
      margin-left: 25px;
      margin-right: 25px;
      .title {
        height: 20px;
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #919db3;
        line-height: 20px;
      }
      .custom {
        display: flex;
        justify-content: space-between;
        height: 32px;
        align-items: center;
        position: relative;
        &.interest {
          .right {
            align-items: flex-end;
            flex-direction: column-reverse;
          }
        }
        &.repayment-plan {
          .right {
            display: flex;
            align-items: center;
            width: 40px;
            height: 40px;
            text-align: right;
            justify-content: flex-end;
            .check {
              color: #00FFAD;
              text-align: right;
              font-family: HarmonyOS Sans SC;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              text-decoration-line: underline;
            }
          }
        }
        .item-title {
          height: 19px;
          font-size: 12px;
          font-family: Avenir;
          font-weight: 500;
          text-align: left;
          color: rgba(255, 255, 255, 0.50);
          line-height: 19px;
        }
        .right {
          text-align: right;
          display: flex;
          align-items: center;
          .after {
            display: flex;
            align-items: center;
            &.check-acc {
              display: flex;
              align-items: center;
              img {
                width: 18px;
                height: 18px;
                margin-left: 5px;
              }
            }
            .currency {
              font-size: 12px;
              font-family: Montserrat, Montserrat-Bold;
              text-align: right;
              color: #fff;
              line-height: 12px;
            }
            .value {
              height: 23px;
              font-size: 12px;
              font-family: DINAlternate, DINAlternate-Bold;
              text-align: right;
              color: #fff;
              line-height: 23px;
              .interest {
                margin-left: 2px;
              }
            }

            .cal {
              font-size: 14px;
              color: #c4cad5;
              font-weight: 500;
            }
          }
          .before {
            &.reduce {
              margin-top: 6px;
              text-decoration: line-through;
              margin-right: 5px;
              .currency {
                color: #fff;
                font-size: 14px;
              }
              .value {
                margin-left: 0px;
              }
            }
            .currency {
              height: 12px;
              font-size: 12px;
              font-family: Montserrat, Montserrat-Bold;
              text-align: right;
              line-height: 12px;
              color: #fff;
            }
            .value {
              height: 14px;
              font-size: 12px;
              font-family: DINAlternate, DINAlternate-Bold;
              text-align: right;
              color: #919db3;
              line-height: 14px;
              margin-left: 4px;
            }
          }
        }
      }
    }
    .contract {
      height: 19px;
      font-size: 14px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: $themeColor;
      line-height: 19px;
      margin-top: 18px;
      margin-left: 25px;
      margin-right: 12px;
    }
    .withdraw {
      width: calc(100% - 48px);
      position: fixed;
      bottom: 55px;
      left: 24px;
      border-radius: 10px;
      background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
      color: #282A30;
      text-align: center;
      font-family: Noto Sans;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
    }
    .bottom-text {
      font-family: Avenir-Medium;
      font-size: 12px;
      color: #989898;
      font-weight: 500;
      text-align: left;
      margin-top: 15px;
      line-height: 20px;
      margin-left: 25px;
      margin-right: 25px;
      span {
        color: #00FFAD;
        font-weight: 500;
      }
    }
    .bottom-text-below {
      position: fixed;
      bottom: 0px;
      left: 0px;
      font-family: Avenir-Medium;
      text-align: left;
      line-height: 20px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #ff7400;
      font-weight: 800;
      width: 100%;
      justify-content: center;
      height: 55px;
      img {
        width: 24px;
        height: 24px;
        margin-right: 4px;
      }
    }
    .van-popup {
      background: rgba(120, 120, 120, 0);
      .purpose-list {
        margin: 0;
        padding: 0 24px;
        list-style: none;
        height: 428px;
        border-radius: 23px 23px 0px 0px;
        background: #26282D;
        overflow-y: scroll;
        overflow-x: hidden;
        .title {
          color: rgba(255, 255, 255, 0.90);
          font-family: Noto Sans;
          font-size: 18px;
          font-style: normal;
          font-weight: 700;
          padding: 16px 0;
        }
        li {
          text-align: left;
          padding: 16px 0;
          font-size: 14px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          align-items: center;
          img {
            width: 20px;
            height: 20px;
          }
          .choosed {
            color: #00FFAD;
          }
        }
      }
      .product-list {
        padding: 25px 0 0 0px;
        .title {
          padding-left: 30px;
        }
        .list {
          margin-top: 15px;
          overflow-y: scroll;
          height: 440px;
          .product {
            display: flex;
            justify-content: space-between;
            padding-right: 19px;
            align-items: center;
            padding-left: 30px;
            padding-top: 10px;
            padding-bottom: 10px;
            &.active {
              background: #fafafa;
            }
            .left {
              text-align: left;
              .item {
                margin-bottom: 3px;
                position: relative;
                .day {
                  font-family: DINAlternate-Bold;
                  font-size: 24px;
                  color: #00FFAD;
                  letter-spacing: 0;
                }
                .amount-add {
                  position: absolute;
                  left: 96px;
                  top: -8px;
                  transform: scale(0.85);
                  .wrap {
                    position: relative;
                    .num {
                      background-image: linear-gradient(
                        90deg,
                        #c78900 0%,
                        #f0c400 100%
                      );
                      color: #ffffff;
                      width: auto;
                      white-space: nowrap;
                      padding: 2px 4px;
                      font-size: 12px;
                    }
                    .arrow {
                      width: 0;
                      height: 0;
                      border-style: solid;
                      border-width: 5px 0 5px 7px;
                      border-color: transparent transparent transparent #c78900;
                      position: absolute;
                      left: 0px;
                      top: 12px;
                    }
                  }
                }
              }
              .detail {
                font-family: Avenir-Medium;
                font-size: 12px;
                color: #919db3;
                letter-spacing: 0;
                margin-bottom: 2px;
              }
              .rate {
                font-family: Avenir-Medium;
                font-size: 12px;
                color: #919db3;
                letter-spacing: 0;
              }
            }
            img {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
    .input-pin {
      &.van-popup {
        border-radius: 23px 23px 0px 0px;
        background: #26282D;
        .van-popup__close-icon--top-left {
          top: 12px;
        }
      }
      .title {
        padding: 16px 24px;
        color: rgba(255, 255, 255, 0.90);
        font-family: Noto Sans;
        font-size: 19px;
        font-style: normal;
        font-weight: 700;
        border-bottom: 1px solid #b5abab;
      }

      .pin-list {
        display: flex;
        margin-top: 23px;
        padding: 0 45px;
        li {
          width: 50px;
          height: 50px;
          margin-right: 21px;
          position: relative;
          .hide-control {
            width: 50px;
            height: 50px;
            border-bottom: 1px solid #bfc4ca;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            background: #ffffff;
            z-index: -1;
            &.control {
              z-index: 2;
            }
            .hide {
              width: 12px;
              height: 12px;
              background: #1b3155;
              border-radius: 12px;
            }
          }
          input {
            width: 50px;
            height: 50px;
            border: none;
            border-bottom: 1px solid #bfc4ca;
            font-size: 25px;
            text-align: center;
          }
        }
      }
      .forget-pin {
        font-size: 16px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        color: $themeColor;
        margin-top: 25px;
        text-align: center;
      }
    }
    .bankcard-list {
      &.van-popup {
        border-radius: 23px 23px 0px 0px;
        background: #26282D;
        .van-popup__close-icon--top-left {
          top: 12px;
          color: #3e6fb3;
        }
      }
      .title {
        margin-top: 45px;
        font-size: 24px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #fff;
        margin-left: 18px;
      }
      .wrap {
        position: absolute;
        z-index: 1;
        top: 3px;
        right: 4px;
        width: 50px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        .add-bank-icon {
          width: 25px;
          height: 25px;
        }
      }
      .bank-list {
        margin-top: 26px;
        li {
          display: flex;
          align-items: center;
          padding: 0 19px;
          margin-bottom: 20px;
          .logo {
            width: 32px;
            height: 32px;
          }
          .name {
            font-size: 14px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #fff;
            margin-left: 17px;
            margin-right: 17px;
            width: 254px;
            .bank-name{
              color: #fff;
              font-family: Avenir;
              font-size: 14px;
              font-style: normal;
              font-weight: 500;
              line-height: 14px;
              text-align: left;
              img{
                width: 16px;
                height: 16px;
              }
            }
            .recommand-tips{
              color: #fff;
              font-family: Avenir;
              font-size: 10px;
              font-style: normal;
              font-weight: 500;
              line-height: normal;
              text-align: left;
            }
          }
          .choose {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
    .reloan-tips {
      &.van-popup {
        width: calc(100% - 44px);
        border-radius: 18px;
        border: 1px solid #FFF;
        background: #26282D;

        .control {
          position: relative;
          img {
            width: 175px;
            height: 94px;
            position: absolute;
            top: -25px;
            right: 0;
          }
          .wrap {
            height: 100%;
            padding-bottom: 35px;
            .title {
              color: #FFF;
              font-family: HarmonyOS Sans SC;
              font-size: 19px;
              font-style: normal;
              font-weight: 700;
              padding-top: 32px;
              padding-left: 18px;
              padding-bottom: 12px;
              text-align: left;
            }
            .content {
              color: rgba(255, 255, 255, 0.70);
              font-family: HarmonyOS Sans SC;
              font-size: 12px;
              font-style: normal;
              font-weight: 500;
              padding-left: 18px;
              padding-right: 18px;
              text-align: left;
            }
            .confirm {
              height: 42px;
              border-radius: 10px;
              background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
              box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #282A30;
              text-align: center;
              font-family: HarmonyOS Sans SC;
              font-size: 16px;
              font-style: normal;
              font-weight: 700;
              margin-left: 18px;
              margin-right: 18px;
              margin-top: 32px;
            }
          }
        }
      }
    }
  }
}
.dialog-control {
  border-radius: 18px;
  border: 1px solid #FFF;
  background: #26282D;
  padding-bottom: 10px;
  width: calc(100% - 44px);
  ::v-deep .van-dialog__content {
    min-height: 70px;
    .tips {
      padding: 24px 13px;
      text-align: left;
      color: #fff;
    }
  }
  ::v-deep .van-button--default{
    background: transparent;
  }
  ::v-deep .van-hairline--top::after {
    border: none;
  }
  ::v-deep .van-button__content {
    span {
      background: $themeColor;
      display: inline-block;
      width: 90%;
      color: #282A30;
      height: 36px;
      border-radius: 8px;
      line-height: 36px;
    }
  }
  .van-dialog__message--left {
    padding: 15px 24px;
  }
}

.dialog-after-withdraw {
  width: calc(100% - 44px);
  border-radius: 18px;
  border: 1px solid #FFF;
  background: #26282D;
  .van-dialog__content {
    .title {
      color: #FFF;
      font-family: HarmonyOS Sans SC;
      font-size: 19px;
      font-style: normal;
      font-weight: 700;
      margin-top: 32px;
      margin-left: 18px;
      text-align: left;
    }
    .tips {
      color: rgba(255, 255, 255, 0.70);
      font-family: HarmonyOS Sans SC;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      text-align: left;
      margin-top: 10px;
      margin-left: 18px;
      margin-right: 18px;
      margin-bottom: 20px;
    }
    .dia-button {
      width: calc(100% - 36px);
      margin-left: 18px;
      height: 40px;
      line-height: 40px;
      border-radius: 10px;
      background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
      margin-bottom: 25px;
      .ok {
        color: #282A30;
        text-align: center;
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
      }
      .cancel {
        height: 40px;
        line-height: 40px;
        background: #ffffff;
        color: $themeColor;
        border-radius: 5px;
      }
    }
  }
  .van-dialog__content--isolated {
    min-height: 70px;
  }
  .van-dialog__message--left {
    padding: 15px 24px;
  }
}
.back-tips {
  .van-popup {
    .alert {
      width: 360px;
      height: 360px;
    }
    .cancel {
      width: 50px;
      height: 50px;
    }
  }
}
.note-tips {
  .van-popup {
    background-color: inherit;
    width: 304px;
    // box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);

    .control {
      position: relative;
      height: 225px;
      padding-top: 28px;

      img {
        width: 175px;
        height: 94px;
        position: absolute;
        top: 2px;
        right: 0;
      }
      .wrap {
        border-radius: 16px;
        background-color: #ffffff;
        height: 100%;
        .title {
          font-size: 24px;
          font-family: Avenir, Avenir-Heavy;
          font-weight: 800;
          text-align: left;
          color: #fff;
          line-height: 33px;
          padding-top: 36px;
          padding-left: 24px;
        }
        .content {
          font-size: 16px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #536887;
          line-height: 23px;
          padding-left: 24px;
        }
        .confirm {
          width: 272px;
          height: 42px;
          background: $themeColor;
          border-radius: 8px;
          box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: center;
          color: #ffffff;
          line-height: 25px;
          margin: 0 auto;
          margin-top: 23px;
        }
      }
    }
  }
}
.van-popup {
  ::v-deep .van-popover__action {
    height: auto;
  }
}
</style>
