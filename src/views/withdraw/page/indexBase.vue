<template>
  <div class="wrap">
      <c-header titleName="Withdraw Loan" :backFun="myBackFun"></c-header>
      <CPage className="home">
        <!-- 增信banner -->
        <template v-if="canShowAddPersonalInformation">
          <div class="increase-limit-upload" @click="showAddPersonalInformationBanner">
            <div class="tip-content">
              <span>Add additional information can increase your </span>
              <span class="sub"> credit limit by up to 50%</span>
              <span class="add">Add now</span>
            </div>
            <img src="@/assets/images/increaseLimit/money.png" />
          </div>
        </template>
        <template v-else>
          <div class="increase-limit-tips" v-if="remindUpLimit > 0">
            <img src="@/assets/images/common/increase-limit-tips.png" />
            <div class="tip-content">Congratulations! You have received ₦<span v-text="common.singleToThousands(remindUpLimit)"></span> credit limit increase.</div>
          </div>
        </template>
        <div class="loan-amount">
          <div class="title">Loan Amount</div>
          <div class="input-amount">
            <div class="currency">₦</div>
            <input @click="withdrawReport('withdraw_amount_click')" type="tel" @blur="startBlurLoanCalculate()" :placeholder="placeholderTips" @keydown="keyDown($event)" @keyup="keyUp()" v-model="loanDetail.amount">
              <div class="amount-add" v-if="installAmt > 0">
                <div class="wrap">
                  <div class="num" v-text="`+ ₦${common.singleToThousands(installAmt)} only for installment`"></div>
                  <div class="arrow"></div>
                </div>
              </div>
              <img class="edit" :src="require(`@/assets/images/${productSource}/edit.png`)" alt="">
          </div>
          <!-- <div class="min-limit-warn" v-if="showMinAlert" v-text="minAlertTips"></div> -->
        </div>
        <div class="range" v-text="rangeTips"></div>

        <div class="new-limit" v-if="chooseTypeData.maxAmount > 2000">
          <div class="base index">
            <img src="@/assets/images/common/index-amount.png" alt="">
            <div class="amount">
              <div class="currency">₦</div>
              <div class="num" v-text="common.singleToThousands(chooseTypeData.maxAmount)"></div>
            </div>
          </div>
          <div class="base second">
            <img src="@/assets/images/common/lock.png" alt="">
            <img v-if="showIncreaseCredit === 'Y' && androidVersion > 28" class="corner-mark" src="@/assets/images/common/corner-mark.png" alt="">
            <div class="amount" @click="chooseAmount">
              <div class="currency">₦</div>
              <div class="num" v-text="common.singleToThousands(chooseTypeData.maxAmount * 3)"></div>
            </div>
          </div>
          <div class="base third">
            <img src="@/assets/images/common/lock.png" alt="">
            <img v-if="showIncreaseCredit === 'Y' && androidVersion > 28" class="corner-mark" src="@/assets/images/common/corner-mark.png" alt="">
            <div class="amount" @click="chooseAmount">
              <div class="currency">₦</div>
              <div class="num" v-text="common.singleToThousands(chooseTypeData.maxAmount * 10)"></div>
            </div>
          </div>
        </div>

        <loanTerms customType="withdraw" class="test" v-model="longActive" :tag14and2User="tag14and2User" @chooseType="chooseLoanTermType" :productSource="productSource" :productList="loanDetail.productList" :proIndex="proIndex"></loanTerms>

        <div class="choose-others">
          <!-- 选择优惠券以及目的 -->
          <div class="select-list">
            <div class="custom coupon">
              <div class="item-title">Coupons</div>
              <div class="right" @click="chooseCoupons()">
                <template v-if="coupon.couponId && caculateStatus">
                  <template v-if="coupon.useScene !== '5' && +totalDue.interestRelief > 0">
                    <div class="tips-content">Interest Relief</div>
                    <CMoney :currencyNum="common.thousandsToFixed(totalDue.interestRelief)"></CMoney>
                  </template>
                  <template v-else>
                    <div class="use-scene-5">
                      <div class="item" v-if="useScene5Coupon.limitIncrease > 0">
                        <div class="tips-content">Limit increase</div>
                        <CMoney :currencyNum="common.singleToThousands(useScene5Coupon.limitIncrease)"></CMoney>
                      </div>
                      <div class="item" v-if="+totalDue.interestRelief > 0">
                        <div class="tips-content">Interest relief</div>
                        <CMoney :currencyNum="common.thousandsToFixed(totalDue.interestRelief)"></CMoney>
                      </div>
                    </div>
                  </template>
                </template>
                <div class="no-selected available" :class="{'no-data': availCouponsNum === 0}" v-else v-text="'Available:' + availCouponsNum"></div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
            <div class="custom" @click="choosePurpose()">
              <div class="item-title">Loan Purpose</div>
              <div class="right">
                <div class="no-selected" :class="{'selected': loanDetail.purpose}" v-text="loanDetail.purpose ? loanDetail.purpose : 'Select'"></div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
            <div class="custom" v-if="loanDetail.repayment.showInsurance">
              <div class="acc-pro-ser">
                <img class="icon" src="@/assets/images/protection-service.png" alt="">
                <div class="item-title">Credit Protection Service</div>
                <CPopover :tipContent="'In the event of death or disability, we will waive your loan balance'">
                  <template v-slot:reference>
                    <img class="question" :src="require(`@/assets/images/${productSource}/question.png`)" alt="">
                  </template>
                </CPopover>
              </div>
              <div class="right">
                <div class="after check-acc" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value" v-text="common.thousandsToFixed(loanDetail.repayment.insuranceFee)"></span>
                  <img class="check" :src="checkShow ? require(`@/assets/images/${productSource}/index-paid.png`) : require('@/assets/images/un-paid.png')"
                  alt="" @click="checkAcc()">
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <!-- 钱包提现账户显示 -->
            <div class="change-bank show-bank-acct1" v-if="loanDetail.bankAcctList.length > 0 && isWalletWithdrawalUser">
              <div class="wrap">
                <img class="icon" :src="(chooseBankAcct && chooseBankAcct.bankLogoUrl) || ninBankImg" />
                <div class="detail">
                  <div class="name">Disbursement Account</div>
                  <div class="num" v-text="disbursementAccount"></div>
                </div>
                <div class="faq" @click="openFaq(chooseBankAcct.bankName)">Where disbursed?</div>
              </div>
            </div>
            <!-- 非钱包提现账户显示 -->
            <div class="change-bank not-nin" v-if="loanDetail.bankAcctList.length > 0 && !isWalletWithdrawalUser">
              <div class="disbursement">
                <div class="item">Disbursement Account</div>
                <div class="right" v-if="identityType !== 'NIN'">
                  <div class="change" @click="changeBank()">Change</div>
                  <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
                </div>
              </div>
              <!-- 存在goldman的账户显示 -->
              <div class="new-style goldman" :class="{'choosen': isChooseGoldmanAccount }" v-if="goldManAccount && goldManAccount.bankCode">
                <div class="top">
                  <img src="@/assets/images/common/disbursed-faster.png" alt="">
                  <div class="disbursed" @click="openFaqGoldman">View disbursement notes></div>
                </div>
                <div class="account">
                  <div class="right">
                    <img class="icon" :src="(goldManAccount && goldManAccount.bankLogoUrl) || goldManImg" />
                    <div class="num" v-text="goldManAccount.bankName + accountSlice(goldManAccount.bankAcctNo)"></div>
                  </div>
                  <div class="left">
                    <!-- Default -->
                    <img class="check" @click="checkAccount('goldman')" :src="isChooseGoldmanAccount ? require(`@/assets/images/${productSource}/index-paid.png`) : require('@/assets/images/un-paid.png')"/>
                  </div>
                </div>
              </div>
              <!-- 其他账号的显示 -->
              <div class="new-style other-default" :class="{'choosen': !isChooseGoldmanAccount }" v-if="otherBankAcct && otherBankAcct.bankCode">
                <div class="account">
                  <div class="right">
                    <img class="icon" :src="(otherBankAcct && otherBankAcct.bankLogoUrl) || backImg" />
                    <div class="num" v-text="otherBankAcct.bankName + accountSlice(otherBankAcct.bankAcctNo)"></div>
                  </div>
                  <div class="left">
                    <img class="check" @click="checkAccount('other')" :src="!isChooseGoldmanAccount ? require(`@/assets/images/${productSource}/index-paid.png`) : require('@/assets/images/un-paid.png')"/>
                  </div>
                </div>
              </div>
              <template v-if="isSupportBalance">
                <!-- 默认勾选的协议 -->
                <div class="agree" v-if="(goldmanDebitScene === 1 && enableGoldman) || (goldmanDebitScene === 2 && isChooseGoldmanAccount)">
                  <img src="@/assets/images/common/agree-choose.png" alt="">
                  <div v-if="goldmanDebitScene === 1">Agree <span @click="goAgree">Authorisation for Disclosure of Personal Information</span> and <span @click="goDirectDebit">Direct Debit Mandate</span> open Goldman Bank account</div>
                  <div v-if="goldmanDebitScene === 2">Agree <span @click="goDirectDebit">Direct Debit Mandate</span></div>
                </div>
              </template>
            </div>
            <!-- 其他情况的未绑定账号的情况 -->
            <div class="add-bank change-bank" @click="addBank(2)" v-if="loanDetail.bankAcctList.length == 0 && !isWalletWithdrawalUser">
              <div class="detail">Add a Bank Account</div>
              <div class="right">
                <div>To get a mysterious gift</div>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 借款信息 -->
        <div class="repayment-schedule" v-if="longActive > -1">
          <div class="repayment-list">
            <div class="custom" v-if="(loanDetail.repayment.platformFee && loanDetail.repayment.platformFee) > 0">
              <div class="item-title">Platform Fee</div>
              <div class="right">
                <div class="after" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value">{{ common.thousandsToFixed(loanDetail.repayment.platformFee && loanDetail.repayment.platformFee)}}</span>
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <div class="custom">
              <div class="item-title">Received</div>
              <div class="right">
                <div class="after" v-if="caculateStatus">
                  <span class="currency">₦</span>
                  <span class="value">{{ common.thousandsToFixed(loanDetail.repayment.actualTotalAmount && loanDetail.repayment.actualTotalAmount - (loanDetail.repayment.platformFee && loanDetail.repayment.platformFee))}}</span>
                </div>
                <div class="after" v-else>
                  <span class="cal">Calculating</span>
                </div>
              </div>
            </div>
            <!-- 单期显示 -->
            <template  v-if="loanDetail.repaymentDetailList.length == 1">
              <div class="repayment-plan custom">
                <div class="item-title">Due Date</div>
                <div class="right">
                  <div class="after" v-if="caculateStatus">
                    <!-- <span class="currency">₦</span> -->
                    <span class="value">{{ (loanDetail.repayment.deadline && loanDetail.repayment.deadline)}}</span>
                  </div>
                  <div class="after" v-else>
                    <span class="cal">Calculating</span>
                  </div>
                </div>
              </div>
              <div class="repayment-plan repayment-amount custom">
                <div class="item-title">Repayment Amount</div>
                <div class="right">
                  <template v-if="caculateStatus">
                    <div class="after" >
                      <span class="currency">₦</span>
                      <span class="value">{{ common.thousandsToFixed(totalDue.outstdBal)}}</span>
                    </div>
                    <div v-if="totalDue.totalAmountDue != totalDue.outstdBal" class="before">
                      <span class="currency">₦</span>
                      <span class="value">{{ common.thousandsToFixed(totalDue.totalAmountDue)}}</span>
                    </div>
                  </template>
                  <div class="after" v-else>
                    <span class="cal">Calculating</span>
                  </div>
                </div>
              </div>
            </template>
            <!-- 非还款再借产品 -->
            <template v-if="loanDetail.repaymentDetailList.length > 1">
              <div class="repayment-plan multiple custom" @click="showPlan()">
                <div class="item-title">Repayment Plan</div>
                <div class="right">
                  <template v-if="+totalDue.interestRelief > 0">
                    <span class="currency">-₦</span>
                    <span class="value">{{ common.thousandsToFixed(totalDue.interestRelief) }}</span>
                  </template>
                  <i class="van-icon van-icon-arrow van-cell__right-icon" v-if="caculateStatus"></i>
                  <div class="after" v-else>
                    <span class="cal">Calculating</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div v-if="longActive != -1" class="contract" @click="viewContract()">View The Contract</div>
        <p class="bottom-text" v-if="bottomText" v-html="bottomText"></p>
        <CButton @buttonClick="goWithdraw(0)" className="withdraw" name="Withdraw"></CButton>
        <!--  PC七周年活动 -->
        <template v-if="activityStatus === 'Valid'">
          <div class="pc_7th_activicty_btn_box">
            <img class="gold" src="@/assets/images/pc7thActivity/gold.png" alt="" />
            <img class="gift_box" src="@/assets/images/pc7thActivity/gift_box.png" alt="" />
            <div class="withdraw-click" @click="goWithdraw(0)"></div>
            <div class="bottom-text-below">
              <img src="@/assets/images/pc7thActivity/june_gift.png" alt="" />
              <div class="text">Withdraw to SPLASH up to ₦10M Cash!</div>
            </div>
          </div>
        </template>
        <!-- 六月活动 -->
        <!--<template v-if="activityStatus === 'Valid'">
          <div class="june_activicty_btn_box">
            <img class="gold" src="@/assets/images/juneActivity/gold.png" alt="" />
            <img class="june_gift_box" src="@/assets/images/juneActivity/june_gift_box.png" alt="" />
            <div class="withdraw-click" @click="goWithdraw(0)"></div>
            <div class="bottom-text-below">
              <img src="@/assets/images/juneActivity/june_gift.png" alt="" />
              <div class="text">Withdraw to slash your interest up to 50%!</div>
            </div>
          </div>
        </template>-->
        <!-- 圣诞活动蒙层 -->
        <!-- <template v-if="activityStatus === 'Valid'">
          <img class="withdraw-bg" src="@/assets/images/christmasActivities/withdraw-bg.png" alt="">
          <div class="withdraw-click" @click="goWithdraw(0)"></div>
          <div class="bottom-text-below">
            <img src="@/assets/images/christmasActivities/christmas-person.png" alt="" />
            <div>Withdraw to win interest-free</div>
          </div>
        </template> -->
        <!-- 万圣节活动蒙层 -->
        <!-- <template v-if="activityStatus === 'Valid'">
          <img class="withdraw-bg bg1" src="@/assets/images/halloweenActivities/withdraw-bg.png" alt="">
          <div class="withdraw-click" @click="goWithdraw(0)"></div>
          <div class="bottom-text-below">
            <img src="@/assets/images/halloweenActivities/christmas-person.png" alt="" />
            <div>Withdraw to win up to 70% off bonus.</div>
          </div>
        </template> -->
        <template v-else>
          <div class="bottom-text-below" v-if="isNew">
            <img src="@/assets/images/ic.png" alt="" />
            <div v-text="maxUsableLmtAmt >= 20000 ? 'Bind bank card, interest rate -2%' : 'Withdraw to get up to ₦500,000 credit limit'"></div>
          </div>
        </template>
        <!-- 还款详情 -->
        <repaymentDetailPopup :repaymentDetailList="loanDetail.repaymentDetailList" :totalInterestRelief="totalDue.interestRelief" @close="closePlan()" :showRepayDetailPop="show" />
        <!-- 验证pin -->
        <Popup v-model="showPin" position="bottom" title="test" class="input_pin_pop_wrapper">
          <div class="title_box">
            <img @click="closePinPop" class="icon_close" src="@/assets/images/cancel.png" alt="">
          </div>
          <div class="input_pin_pop_content">
            <div class="title" v-text="`${channel} PIN`"></div>
            <ul class="pin-list">
              <li v-for="(pin, index) in pinList" :key="index + 'key'">
                <input v-model="pinList[index].value" :ref="'input' + index" @keyup="inputPin($event, index)" type="number" />
                <div class="hide-control" :class="{'control': pin.value !== '' || !pin.showInput}" >
                  <div v-if="pin.value !== ''" class="hide"></div>
                </div>
              </li>
            </ul>
            <div class="try_another_way_wrapper">
              <div class="try_another_way_title">Try another way:</div>
              <div class="try_anonther">
                <span class="item item_1" @click="chooseOtpVerify">OTP Verify ></span>
                <!-- <span class="item item_2">Face Verify ></span> -->
                <span class="item item_3" @click="forgetPin()">Reset PIN ></span>
              </div>
            </div>
          </div>
        </Popup>
        <!-- OTP verify -->
        <Popup v-model="showOtpVerifyPop" position="bottom" class="otp_verify_pop_wrapper">
          <div class="title_box">
            <img @click="closeOtpVerifyPop" class="icon_close" src="@/assets/images/cancel.png" alt="">
          </div>
          <div class="otp_verify_pop_content">
            <div class="title">OTP verify</div>
            <div class="otp_pop_tip">A message with an One-Time-Password has<br> been sent to {{ phone ? phone.substring(0, 3) + "****" + phone.substring(7) : ''}}. Please enter</div>
            <ul class="otp_list">
              <li v-for="(item, index) in otpList" :key="index + 'key'">
                <input v-model="otpList[index].value" :ref="'inputOtp' + index" @keyup="otpKeyUp($event, index)" type="tel" maxlength="1" />
              </li>
            </ul>
            <div class="get_otp_box enable" :class="[enableReGetOtpBtn ? 'enable' : 'disable']" @click="reGetOtpBtnClick">
              {{ enableReGetOtpBtn ? `Re-get OTP` : `${optCount}s` }}
            </div>
          </div>
        </Popup>
        <!-- 显示借款目的 -->
        <Popup v-model="show1" position="bottom" @close="closePurpose()">
          <ul class="purpose-list">
            <li class="cancel">Please select the loan purpose</li>
            <li class="van-hairline--bottom" v-for="(purpose, index) in loanDetail.purposeList" :key="purpose" @click="selectedPurpose(index)">
              <span :class="{'choosed': index === purposeActive}" v-text="purpose"></span>
              <img v-if="index === purposeActive" :src="pick" alt="">
            </li>
          </ul>
        </Popup>
        <!-- 显示银行账户 -->
        <Popup closeable close-icon-position="top-left" v-model="showCardList" position="bottom" title="test" :style="{ height: '55%' }" class="bankcard-list">
          <div class="title">Select the Disbursement Account to use</div>
          <div class="wrap" @click="addBank(2)">
            <img class="add-bank-icon" :src="addBankIcon" alt="">
          </div>
          <ul class="bank-list">
            <li v-for="(card, index) in loanDetail.bankAcctList" :key="index + 'key'">
              <img class="logo" :src="card.bankLogoUrl || defaultBankAcctImg(card)" alt="">
              <div class="name">
                <div class="bank-name">
                  {{ card.bankName + (card.bankAcctNo ? '(' + card.bankAcctNo.slice(-4, card.bankAcctNo.length) + ')' : '') }}
                  <img v-if="card.bankCode === '090574'" src="@/assets/images/common/bank-recommand.png" alt="">
                </div>
                <div v-if="card.bankCode === '090574'" class="recommand-tips">Balance can enjoy higher deposit interest</div>
              </div>
              <img class="choose" :src="index === bankIndex ? require(`@/assets/images/${productSource}/index-paid.png`) : unchecked" alt="" @click="changeBankCard(index)">
            </li>
          </ul>
        </Popup>
        <Popup v-model="showRelonTips" :close-on-click-overlay='false' class="reloan-tips">
            <div class="control">
                <div class="wrap">
                    <div class="title">Warm Tips</div>
                    <div class="content">Repay and Re-loan: this function allows you to repay and apply for another loan while payment is pending. Loan will be disbursed after the payment is successful.</div>
                    <div class="confirm" @click="tipConfirm()">OK</div>
                </div>
            </div>
        </Popup>
        <van-dialog v-model="showDialog" className="dialog-control" confirmButtonText="To add one" @confirm="confirmAddBankAcc()">
            <div class="tips">Kindly add your bank account to receive loans.</div>
        </van-dialog>
        <div class="back-tips">
            <Popup v-model="showBackDialog" :close-on-click-overlay='false'>
              <img class="alert" @click="getNow" :src="withdrawAlert" />
              <img class="cancel"  @click="cancelBack" :src="cancel" />
            </Popup>
        </div>
        <!-- 提交报错提示 -->
        <van-dialog v-model="showSubmitDialog" show-cancel-button className="dialog-after-withdraw" :showConfirmButton="false" :showCancelButton="false">
            <div class="title">Note!</div>
            <div class="tips" v-text="showMsg"></div>
            <div class="dia-button">
                <div class="ok" @click="hideDialog()">OK</div>
            </div>
        </van-dialog>
        <div class="note-tips">
            <Popup v-model="showTips" :close-on-click-overlay='false'>
                <div class="control">
                    <img :src="tips" />
                    <div class="wrap">
                        <div class="title">Note!</div>
                        <div class="content" v-text="freezeMsg"></div>
                        <div class="confirm" @click="hideShowTips()">OK</div>
                    </div>
                </div>
            </Popup>
        </div>
      </CPage>
      <reasonPopup @close="hideReasonPopup" :showReason="showReasonPopup" :reasonObj="reasonObj[reasonType]" mainButton="Submit" sideButton="Cancel" :productSource="productSource"></reasonPopup>
      <CDialog title="Note!" :content="popTips" @mainAction="hideProDialog" :show="showNoProDialog" mainButton="OK"></CDialog>
      <giftPopup :show="showGiftPopup" :step="stepGiftPopup" :usableLmtAmt="maxUsableLmtAmt" :purpose="loanDetail.purpose" @hidePopup="hideGiftPopup" @choose="startPurpose"></giftPopup>
      <faqPopup ref="faqPopupDom"></faqPopup>
      <faqGoldman :setting="faqGoldmanSetting" ref="faqGoldmanDom"></faqGoldman>
      <Question @question="startQuestion"></Question>
      <NinCheck @close="closeNinCheck" @openFaq="openFaqGoldman" ref="ninCheck"></NinCheck>
      <addPersonalInformation v-model="showAddPersonalInformation" @hidePopup="hideAddPersonalInformation"></addPersonalInformation>
      <!-- 用户选择特定选项后，第二次返回的挽留弹窗 -->
      <GiftDialog :giftDialogType="giftDialogType" @refreshLoan="refreshLoan" @close="closeGiftDialog" :show="showGiftDialog" :activityText="giftDialogActivityText" />
      <!-- 提现受限弹窗 -->
      <WithdrawLimitedDialog :show="showWithdrawLimitedDialog" :showContactBankButton="showContactBankButton" @close="closeWithdrawLimitedDialog" @changeBankAccount="handleChangeBankAccount" />
  </div>
</template>

<script>
import publicMixns from '../indexMixins.js'
export default {
    name: 'indexBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>
<style lang="scss" scoped>
  .wrap{
      height: 100%;
      .home{
      padding-bottom: 120px;
      background: #ffffff;
      padding-left: 0px;
      padding-right: 0px;

      .increase-limit-tips{
        margin: 12px 12px -17px 12px;
        background: rgba(255, 229, 0, 0.10);
        height: 44px;
        color: #FF8A00;
        font-family: Avenir;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        text-align: left;
        padding-left: 13px;
        position: relative;
        img{
          width: 24px;
          height: 25px;
          margin-right: 2px;
          margin-top: 10px;
        }
        .tip-content{
          transform: scale(.85);
          display: inline-block;
          position: absolute;
          left: 19px;
          top: 8px;
          width: 336px;
          span{
            font-weight: 900;
          }
        }
      }
      .increase-limit-upload{
        border-radius: 12px;
        height: 53px;
        background: $themeColor;
        box-sizing: border-box;
        margin: 0 12px 14px;
        position: relative;
        display: flex;
        align-items: center;
        z-index: 5;
        img{
          width: 62px;
          height: 54px;
          position: absolute;
          right: 0;
          top: 0;
          z-index: 1;
        }
        .tip-content{
          width: 275px;
          color: #FFF;
          font-family: Avenir;
          font-size: 12px;
          font-style: normal;
          font-weight: 900;
          line-height: normal;
          text-align: left;
          margin-left: 10px;
          position: relative;
          z-index: 10;
          .sub{
            color: #FFF600;
            font-family: Avenir;
            font-size: 15px;
            font-style: normal;
            font-weight: 900;
            line-height: normal;
          }
          .add{
            width: 77px;
            height: 20px;
            border-radius: 4px;
            background: #FFF600;
            display: inline-block;
            text-align: center;
            line-height: 20px;
            color: #FF454F;
            font-family: Avenir;
            font-size: 12px;
            font-style: normal;
            font-weight: 900;
            margin-left: 6px;
          }
        }
      }
      .loan-amount {
        text-align: left;
        padding: 0 12px;
        background: #ffffff;
        padding-bottom: 10px;
        border: 1px solid #E3E5E9;
        margin: 12px 12px 0 12px;
        border-radius: 8px;
        height: 70px;
        transform: perspective(10px);
        .title{
          font-size: 12px;
          font-weight: 500;
          text-align: left;
          color: #919DB3;
          line-height: 19px;
          font-family: Avenir, Avenir-Medium;
          margin-top: 3px;
        }
        .input-amount{
          height: 45px;
          display: flex;
          align-items: flex-end;
          position: relative;
          .currency{
            font-weight: 700;
            color: #1B3155;
            font-size: 32px;
          }
          input{
            border: none;
            height: 40px;
            color: #1B3155;
            font-size: 32px;
            font-family: DINAlternate, DINAlternate-Bold;
            line-height: 45px;
            font-weight: 700;
            margin-left: 5px;
            width: calc(100% - 19px);
            // background: #fcfcfd;
            outline: none;
            position: relative;
            &::placeholder{
              font-size: 12px;
              font-weight: 600;
              color: rgb(177, 176, 176);
              position: absolute;
              top: 22px;
            }
          }
          .amount-add{
            position: absolute;
            left: 154px;
            top: -2px;
            transform: scale(.85);
            .wrap{
              position: relative;
              .num{
                background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
                color: #ffffff;
                width: auto;
                white-space: nowrap;
                padding: 2px 4px;
                font-size: 12px;
              }
              .arrow{
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 5px 0 5px 7px;
                border-color: transparent transparent transparent #C78900;
                position: absolute;
                left: 0px;
                top: 12px;
              }
            }
          }
          .edit{
            width: 24px;
            height: 24px;
            position: absolute;
            right: -4px;
            top: 2px;
          }
        }

        .min-limit-warn{
          margin-top: 5px;
          font-size: 12px;
          color: red;
        }
        .limit{
          margin-top: 10px;
          .tip{
            width: 104px;
            height: 18px;
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #919db3;
            line-height: 18px;
          }
          .currency{
            width: 10px;
            height: 12px;
            font-size: 12px;
            font-family: Montserrat, Montserrat-Bold;
            font-weight: 700;
            color: #1b3155;
            line-height: 12px;
            margin-left: 5px;
            margin-bottom: 2px;
          }
          .num {
            width: 37px;
            height: 16px;
            font-size: 14px;
            font-family: DINAlternate, DINAlternate-Bold;
            font-weight: 700;
            color: #099bfa;
            line-height: 16px;
            margin-left: 5px;
          }
        }
      }
      .range{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: $themeColor;
        letter-spacing: 0;
        font-weight: 500;
        transform: scale(.84);
        margin-left: -7px;
        margin-top: 3px;
        text-align: left;
      }
      .new-limit{
        display: flex;
        align-items: center;
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 8px;
        .base{
          width: 105px;
          height: 48px;
          background: $themeColor;
          color: #FFF;
          text-align: center;
          font-family: DINPro;
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
          position: relative;
          img{
            width: 19px;
            height: 19px;
            margin-top: 5px;
          }
          .amount{
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -3px;
            .currency{
              margin-right: 2px;
            }
          }
          &.index{
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
          }
          &.second{
            margin: 0 7.5px;
          }
          &.second, &.third{
            background: #E7E7E7;
            color: #999;
            img{
              width: 16px;
              height: 16px;
              margin-top: 8px;
            }
            .amount{
              margin-top: -3px;
            }
          }
          &.third{
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
          }
          .corner-mark{
            width: 36px !important;
            height: 28px !important;
            position: absolute;
            right: -2px;
            top: -8px;
          }
        }
      }
      .loan-terms{
        margin-top: 18px;
        margin-left: 12px;
        margin-right: 12px;
        ::v-deep .select-loan-terms {
          height: 80px;
          .left{
            border: 1px solid $themeColor;
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
            padding-left: 12px;
            .title{
              color: $themeColor;
              font-size: 12px;
              font-family: Avenir;
            }
            .days{
              color: #1B3155;
              font-size: 16px;
              font-weight: 900;
              font-family: DINPro;
            }
            .detail{
              font-family: Avenir;
              font-size: 12px;
              margin-left: -24px;
            }
          }
        }
      }
      .choose-others{
        background: #ffffff;
        border-radius: 8px;
        text-align: left;
        padding: 4px 0 0 12px;
        margin-top: 14px;
        margin-left: 12px;
        margin-right: 12px;
        .title{
          width: 125px;
          height: 19px;
          font-size: 14px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #536887;
          line-height: 19px;
        }
        .staging{
          margin-top: 7px;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          li{
            display: flex;
            margin-top: 10px;
            background: #f3f5f6;
            align-items: center;
            justify-content: center;
            margin-right: 6px;
            color: #536887;
            min-width: 60px;
            height: 32px;
            border: 1px solid #c4cad5;
            border-radius: 23px;
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            color: #c4cad5;
            padding-left: 5px;
            padding-right: 5px;
            &.active {
              background: $themeColor;
              color: #ffffff;
              border: 1px solid $themeColor;
            }
            .time{
              text-align: left;
            }
            .day{
              text-align: left;
              margin-left: 2px;
            }
          }
        }
        .select-list{
          .custom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 42px;
            position: relative;
            .acc-pro-ser{
              display: flex;
              align-items: center;
              .icon{
                width: 14px;
                height: 14px;
                margin-top: -2px;
                margin-right: 2px;
              }
              .question{
                width: 14px;
                height: 14px;
              }
              ::v-deep .c-popover{
                margin-left: 2px;
                .popover-tips{
                  right: 46px;
                  top: -45px;
                }
                .wrap{
                  width: 14px;
                  height: 14px;
                  display: flex;
                  align-items: center;
                }
              }
            }
            .item-title{
              font-size: 12px;
              font-family: Avenir;
              font-weight: 500;
              color: #536887;
            }
            .right{
              display: flex;
              align-items: center;
              margin-right: 10px;
              height: 40px;
              .tips-content{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                text-align: left;
                color: #c4cad5;
                margin-right: 3px;
                color: $themeColor;
                height: 20px;
                line-height: 20px;
              }
              ::v-deep .c-money{
                text-align: right;
                display: flex;
                align-items: center;
                .monetary-unit{
                  font-size: 12px;
                  color: $themeColor;
                  transform: scale(1) !important;
                }
                .currency-num{
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  color: $themeColor;
                  margin-left: 0px;
                }
              }
              .no-selected{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                color: #c4cad5;
              }
              .available{
                color: $themeColor;
              }
              .selected{
                color: #1b3155;
              }
              .no-data{
                color: #c4cad5;
              }
              .amount-fee{
                font-family: Avenir-Medium;
                font-size: 12px;
                color: $themeColor;
                text-align: right;
              }
              .after{
                display: flex;
                align-items: center;
                &.check-acc{
                  display: flex;
                  align-items: center;
                  img{
                    width: 18px;
                    height: 18px;
                    margin-left: 5px;
                  }
                }
                .currency{
                  font-size: 12px;
                  font-family: Montserrat, Montserrat-Bold;
                  text-align: right;
                  color: #1b3155;
                  line-height: 12px;
                }
                .value{
                  height: 23px;
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  text-align: right;
                  color: #1b3155;
                  line-height: 23px;
                }

                .cal{
                  font-size: 12px;
                  color: #c4cad5;
                  font-weight: 500;
                }
              }
              .van-icon-arrow:before{
                color: #D8D8D8;
              }
            }
            &.coupon{
              .use-scene-5{
                height: 40px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                .item{
                  display: flex;
                  justify-content: center;
                  color: $themeColor;
                  justify-content: flex-end;
                  height: 20px;
                  ::v-deep .c-money{
                    .monetary-unit{
                      transform: scale(1) !important;
                    }
                  }
                }
              }
            }
          }
          .change-bank{
            height: 40px;
            font-weight: 600;
            border-radius: 12px;
            background: #ffffff;
            display: flex;
            align-items: center;
            padding: 10px 0px;
            margin-right: 10px;
            justify-content: space-between;
            &.add-bank {
              justify-content: space-between;
              .detail {
                color: $themeColor;
                font-size: 12px;
                width: 120px;
              }
              .right{
                margin-bottom: 2px;
                display: flex;
                align-items: center;
                font-family: DINAlternate-Bold;
                font-size: 12px;
                color: #FF8A51;
                text-align: left;
                font-weight: 700;
              }
            }
            .van-icon-arrow:before{
              color: #D8D8D8;
            }
            .change{
              color: #C4CAD5;
              font-size: 12px;
              height: 40px;
              display: flex;
              align-items: center;
              font-weight: normal;
            }
            .faq{
              flex: none;
              color: $themeColor;
              font-size: 12px;
              height: 40px;
              display: flex;
              align-items: center;
              text-decoration: underline;
            }
            img {
              flex: none;
              width: 40px;
              height: 40px;
              margin-right: 5px;
            }
            .detail{
              width: 196px;
              margin-bottom: 10px;
              .name{
                font-size: 12px;
                font-family: Avenir, Avenir-Medium;
                font-weight: 500;
                text-align: left;
                color: #1b3155;
                line-height: 19px;
                width: 100%;
              }
              .num{
                font-size: 14px;
                // font-family: Avenir, Avenir-Medium;
                font-weight: 700;
                text-align: left;
                color: #1b3155;
                line-height: 16px;
                width: 100%;
              }
              .disbursed{
                color: #314CFE;
                font-family: Avenir;
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                text-decoration-line: underline;
                padding-bottom: 10px;
              }
            }
            &.show-bank-acct1{
              display: block;
              height: auto;
              background: #ffffff;
              margin-left: -12px;
              margin-right: 0px;
              .wrap{
                height: 40px;
                font-weight: 600;
                border-radius: 8px;
                background: #ffffff;
                display: flex;
                align-items: center;
                padding: 10px 0px;
                margin-left: 10px;
                margin-right: 10px;
                justify-content: space-between;
              }
            }
            &.not-nin{
              padding-left: 10px;
              padding-right: 10px;
              box-sizing: border-box;
              display: block;
              height: auto;
              margin-left: -12px;
              margin-right: 0px;
              border-radius: 8px;
              border: 1px solid #919DB3;
              .disbursement{
                display: flex;
                align-items: center;
                justify-content: space-between;
                .item{
                  color: #536887;
                  font-family: Avenir;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: normal;
                }
                .right{
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  color: #C4CAD5;
                  font-family: Avenir;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: normal;
                }
              }
              .new-style{
                font-weight: 600;
                border-radius: 8px;
                border: 1px solid #E3E5E9;
                border-radius: 8px;
                position: relative;
                &.other-default{
                  margin-top: 8px;
                }
                &.choosen{
                  border-color: $themeColor;
                }
                .top{
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  img{
                    width: 97px;
                    height: 19px;
                    border-top-left-radius: 8px;
                    margin-top: -5px;
                  }
                  .disbursed{
                    color: #1B3155;
                    text-align: right;
                    font-family: Avenir;
                    font-size: 10px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                    text-decoration-line: underline;
                    margin-top: 6px;
                    margin-right: 8px;
                  }
                }
                .account{
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-top: 11px;
                  margin-bottom: 10px;
                  margin-left: 10px;
                  .right{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    color: #1B3155;
                    font-family: Avenir;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                  }
                  .left{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    color: #536887;
                    font-family: Avenir;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                    img{
                      width: 18px;
                      height: 18px;
                      margin-left: 5px;
                    }
                  }
                }
              }
              .agree{
                display: flex;
                align-items: center;
                padding: 4px 8px;
                margin-top: 5px;
                img{
                  width: 15px;
                  height: 15px;
                }
                div{
                  color: #989898;
                  font-family: Avenir;
                  font-size: 11px;
                  font-style: normal;
                  font-weight: 600;
                  span{
                    color: $themeColor;
                    text-decoration-line: underline;
                  }
                }
              }
            }
          }
          .add-bank{
            .detail{
              margin-bottom: 0;
            }
          }
        }
      }
      .repayment-schedule{
        background: #ffffff;
        text-align: left;
        padding: 8px 0 0 0px;
        margin-left: 25px;
        margin-right: 25px;
        .title{
          height: 20px;
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #919db3;
          line-height: 20px;
        }
        .custom{
            display: flex;
            justify-content: space-between;
            height: 32px;
            align-items: center;
            position: relative;
            &.interest{
              .right{
                align-items: flex-end;
                flex-direction: column-reverse;
              }
            }
            &.repayment-plan{
              .right{
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                text-align: right;
                justify-content: center;
                line-height: 17px;
                .after {
                  .value {
                    height: 14px;
                    line-height: 14px;
                  }
                }
              }
              &.multiple {
                .right {
                  flex-direction: row;
                  align-items: center;
                  font-size: 12px;
                  font-weight: 700;
                  line-height: 18px;
                  color: $themeColor;
                }
                .value {
                  margin-right: 6px;
                  font-weight: 500;
                }
              }
            }
            &.repayment-amount {
              height: auto;
              align-items: flex-start;
              margin-top: 10px;
              .item-title {
                line-height: 12px;
              }
            }
            .item-title{
              height: 19px;
              font-size: 12px;
              font-family: Avenir;
              font-weight: 500;
              text-align: left;
              color: #536887;
              line-height: 19px;
            }
            .right{
              text-align: right;
              display: flex;
              align-items: center;
              .after{
                display: flex;
                align-items: center;
                &.check-acc{
                  display: flex;
                  align-items: center;
                  img{
                    width: 18px;
                    height: 18px;
                    margin-left: 5px;
                  }
                }
                .currency{
                  font-size: 12px;
                  font-family: Montserrat, Montserrat-Bold;
                  text-align: right;
                  color: #536887;
                  line-height: 12px;
                }
                .value{
                  height: 14px;
                  line-height: 14px;
                  font-size: 12px;
                  font-family: DINAlternate, DINAlternate-Bold;
                  text-align: right;
                  color: #536887;
                  .interest{
                    margin-left: 2px;
                  }
                }

                .cal{
                  font-size: 14px;
                  color: #c4cad5;
                  font-weight: 500;
                }
              }
              .before{
                &.reduce{
                  margin-top: 6px;
                  text-decoration: line-through;
                  margin-right: 5px;
                  .currency{
                    color: #919db3;
                    font-size: 14px;
                  }
                  .value{
                    margin-left: 0px;
                  }
                }
                .currency{
                  text-decoration: line-through;
                  height: 12px;
                  font-size: 10px;
                  font-family: Montserrat, Montserrat-Bold;
                  text-align: right;
                  line-height: 12px;
                  color: #929eb4;
                }
                .value{
                  text-decoration: line-through;
                  height: 12px;
                  font-size: 10px;
                  text-align: right;
                  color: #919db3;
                  line-height: 12px;
                }
              }
            }
        }
      }
      .contract{
        height: 19px;
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: $themeColor;
        line-height: 19px;
        margin-top: 18px;
        margin-left: 25px;
        margin-right: 12px;
      }
      .withdraw{
        position: fixed;
        bottom: 55px;
        left: calc((100% - 328px)/2);
        background: #02B17B;
        background: $themeColor;
      }
      .withdraw-click{
        position: fixed;
        bottom: 55px;
        left: calc((100% - 328px)/2);
        width: 328px;
        height: 42px;
      }
      .withdraw-bg{
        position: fixed;
        bottom: 55px;
        left: calc((100% - 340px)/2);
        width: 328px;
        height: 77px;
        &.bg1{
          width: 336px;
          height: 57px;
          bottom: 50px;
        }
      }
      .bottom-text {
        font-family: Avenir-Medium;
        font-size: 10px;
        color: #000;
        font-weight: 500;
        text-align: left;
        margin-top: 15px;
        line-height: 18px;
        margin-left: 25px;
        margin-right: 25px;
        background: #F3F5F6;
        padding: 6px 12px;
        span {
          color: #1B3155;
          font-weight: 500;
        }
      }
      .bottom-text-below{
        position: fixed;
        bottom: 0px;
        left: 0px;
        font-family: Avenir-Medium;
        text-align: left;
        line-height: 20px;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #FF7400;
        font-weight: 800;
        width: 100%;
        justify-content: center;
        height: 55px;
        background-image: linear-gradient(180deg, rgba(242,243,248,0.00) 0%, rgba(242,243,248,0.80) 43%, #F2F3F8 100%);
        img{
          width: 24px;
          height: 24px;
          margin-right: 4px;
        }
      }
      .van-popup{
        background: rgba(120,120,120,0);
        .purpose-list{
          margin: 0;
          padding: 0;
          list-style: none;
          height: 428px;
          background: #ffffff;
          overflow-y: scroll;
          overflow-x: hidden;
          li {
              height: 50px;
              text-align: left;
              line-height: 50px;
              padding: 0 15px;
              font-size: 14px;
              color: #000000;
              display: flex;
              justify-content: space-between;
              align-items: center;

              &.cancel{
                background: #ebebeb;
              }
              &.van-hairline--bottom::after{
                left: -44%;
              }
              img{
                width: 20px;
                height : 20px;
              }
              .choosed{
                color: #129ffa;
              }
          }
        }
        .product-list{
          padding: 25px 0 0 0px;
          .title{
            padding-left: 30px;
          }
          .list{
            margin-top: 15px;
            overflow-y: scroll;
            height: 440px;
            .product{
              display: flex;
              justify-content: space-between;
              padding-right: 19px;
              align-items: center;
              padding-left: 30px;
              padding-top: 10px;
              padding-bottom: 10px;
              &.active{
                background: #FAFAFA;
              }
              .left{
                text-align: left;
                .item {
                  margin-bottom: 3px;
                  position: relative;
                  .day{
                    font-family: DINAlternate-Bold;
                    font-size: 24px;
                    color: #1B3155;
                    letter-spacing: 0;
                  }
                  .amount-add{
                    position: absolute;
                    left: 96px;
                    top: -8px;
                    transform: scale(.85);
                    .wrap{
                      position: relative;
                      .num{
                        background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
                        color: #ffffff;
                        width: auto;
                        white-space: nowrap;
                        padding: 2px 4px;
                        font-size: 12px;
                      }
                      .arrow{
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width: 5px 0 5px 7px;
                        border-color: transparent transparent transparent #C78900;
                        position: absolute;
                        left: 0px;
                        top: 12px;
                      }
                    }
                  }
                }
                .detail{
                  font-family: Avenir-Medium;
                  font-size: 12px;
                  color: #919DB3;
                  letter-spacing: 0;
                  margin-bottom: 2px;
                }
                .rate{
                  font-family: Avenir-Medium;
                  font-size: 12px;
                  color: #919DB3;
                  letter-spacing: 0;
                }
              }
              img{
                width: 20px;
                height: 20px;
              }
            }
          }
        }
      }
      .input_pin_pop_wrapper{
        height: calc(100% - 140px);
        display: flex;
        flex-direction: column;
        .title_box {
          flex: 0;
          text-align: left;
          .icon_close {
            width: 45px;
            height: 45px;
            margin-left: 16px;
          }
        }
        .input_pin_pop_content {
          flex: 1;
          background: #ffffff;
        }
        .title{
          color: #1B3155;
          text-align: center;
          font-family: Avenir;
          font-size: 16px;
          font-style: normal;
          font-weight: 800;
          margin-top: 12px;
          margin-bottom: 24px;
        }

        .pin-list{
          display: flex;
          padding: 0 40px;
          li {
            width: 40px;
            height: 40px;
            margin-right: 40px;
            position: relative;
            &:last-child {
              margin-right: 0px;
            }
            .hide-control{
              width: 40px;
              height: 40px;
              border-bottom: 1px solid #BFC4CA;
              display: flex;
              justify-content: center;
              align-items: center;
              position: absolute;
              top: 0;
              background: #ffffff;
              z-index: -1;
              &.control {
                z-index: 2;
              }
              .hide{
                width: 12px;
                height: 12px;
                background: #1b3155;
                border-radius: 12px;
              }
            }
            input{
              width: 40px;
              height: 40px;
              border: none;
              border-bottom: 1px solid #BFC4CA;
              font-size: 25px;
              text-align: center;
            }
          }
        }
        .try_another_way_wrapper {
          margin: 30px 30px 0 30px;
          .try_another_way_title {
            color: #1B3155;
            font-family: Avenir;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            text-align: left;
            margin-bottom: 20px;
          }
          .try_anonther {
            text-align: left;
            .item {
              color: #099BFA;
              text-align: center;
              font-family: Avenir;
              font-size: 12px;
              font-style: normal;
              font-weight: 500;
              &.item_1 {
                border-right: 1px solid #099BFA;
                padding-right: 20px;
              }
              &.item_2 {
                border-right: 1px solid #099BFA;
                padding-right: 20px;
                padding-left: 20px;
              }
              &.item_3 {
                padding-left: 20px;
              }

            }
          }
        }
      }
      .otp_verify_pop_wrapper {
        height: calc(100% - 140px);
        display: flex;
        flex-direction: column;
        .title_box {
          flex: 0;
          text-align: left;
          .icon_close {
            width: 45px;
            height: 45px;
            margin-left: 16px;
          }
        }
        .otp_verify_pop_content {
          background: #ffffff;
          flex: 1;
        }
        .title{
          color: #1B3155;
          text-align: center;
          font-family: Avenir;
          font-size: 16px;
          font-style: normal;
          font-weight: 800;
          margin-top: 12px;
          margin-bottom: 9px;
        }
        .otp_list{
          display: flex;
          margin-top: 24px;
          padding: 0 45px;
          li {
            width: 50px;
            height: 50px;
            margin-right: 21px;
            position: relative;
            input{
              width: 50px;
              height: 50px;
              border: none;
              border-radius: 8px;
              background: #F3F5F6;
              color: #1B3155;
              text-align: center;
              font-family: Avenir;
              font-size: 24px;
              font-style: normal;
              font-weight: 800;
              &:focus {
                border: 1px solid #099BFA;
                background: #fff;
                caret-color: #099BFA;
              }
            }
          }
        }
        .otp_pop_tip {
          color: #000;
          text-align: center;
          font-family: Avenir;
          font-size: 11px;
          font-style: normal;
          font-weight: 500;
        }
        .get_otp_box {
          border-radius: 8px;
          height: 45px;
          line-height: 45px;
          width: calc(100% - 36px);
          margin-left: 18px;
          margin-top: 24px;
          text-align: center;
          font-family: Avenir;
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          color: #FFF;
          &.enable {
            background: #099BFA;
          }
          &.disable {
            background: #D8D9DA;
          }
        }
      }
      .bankcard-list{
        &.van-popup{
          background: #ffffff;
          .van-popup__close-icon--top-left{
            top: 12px;
            color: #3e6fb3;
          }
        }
        .title{
          margin-top: 45px;
          font-size: 24px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #1b3155;
          margin-left: 18px;
        }
        .wrap{
          position: absolute;
          z-index: 1;
          top: 3px;
          right: 4px;
          width: 50px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          .add-bank-icon{
            width: 25px;
            height: 25px;
          }
        }
        .bank-list{
          margin-top: 26px;
          li {
            display: flex;
            align-items: center;
            padding: 0 19px;
            margin-bottom: 20px;
            .logo{
              width: 32px;
              height: 32px;
            }
            .name{
              margin-left: 17px;
              margin-right: 17px;
              width: 254px;
              .bank-name{
                color: #1B3155;
                font-family: Avenir;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 14px;
                text-align: left;
                img{
                  width: 16px;
                  height: 16px;
                }
              }
              .recommand-tips{
                color: #536887;
                font-family: Avenir;
                font-size: 10px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                text-align: left;
              }
            }
            .choose{
              width: 18px;
              height: 18px;
            }
          }
        }
      }
      .reloan-tips{
        &.van-popup{
            background-color: inherit;
            width: 304px;
            border-radius: 16px;
            box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);

            .control{
                position: relative;
                height: 275px;

                img {
                    width: 175px;
                    height: 94px;
                    position: absolute;
                    top: -25px;
                    right: 0;
                }
                .wrap{
                    background-color: #ffffff;
                    height: 100%;
                    .title{
                        font-size: 24px;
                        font-family: Avenir, Avenir-Heavy;
                        font-weight: 800;
                        text-align: left;
                        color: #1b3155;
                        line-height: 33px;
                        padding-top: 22px;
                        padding-left: 24px;
                        padding-bottom: 12px;
                    }
                    .content{
                        font-size: 16px;
                        font-family: Avenir, Avenir-Medium;
                        font-weight: 500;
                        text-align: left;
                        color: #536887;
                        line-height: 23px;
                        padding-left: 24px;
                    }
                    .confirm{
                        width: 272px;
                        height: 42px;
                        background: $themeColor;
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 18px;
                        font-family: Avenir, Avenir-Medium;
                        font-weight: 500;
                        text-align: center;
                        color: #ffffff;
                        line-height: 25px;
                        margin: 0 auto;
                        margin-top: 23px;
                    }
                }
            }
        }
      }
    }
  }
  .dialog-control{
    color: #536887;
    padding-bottom: 10px;
    width: 300px;
    ::v-deep .van-dialog__content{
      min-height: 70px;
      .tips {
        padding: 24px 13px;
        text-align: left;
      }
    }
    ::v-deep .van-button__content{
      span {
        background: $themeColor;
        display: inline-block;
        width: 90%;
        color: #ffffff;
        height: 36px;
        border-radius: 8px;
        line-height: 36px;
      }
    }
    .van-dialog__message--left {
        padding: 15px 24px;
    }
  }

  .dialog-after-withdraw{
      color: #536887;
      padding: 12px 15px;
      width: 254px;
      .van-dialog__content{
        .title{
            height: 40px;
            line-height: 40px;
            text-align: left;
            margin-bottom: 5px;
            color: #536887;
            font-weight: 700;
        }
        .tips{
            line-height: 20px;
            text-align: left;
        }
        .dia-button{
            margin-top: 15px;
            .ok{
                height: 40px;
                line-height: 40px;
                background: $themeColor;
                color: #ffffff;
                border-radius: 5px;
            }
            .cancel{
                height: 40px;
                line-height: 40px;
                background: #ffffff;
                color:  $themeColor;
                border-radius: 5px;
            }
        }
      }
      .van-dialog__content--isolated{
        min-height: 70px;
      }
      .van-dialog__message--left {
          padding: 15px 24px;
      }
    }
    .back-tips{
      .van-popup{
        .alert{
          width: 360px;
          height: 360px;
        }
        .cancel{
          width: 50px;
          height: 50px;
        }
      }
    }
    .note-tips{
      .van-popup{
          background-color: inherit;
          width: 304px;
          // box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);

          .control{
            position: relative;
            height: 225px;
            padding-top: 28px;

              img {
                  width: 175px;
                  height: 94px;
                  position: absolute;
                  top: 2px;
                  right: 0;
              }
              .wrap{
                  border-radius: 16px;
                  background-color: #ffffff;
                  height: 100%;
                  .title{
                      font-size: 24px;
                      font-family: Avenir, Avenir-Heavy;
                      font-weight: 800;
                      text-align: left;
                      color: #1b3155;
                      line-height: 33px;
                      padding-top: 36px;
                      padding-left: 24px;
                  }
                  .content{
                      font-size: 16px;
                      font-family: Avenir, Avenir-Medium;
                      font-weight: 500;
                      text-align: left;
                      color: #536887;
                      line-height: 23px;
                      padding-left: 24px;
                  }
                  .confirm{
                      width: 272px;
                      height: 42px;
                      background: $themeColor;
                      border-radius: 8px;
                      box-shadow: 0px 1px 16px 0px rgb(0 0 0 / 40%);
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 18px;
                      font-family: Avenir, Avenir-Medium;
                      font-weight: 500;
                      text-align: center;
                      color: #ffffff;
                      line-height: 25px;
                      margin: 0 auto;
                      margin-top: 23px;
                  }
              }
          }
      }
    }
  .van-popup {
    ::v-deep .van-popover__action{
      height: auto;
    }
  }

  .pc_7th_activicty_btn_box {
    .gold {
      width: 97px;
      height: 34px;
      position: fixed;
      bottom: 46px;
      left: 24px;
    }
    .gift_box {
      width: 51px;
      height: 43px;
      position: absolute;
      position: fixed;
      bottom: 48px;
      right: 38px;
    }
    .bottom-text-below {
      img {
        width: 50px;
        height: 54px;
      }
      .text {
        color: #EE4D37;
        font-family: Avenir;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
      }
    }

  }
</style>
