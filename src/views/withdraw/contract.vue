<template>
  <div class="contract">
    <c-header titleName="Legal Document"></c-header>
    <!-- <div class="contract-content" v-html="contract">
    </div> -->
    <iframe width="100%" height="auto" id="iframe" frameborder="0"></iframe>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'contract',
  computed: mapState({
    contract: state => state.contract
  }),
  data() {
    return {
      
    };
  },

  mounted() {
    let iframe = document.getElementById('iframe');
    iframe.contentWindow.document.body.innerHTML +=  this.contract;
    iframe.contentWindow.document.close();
    window.scrollTo(0, 0);
  },

  methods: {
    
  },
};
</script>

<style lang="scss" scoped>
  .contract{
    height: 100%;
  }
  #iframe{
    margin-top: 56px;
    height: 100%;
  }
</style>