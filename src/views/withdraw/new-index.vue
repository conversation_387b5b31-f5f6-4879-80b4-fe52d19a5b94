
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
    name: 'withdrawLoan',
    components: {
      baseLoan: () => import(/* webpackChunkName: "basewl" */ './page/indexBase.vue'),
      wayacredit: () => import(/* webpackChunkName: "wayacreditWithdraw" */ './page/indexWayacredit.vue'),
      ponykash: () => import(/* webpackChunkName: "ponykashWithdraw" */ './page/indexPonykash.vue'),
      indexCommon1: () => import(/* webpackChunkName: "indexCommon1" */ './page/indexCommon1.vue')
    },
    data() {
        return {
          componentTag: 'baseLoan',
          to: '',
          from: '',
        }
    },
    created() {
      const productSource = localStorage.getItem('productSource');
      console.log('productSource', productSource);
      if (productSource === 'ponykash') {
        this.componentTag = 'ponykash'
      }
      if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.withdraw) {
        this.componentTag = globalConfig.pageStyleSetting.withdraw
      }
    },
    beforeRouteEnter (to, from, next) {
      next(vm => {
        vm.to = to;
        vm.from = from;
        if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
          vm.$refs.child.beforeRouteEnter(to, from);
        }
      })
    }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
