<template>
  <div v-if="show" class="popup">
    <img class="close" src="@/assets/images/btn-popup-close.png" @click="hidePopup('close')" alt="">
    <div class="popup-content">
      <img class="img" src="@/assets/images/back-pop.png" alt="">
      <CButton @buttonClick="hidePopup('confirm')" name="Get Now"></CButton>
    </div>
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
export default {
   props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  .close {
    position: fixed;
    top: 82px;
    right: 38px;
    width: 20px;
    height: 20px;
  }
  .popup-content {
    padding: 20px;
    .img{
      width: 350px;
      height: 315px;
    }
    .c-button{
      width: 200px;
      height: 42px;
      border-radius: 42px;
      background: #ffa00b;
      font-weight: 600;
      margin-top: -27px;
    }
  }

  .close-button {
    margin-top: 10px;
  }
}
</style>
