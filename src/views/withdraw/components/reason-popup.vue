<template>
  <div class="reason-popup" :class="className" v-if="showDialog">
    <van-popup v-model="showDialog" position="bottom" :closeOnClickOverlay="false" :showConfirmButton="false"
      :showCancelButton="false">
      <img class="close-icon" src="@/assets/images/common/close.png" @click="close()" alt="">
      <div class="title" v-text="reasonObj.title"></div>
      <img class="tip-icon" src="@/assets/images/common/reason-popup.png" alt="">
      <ul class="reason-list">
        <li v-for="(res, index) in reasonObj.list" :key="res.key" @click="chooseReason(index)">
          <div class="line-content">
            <img :src="index === reasonActive ? require(`@/assets/images/${productSource}/index-paid.png`) : unPaid" alt="">
            <div class="reason" v-text="res.name"></div>
          </div>
          <img v-if="res.banner && index === reasonActive" class="banner" @click.stop="close('banner' + res.key, index)" :src="res.banner" alt="">
          <div class="input-area" v-if="res.type === 'textarea'">
            <textarea :placeholder="res.placeholder" v-model="res.textAreaContent" maxlength="1000" name="" id="" cols="30" rows="10"></textarea>
            <div class="content-num" v-text="`${res.textAreaContent.length}/1000`"></div>
          </div>
        </li>
      </ul>
      <div class="dia-button">
        <div v-if="mainButton" class="confirm" @click="mainAction()" v-text="mainButton"></div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import unPaid from '@/assets/images/un-paid.png';
import api from "@/api/interface";

export default {
  name: 'ReasonPopup',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    className: [String, Array],
    title: {
      type: String,
      default: ''
    },
    value: Boolean,
    mainButton: {
      type: String,
      default: ''
    },
    sideButton: {
      type: String,
      default: ''
    },
    productSource: {
      type: String,
      default: ''
    },
    reasonObj: {
      type: Object,
      default: function() {
        return {
          title: '',
          reasonType: '',
          list: []
        }
      }
    },
    showReason: Boolean
  },
  data() {
    return {
      unPaid,
      showDialog: false,
      reasonActive: -1
    };
  },

  methods: {
    mainAction() {
      this.$emit('mainAction');
      if (this.reasonActive === -1) {
        this.$toast('Please choose a reason');
        return
      }
      this.submitData();
    },
    // 提交数据
    submitData() {
      this.$loading();
      const chooseReason = this.reasonObj.list[this.reasonActive];
      return new Promise((resolve, reject) => {
        this.$http(api.withdraw.dropLoanApplyCase, {
          data: {
            reason: chooseReason.name,
            scene: this.reasonObj.reasonType === 'back' ? 'LOAN_GUIDE' : 'LOAN_FLOATING_GUIDE',
            remark: chooseReason.type === 'textarea' ? chooseReason.textAreaContent : ''
          }
        }).then(res => {
          this.ifSetLocalStorage();
          this.$hideLoading();
          console.log('this.reasonActive === 1', this.reasonActive === 1);
          this.$emit('close', {
            type: 'submit',
            reasonActive: this.reasonActive
          });
          resolve(res)
        }).catch(e => {
          this.$hideLoading();
          reject(e);
        });
      })
    },
    // 是否设置缓存
    ifSetLocalStorage() {
      const chooseReason = this.reasonObj.list[this.reasonActive];
      // 判断选项是否符合7天内弹利息券弹窗的条件
      const arr1 = ['The interest rate is too high', 'There is no loan demand at present'];
      // 判断选项是否符合7天内弹提额券弹窗的条件
      const arr2 = ['The loan amount is not enough'];
      if (arr1.includes(chooseReason.name)) {
        localStorage.setItem('withdraw_discount_A01', new Date().getTime());
      } else if (arr2.includes(chooseReason.name)) {
        localStorage.setItem('withdraw_increase_B01', new Date().getTime());
      }
    },
    close(type, index) {
      if (!type) {
        this.$emit('close', {
          type: type ? type : ''
        });
      } else {
        if (type.includes('banner')) {
          this.reasonActive = index;
          this.submitData();
        }
      }
    },
    chooseReason(index) {
      this.reasonActive = index;
    },
  },
  watch: {
    showReason: function(value) {
      console.log('value', value);
      this.showDialog = value;
      if (this.showDialog) {
        this.reasonActive = -1
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.reason-popup {
  color: #919db3;
  padding: 0 15px;
  width: 275px;
  padding-top: 17px;
  padding-bottom: 14px;
  background: linear-gradient(180deg,#ecffe7, #ffffff 23%);
  overflow: inherit;

  .van-popup {
    border-radius: 16px 16px 0px 0px;
    background: linear-gradient(180deg, #EDF0F6 0%, #FFF 30%);
    .title {
      font-size: 15px;
      font-family: Avenir, Avenir-Heavy;
      font-weight: 800;
      text-align: left;
      color: #009265;
      line-height: 28px;
      margin-left: 40px;
      margin-right: 40px;
      margin-top: 25px;
    }

    .close-icon{
      width: 27px;
      height: 27px;
      position: absolute;
      top: 7px;
      right: 10px;
      z-index: 10000;
    }

    .tip-icon{
      width: 70px;
      height: 56px;
      position: absolute;
      top: 52px;
      right: 28px;
      z-index: 10000;
    }

    .reason-list {
      margin-left: 8px;
      margin-top: 10px;
      margin-left: 40px;
      margin-right: 40px;
      li {
        margin-bottom: 22px;
        margin-top: 22px;
        .line-content{
          display: flex;
          align-items: center;
          justify-content: flex-start;
          img {
            width: 20px;
            height: 20px;
            margin-right: 10px;
          }

          div {
            color: #1B3155;
            font-family: Avenir;
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            text-align: left;
          }
        }
        .banner{
          width: 280px;
          height: 80px;
          border-radius: 8px;
          margin-top: 22px;
        }
        .input-area{
          position: relative;
          margin-top: 14px;
          margin-left: 30px;
          textarea{
            width: 100%;
            color: #979797;
            font-family: Helvetica;
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            height: 50px;
            border: 1px solid #C4CAD5;
            resize: none;
            padding: 5px;
          }
          .content-num{
            color: #C4CAD5;
            text-align: right;
            font-family: Helvetica;
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            position: absolute;
            right: -5px;
            bottom: 7px;
          }
        }
      }
    }

    .dia-button {
      margin-top: 10px;
      margin-left: 16px;
      margin-right: 16px;
      .confirm {
        height: 42px;
        background: $themeColor;
        border-radius: 8px;
        line-height: 42px;
        color: #ffffff;
        margin-top: 25px;
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 14px;
      }

      .cancel {
        height: 20px;
        line-height: 20px;
        background: #ffffff;
        color: $themeColor;
        border-radius: 5px;
        font-size: 14px;
      }
    }
  }

  .van-dialog__content--isolated {
    min-height: 70px;
  }

  .van-dialog__message--left {
    padding: 15px 24px;
  }
}
</style>
