<template>
  <!-- nin验证 -->
  <div class="nin-check">
    <van-popup class="popup" v-model="showNinCheck">
      <div class="title">Your loan will be disbursed to your <span style="color: #314CFE;">Goldman Bank account</span>,input NIN to confirm</div>
      <img class="close" @click="close('close')" src="@/assets/images/common/close.png" alt="">
      <!-- <div class="faq" @click="openFaq()">Where disbursed?</div> -->
      <div class="input-number">
        <div class="phone-number">Input your NIN number</div>
        <div class="wrap">
          <input v-model="validNIN" @click="report('nin_verify_popup_number_click')" type="tel" maxlength="11" @input="input($event)" placeholder="Enter your NIN number">
          <div class="length">
            <span class="index-length" v-text="`${validNIN.length}`"></span>
            /
            <span>11</span>
          </div>
        </div>
      </div>
      <div class="tips">
        Don't know your NIN?
        <span @click="goToCall">Dial *565#</span>
      </div>
      <div class="note-tip">
          <div class="title">Note</div>
          <div class="detail">After the loan disbursed,you can go to the added <span style="color: #314CFE;">Balance page</span> on <span v-text="channel"></span> and check the amount you've received.(The Balance page is only visible after successful disbursement)</div>
      </div>
      <div class="show-img">
        <img class="one" src="@/assets/images/faqGoldman/step1.png" alt="">
      </div>
      <c-button name="Confirm" @buttonClick="close('confirm')"></c-button>
    </van-popup>
  </div>
</template>

<script>
import { callPhone } from "@/assets/js/native";
import { mapState } from 'vuex';
export default {
  name: 'ninCheck',
  data() {
    return {
      showNinCheck: false,
      validNIN: ''
    };
  },

  mounted() {

  },
  computed: {
    ...mapState(['productSource', 'channel']),
  },
  methods: {
    goToCall() {
      callPhone('*565#');
    },
    showCheck() {
      this.showNinCheck = true;
    },
    input(e) {
      this.validNIN = e.target.value.replace(/\D+/g, '');
    },
    close(type) {
      this.showNinCheck = false;
      if (type === 'confirm') {
        this.report('nin_verify_popup_confirm_cilck')
      } else {
        this.report('nin_verify_popup_close_cilck')
      }
      this.$emit('close', {
        type,
        validNIN: this.validNIN
      })
    },
    hideCheck() {
      this.showNinCheck = false;
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'ninCheck',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    openFaq() {
      this.$emit('openFaq')
    }
  },
};
</script>

<style lang="scss" scoped>
.popup{
    height: 521px;
    width: 300px;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 12px;
    box-shadow: 0px 1px 10px 0px rgba(0, 54, 101, 0.06);
    overflow: hidden;
    .close{
      width: 20px;
      height: 20px;
      position: absolute;
      top: 5px;
      right: 6px;
    }
    .title{
      color: #000;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      text-align: left;
    }
    .faq{
      color: #314CFE;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-decoration-line: underline;
      text-align: left;
      margin-top: 10px;
    }
    .input-number{
      width: 100%;
      height: 45px;
      border: 1px solid;
      margin-top: 20px;
      box-sizing: border-box;
      border-color: $themeColor;
      border-radius: 8px;
      position: relative;
      .phone-number {
        color: $themeColor;
        font-family: Avenir;
        font-size: 12px;
        transform: scale(0.84);
        font-style: normal;
        font-weight: 500;
        position: absolute;
        left: 8px;
        top: -9px;
        background: #ffffff;
      }
      .wrap{
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        box-sizing: border-box;
        padding: 13px;
        input{
          font-family: Avenir;
          font-style: normal;
          font-weight: 500;
          height: 16px;
          width: 170px;
          border: none;
          color: #1B3155;
          text-align: left;
          color: #1B3155;
          font-size: 16px;
          font-weight: 800;
          &::placeholder{
            color: #C4CAD5;
            text-align: left;
            font-size: 12px;
            font-weight: normal;
          }
        }
        .length{
          color: #999;
          font-family: Avenir;
          font-size: 10px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          .index-length{
            color: #FE5B6E;
          }
        }
      }
    }
    .tips{
      color: #C4CAD5;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 18px;
      text-align: right;
      margin-top: 10px;
      span{
        color: $themeColor;
      }
    }
    .show-img{
      .one{
        width: 181px;
        height: 163px;
      }
    }
    .note-tip {
      .title {
        color: #000;
        font-family: Avenir;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
      }
      .detail {
        color: #000;
        font-family: Avenir;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
      }
    }
    .c-button{
      width: calc(100% - 32px);
      height: 42px;
      border-radius: 12px;
      position: absolute;
      bottom: 15px;
      left: 16px;
    }
  }
</style>
