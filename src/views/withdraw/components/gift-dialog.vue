<template>
  <div class="commonPopup">
    <van-popup v-model="commonPopupShow" :close-on-click-overlay="false" class="layer" :class="[giftDialogType]">
      <div class="contents">
        <div class="close"  @click="commonPopupClose">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M14.5714 12L23.4857 3.08571C24.1714 2.4 24.1714 1.2 23.4857 0.514286C22.8 -0.171429 21.6 -0.171429 20.9143 0.514286L12 9.42857L3.08571 0.514286C2.4 -0.171429 1.2 -0.171429 0.514286 0.514286C-0.171429 1.2 -0.171429 2.4 0.514286 3.08571L9.42857 12L0.514286 20.9143C-0.171429 21.6 -0.171429 22.8 0.514286 23.4857C1.2 24.1714 2.4 24.1714 3.08571 23.4857L12 14.5714L20.9143 23.4857C21.6 24.1714 22.8 24.1714 23.4857 23.4857C24.1714 22.8 24.1714 21.6 23.4857 20.9143L14.5714 12Z" fill="#F51818"/>
          </svg>
        </div>
        <div class="title" v-html="activityText"></div>
        <img src="@/assets/images/page/withdraw/gift.png" class="pic"/>
        <div class="btn" @click="sendCoupon">Click to use</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      commonPopupShow: false,
      reasonType: ''
    }
  },
  props: {
    activityText: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    giftDialogType: {
      type: String,
      default: ''
    }
  },
  watch: {
    show(val) {
      this.commonPopupShow = val;
    }
  },
  async created() {
  },
  methods: {
    commonPopupClose() {
      this.$emit('close', 'exit');
    },
    sendCoupon() {
      this.$emit('close', 'confirm');
      this.$emit('refreshLoan', this.giftDialogType);
    }
  }
}
</script>

<style scoped lang="scss">
.commonPopup {
  .layer{
    width: 304px;
    min-height: 367px;
    background: none;
  }
  .contents{
    width: 270px;
    min-height: 270px;
    background: #FFFFFF;
    border-radius: 12px;
    box-sizing: border-box;
    padding: 27px 16px 27px 16px;
    position: relative;
    margin: auto;
    .pic{
      width: 133px;
      height: 93px;
      position: absolute;
      bottom: 72px;
      left: 79px;
    }
    .title{
      font-size: 16px;
      font-family: "Noto Sans";
      color: #000;
      margin-bottom: 127px;
      text-align: left;
      overflow-wrap: break-word;
      ::v-deep span{
        color: #FF770F;
      }
    }
    .btn{
      width: 240px;
      height: 45px;
      line-height: 45px;
      box-shadow: 0px 5px 12px 0px rgba(108,169,255,0.31);
      border-radius: 34px;
      margin: 0 auto;
      font-size: 18px;
      color: #FFFFFF;
      text-align: center;
      position: absolute;
      bottom: 27px;
      left: 16px;
      background: #FF770F;
      box-shadow: 0px 13px 14px 0px rgba(255, 119, 15, 0.30);
    }
    .close{
      background-repeat: no-repeat;
      background-position: center center;
      position: absolute;
      top: 12px;
      right: 9px;
      svg{
        width: 12px;
        height: 12px;
        path{
          fill: #FF770F;
        }
      }
    }
  }
  .withdraw_increase_B01{
    .title{
      ::v-deep span{
        color: #F51818;
      }
    }
    .btn{
      background: #F51818;
      box-shadow: 0px 13px 14px 0px rgba(245, 24, 24, 0.30);
    }
    .close{
      svg{
        path{
          fill: #F51818;
        }
      }
    }
  }
}
</style>
