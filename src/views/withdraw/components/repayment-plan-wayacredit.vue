<template>
  <van-popup v-model="show" position="bottom" :style="{'height': '80%'}" :close-on-click-overlay="false">
    <div class="repayment-details">
      <div class="title">
        <img src="@/assets/images/wayacredit/back.png" alt="" @click="closePlan()" />
        <div class="text">repayment plan</div>
      </div>
      <div class="note">
        Note: The actual cost is subject to the transaction data.
      </div>
      <ul class="stage-list">
        <li v-for="(loan, index) in repaymentDetailList" :class="{'long-term': repaymentDetailList.length > 1}" :key="loan.dueTime">
          <div class="item item-term">
            <span class="part" v-text="index + 1"></span>
            <span class="sepa">/</span>
            <span class="num" v-text="repaymentDetailList.length"></span>
          </div>
          <div class="item item-amount">
            <div class="repay-amount">
              ₦{{ loan.termAmount |  singleToThousands}}
            </div>
            <div class="text">Repayment Amount</div>
          </div>
          <div class="item item-duedate">
            <div class="date-time" v-text="loan.dueDate"></div>
            <div class="text">Due Date</div>
          </div>
        </li>
      </ul>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'repaymentPlanWayacredit',
  props: {
    repaymentDetailList: {
      type: Array,
      default: function () {
        return []
      }
    },
    showRepayDetailPop: Boolean
  },
  data() {
    return {
      show: false
    };
  },

  mounted() {
    
  },

  methods: {
    closePlan() {
      this.$emit('close');
    },
  },
  watch: {
    showRepayDetailPop: function(value) {
      this.show = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.van-popup {
  background: transparent;
}
.repayment-details{
  height: 100%;
  background: rgba(30, 30, 32, 0.95);
  border-radius: 23px 23px 0 0;
  .title{
    padding: 16px 24px;
    display: flex;
    align-items: center;
    img{
      width: 35px;
      height: 35px;
      margin-right: 12px;
    }
    .text {
      font-size: 18px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: rgba(255, 255, 255, 0.90);
    }
  }
  .note{
    font-size: 12px;
    font-family: Avenir, Avenir-Heavy;
    text-align: left;
    color: #fff;
    margin-top: 7px;
    margin-left: 24px;
    margin-right: 24px;
  }
  .stage-list{
    margin-top: 16px;
    margin-left: 24px;
    margin-right: 24px;
    border-radius: 18px;
    border: 0.5px solid #FFF;
    background: rgba(40, 42, 48, 0.80);
    li {
      display: flex;
      padding: 12px 0;
      margin: 0 16px;
      border-bottom: 1px dashed rgba(255, 255, 255, 0.10);
      .item {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
      }
      .item-term {
        width: 20%;
        flex-direction: row;
        .part{
          font-family: Noto Sans;
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          color: #fff;
        }
        .sepa{
          margin-left: 3px;
          margin-right: 2px;
          color: rgba(255, 255, 255, 0.50);
          font-family: Noto Sans;
          font-size: 10px;
          font-style: normal;
          font-weight: 700;
        }
        .num {
          color: rgba(255, 255, 255, 0.50);
          font-family: Noto Sans;
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
        }
      }
      .item-amount {
        width: 50%;
        .repay-amount {
          color: #00FFAD;
          font-family: Impact;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          margin-bottom: 6px;
        }
        .text {
          color: rgba(255, 255, 255, 0.50);
          font-family: HarmonyOS Sans SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          text-align: left;
        }
      }
      .item-duedate {
        width: 30%;
        .date-time{
          font-family: HarmonyOS Sans SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          color: #fff;
          margin-bottom: 16px;
        }
        .text {
          color: rgba(255, 255, 255, 0.50);
          font-family: HarmonyOS Sans SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
    }
  }
}
</style>