<template>
  <van-popup v-model="show" position="bottom" :close-on-click-overlay="false">
    <div class="close" @click="closePlan()">
      <img :src="require('@/assets/images/cancel.png')" alt="" />
    </div>
    <div class="repayment-details">
      <div class="title">Repayment Details</div>
      <div class="note">
        <span class="note-item">Note:</span>
        <span class="content">The actual cost is subject to the transaction data.</span>
      </div>
      <div v-if="+totalInterestRelief > 0" class="total-interest-relief">
        Total interest relief {{common.thousandsToFixed(totalInterestRelief)}}
      </div>
      <div class="stage-title">
        <div class="item due-date">Due Date</div>
        <div class="item">Repayment Amount</div>
      </div>
      <ul class="stage-list">
        <li v-for="(loan, index) in repaymentDetailList" :class="{'long-term': repaymentDetailList.length > 1}" :key="loan.dueTime">
          <div class="date-detail van-hairline--right">
            <div class="stage">
              <span class="part" v-text="index + 1"></span>
              <span class="sepa">/</span>
              <span v-text="repaymentDetailList.length"></span>
            </div>
            <div class="date-time" v-text="loan.dueDate"></div>
            <div class="circle">
            </div>
            <div v-if="index == repaymentDetailList.length - 1" class="finish">Finish</div>
          </div>
          <div class="payment">
            <div class="after">
              <span class="currency">₦</span>
              <span class="num">{{ loan.outstdBal |  singleToThousands}}</span>
            </div>
            <div v-if="loan.termAmount !== loan.outstdBal" class="before">
              <span class="currency">₦</span>
              <span class="num">{{ loan.termAmount |  singleToThousands}}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'repaymentDetail',
  props: {
    totalInterestRelief: {
      type: String,
      default: ''
    },
    repaymentDetailList: {
      type: Array,
      default: function () {
        return []
      }
    },
    showRepayDetailPop: Boolean
  },
  data() {
    return {
      show: false
    };
  },

  mounted() {

  },

  methods: {
    closePlan() {
      this.$emit('close');
    },
  },
  watch: {
    showRepayDetailPop: function(value) {
      this.show = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.van-popup{
  background: rgba(120,120,120,0);
}
.close{
  text-align: left;
  img{
    width: 38px;
    height: 38px;
    margin-left: 16px;
    margin-bottom: 3px;
  }
}
.repayment-details{
  background: #ffffff;
  height: 480px;
  padding: 25px 0 20px 30px;
  overflow: scroll;
  .title{
    height: 25px;
    font-size: 18px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: left;
    color: #1b3155;
  }
  .note{
    height: 16px;
    font-size: 12px;
    font-family: Avenir, Avenir-Heavy;
    text-align: left;
    color: #1b3155;
    margin-top: 7px;
    .note-item{
      font-weight: 800;
      margin-right: 4px;
    }
  }
  .total-interest-relief {
    margin-top: 12px;
    margin-right: 27px;
    padding: 4px 13px 3px;
    font-family: Avenir;
    font-size: 12px;
    font-weight: 800;
    line-height: 16px;
    background: #E3FCEA;
    color: $themeColor;
    text-align: left;
  }
  .stage-title{
    width: 247px;
    height: 16px;
    font-size: 12px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: left;
    color: #919db3;
    display: flex;
    margin-left: 25px;
    margin-top: 24px;
    .due-date{
      margin-right: 45px;
      margin-right: 50px;
    }
  }
  .stage-list{
    margin-top: 20px;
    li {
      display: flex;
      // &.long-term{
      //   .circle {
      //     background: #1B3155 !important;
      //   }
      //   .finish{
      //     background: #f0f1f1 !important;
      //     color: #1B3155 !important;
      //   }
      // }
      .date-detail{
        position: relative;
        padding-right: 25px;
        margin-right: 25px;
        height: 90px;
        &.van-hairline--right::after{
          top: -36%;
        }
        .stage{
          font-size: 12px;
          font-family: Avenir, Avenir-Heavy;
          text-align: right;
          color: #c4cad5;
          .part{
            font-size: 18px;
            font-family: DIN Alternate, DIN Alternate-Bold;
            font-weight: 700;
            color: #1b3155;
          }
          .sepa{
            margin-left: 3px;
            margin-right: 2px;
          }
        }
        .date-time{
          height: 16px;
          font-size: 14px;
          font-family: DIN Alternate, DIN Alternate-Bold;
          font-weight: 700;
          color: #919db3;
          margin-top: 2px;
        }
        .circle{
          width: 10px;
          height: 10px;
          background: linear-gradient(151deg,  $themeColor 0%,  $themeColor 100%,  $themeColor 100%);
          border: 2px solid #ffffff;
          border-radius: 50%;
          box-shadow: 0px 1px 2px 0px rgb(26 123 199 / 25%);
          position: absolute;
          right: -7px;
          top: -2px;
        }
        .finish{
          width: 70px;
          height: 20px;
          background: #E3FCEA;
          border-radius: 10px;
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          color: $themeColor;
          position: absolute;
          bottom: -7px;
          right: -35px;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 11;
        }
      }
      .payment{
        text-align: left;
        .after, .before {
          display: inline-block;
        }
        .after{
          margin-right: 3px;
          .num{
            height: 28px;
            font-size: 24px;
            font-family: DINAlternate, DINAlternate-Bold;
            font-weight: 700;
            text-align: left;
            color: #1b3155;
            line-height: 28px;
          }
        }
        .before {
          .num {
            font-family: DINAlternate, DINAlternate-Bold;
            font-size: 14px;
            font-weight: 700;
            line-height: 17px;
            text-align: left;
            color: #C4CAD5;
            text-decoration: line-through;
          }
        }
        .currency{
          width: 19px;
          height: 15px;
          font-size: 12px;
          font-family: Montserrat, Montserrat-Bold;
          font-weight: 700;
          color: #1b3155;
          line-height: 15px;
        }
      }
    }
  }
}
</style>
