<template>
  <div v-if="show" class="popup">
    <div class="popup-content">
      <div class="tips">
        <template v-if="step === '1'">
          Withdraw to get  <span class="inter">4% interest discount</span> and increase your credit limit up to <span class="inter">₦500,000</span>
        </template>
        <template v-if="step === '2'">
          Haven't gotten the <span class="inter">4% interest discount</span> and credit limit incease, <span class="bold">do you really want to quit?</span>
        </template>
      </div>
      <div class="select" @click="choose">
        <div class="purpose">Purpose of Loan</div>
        <div class="action">
          <div v-text="purpose ? purpose : 'Select'"></div>
          <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
        </div>
      </div>
      <img class="img" src="@/assets/images/gift-pop.png" alt="">
      <CButton @buttonClick="hidePopup('confirm')" name="Get Now"></CButton>
      <div v-if="step === '2'" class="bru-close" @click="hidePopup('close')">Brutally closed</div>
      <img v-if="step === '1'" class="close" src="@/assets/images/review-btn-popup-close.png" @click="hidePopup('close')" alt="">
    </div>
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
export default {
   props: {
    show: {
      type: Boolean,
      default: false
    },
    step: {
      type: String,
      default: '1' // 弹窗1 弹窗2
    },
    purpose: {
      type: String,
      default: ''
    },
    usableLmtAmt: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    },
    choose() {
      this.$emit('choose');
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  .popup-content {
    width: calc(100% - 44px);
    border-radius: 15px;
    background: #fff;
    position: relative;
    .img{
      width: 87px;
      height: 85px;
      position: absolute;
      top: -41px;
      left: 111px;
    }
    .c-button{
      width: calc(100% - 36px);
      border-radius: 50px;
      color: #fff;
      background: #1B40FF;
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      margin-top: 20px;
      margin-bottom: 13px;
    }
    .tips{
      font-family: Avenir-Medium;
      font-size: 14px;
      color: #666;
      text-align: center;
      line-height: 21px;
      font-weight: 500;
      margin-top: 50px;
      .inter{
        color: #FF8800;
      }
      .bold{
        color: #666;
        font-weight: 800;
      }
    }
    .select{
      height: 47px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 18px;
      margin-left: 16px;
      margin-right: 16px;
      .purpose{
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
      .action{
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #1B40FF;
        text-align: right;
        font-weight: 500;
        display: flex;
        align-items: center;
      }
    }
    .bru-close{
      height: 16px;
      font-family: Avenir-Medium;
      font-size: 12px;
      color: #231815;
      font-weight: 500;
      padding-bottom: 10px;
    }
    .close {
      position: absolute;
      top: -81px;
      right: 14px;
      width: 23px;
      height: 23px;
    }
  }

  .close-button {
    margin-top: 10px;
    border-radius: 10px;
    background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
    color: #282A30;
    text-align: center;
    font-family: HarmonyOS Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
  }
}
</style>
