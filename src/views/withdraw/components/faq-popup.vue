<template>
  <Popup v-model="showFaq" position="bottom">
    <div class="close" @click="closeFaq">
      <img src="@/assets/images/opay-close.png" alt="png" />
    </div>
    <div class="faq">
      <h1>Where is the payment disbursed after I have successfully applied for a loan through NIN？</h1>
      <div class="faq-box">
        <p>Your loan will be disbursed to your <span class="type" v-text="walletName"></span> account.</p>
        <section class="faq-li">
          <div><i></i><span></span><h1><span></span>Step 1</h1><h2>Download and open <span class="type" v-text="walletName"></span> after successful release of funds </h2></div>
          <div><span></span><h1><span></span>Step 2</h1><h2>Sign in with your Phone Number of <span v-text="channel"></span></h2></div>
          <div><h1><span></span>Step 3</h1><h2>Check the Amount on the home page of <span class="type" v-text="walletName"></span> then you can withdraw </h2></div>
        </section>
      </div>
      <div class="faq-footer" @click="openWallet">
        <div class="btn">Open<img v-if="walletType" :src="require(`@/assets/images/walletType/${walletType}.png`)" alt="png"></div>
      </div>
    </div>
  </Popup>
</template>

<script>
import { Popup } from 'vant';
import { getPhone } from "@/assets/js/native";
import Clipboard from 'clipboard';
import { mapState } from 'vuex';
export default {
  name: 'faqPopup',
  computed: {
    ...mapState(['productSource', 'channel']),
  },
  data() {
    return {
      showFaq: false,
      phone: '',
      walletType: '',
      walletName: '', // 钱包名称
      walletNameObj: {
        opay: 'Opay',
        palmpay: 'PalmPay',
        fairmoney: 'FairMoney',
        branch: 'Branch'
      }
    }
  },
  components: {
    Popup
  },
  mounted() {
    this.phone = getPhone()
    console.log(this.channel, this.productSource);
  },
  methods: {
    openFaq(type) {
      this.showFaq = true;
      this.walletType = type ? type.toLocaleLowerCase() : '';
      this.walletName = type ? this.walletNameObj[this.walletType] : '';
    },
    closeFaq() {
      this.showFaq = false
    },
    openWallet() {
      if (this.walletType === 'opay') {
        location.href = 'https://play.google.com/store/apps/details?id=team.opay.pay&hl=en-gb&gl=ng'
      }
      if (this.walletType === 'palmpay') {
        location.href = 'https://play.google.com/store/apps/details?id=com.transsnet.palmpay&hl=en-gb&gl=ng'
      }
      if (this.walletType === 'fairmoney') {
        location.href = 'https://play.google.com/store/apps/details?id=ng.com.fairmoney.fairmoney&hl=en-gb&gl=ng'
      }
      if (this.walletType === 'branch') {
        location.href = 'https://play.google.com/store/apps/details?id=com.branch_international.branch.branch_demo_android&hl=en-gb&gl=ng'
      }
    },
    copy() {
      const clipboard = new Clipboard('.copy', {
        text: () => {
          return this.phone;
        }
      });
      clipboard.on('success', () => {
        this.$toast('copy successful')
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.van-popup{
  background: rgba(120,120,120,0);
  .close{
    background: #F5F5F5;
    height: 40px;
    position: relative;
    img{
      position: absolute;
      left: 20px;
      top: 15px;
      width: 20px;
      height: 20px;
    }
  }
}
.faq {
  position: relative;
  text-align: left;
  > h1 {
    font-family: Roboto-Medium;
    font-size: 16px;
    color: #1B3155;
    letter-spacing: 0;
    font-weight: 500;
    background: #F5F5F5;
    padding: 15px 10px 15px 20px;
  }
  .faq-box {
    height: 350px;
    padding: 0 20px 125px;
    background-color: #fff;
    .type{
      color: #000;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 900;
    }
    > p {
      font-family: Roboto-Medium;
      font-size: 12px;
      color: #919DB3;
      line-height: 18px;
      font-weight: 500;
      padding: 13px 0 25px;
    }
    .faq-li {
      height: 100%;
      overflow: scroll;
      div {
        padding-bottom: 25px;
        position: relative;

        > i {
          width: 25px;
          height: 30px;
          position: absolute;
          background-image: url(../../../assets/images/faq-icon.png);
          background-size: 100% 100%;
          left: 0;
          top: 4px;
        }
        > span {
          position: absolute;
          left: 42px;
          top: 18px;
          width: 1px;
          height: 100%;
          background: $themeColor;
        }
        > h1 {
          font-family: Roboto-BoldItalic;
          font-size: 18px;
          color: #1B3155;
          font-weight: 700;
          margin-left: 35px;
          margin-bottom: 10px;
          span {
            display: inline-block;
            margin-right: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: $themeColor;
          }
        }
        > h2 {
          font-family: Roboto-Medium;
          font-size: 13px;
          color: #536887;
          line-height: 19px;
          font-weight: 500;
          margin-left: 50px;
          margin-bottom: 5px;
          strong {
            color: #1B3155;
            font-weight: 500;
          }
        }
        > h3 {
          font-family: Roboto-Medium;
          font-size: 13px;
          color: #536887;
          line-height: 19px;
          font-weight: 500;
          img {
            position: relative;
            z-index: 2;
            width: 300px;
            height: 140px;
            margin: auto;
          }
          .faq-4{
            width: 290px;
            padding-left: 10px;
          }
          em {
            font-family: Roboto-Medium;
            color: #000000;
            line-height: 19px;
            font-weight: 500;
            padding-left: 50px;
            padding-right: 15px;
          }
          span {
            font-family: Roboto-Medium;
            font-size: 12px;
            color: $themeColor;
            text-align: center;
            font-weight: 500;
            border: 1px solid $themeColor;
            border-radius: 4px;
            padding: 1px 6px;
          }
        }
      }
    }
  }
  .faq-footer {
    width: 100%;
    background-color: rgba(0,0,0,.3);
    position: fixed;
    bottom: 0;
    z-index: 3;
    padding: 20px 43px;
    .btn {
      width: 76%;
      height: 42px;
      line-height: 42px;
      background: $themeColor;
      border-radius: 21px;
      text-align: center;
      font-family: Roboto-Bold;
      font-size: 18px;
      color: #FFFFFF;
      font-weight: 700;
      > img {
        display: inline-block;
        width: 57px;
        height: 23px;
        vertical-align: middle;
        border-radius: 23px;
        margin-left: 6px;
      }
    }
  }
}
</style>
