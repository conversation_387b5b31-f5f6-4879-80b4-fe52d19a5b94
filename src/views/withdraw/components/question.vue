<template>
  <div
    class="drag"
    ref="moveDiv"
    @click="startQuestion"
    @mousedown.passive="down"
    @touchstart.passive="down"
    @mousemove.passive="move"
    @touchmove.prevent="move"
    @mouseup.passive="end"
    @touchend.passive="end">
    <img src="@/assets/images/common/question.png" alt="" class="question">
  </div>
</template>
<script>
export default {
  name: 'question',
  data() {
    return {
      position: { x: 0, y: 0 },
      nx: '',
      ny: '',
      dx: '',
      dy: '',
      xPum: '',
      yPum: '',
      screenWidth: '',
      screenHeight: '',
    };
  },

  mounted() {
    this.screenWidth = window.screen.width;
    this.screenHeight = window.screen.height;
  },

  methods: {
    // 实现移动端拖拽
    down(event) {
      const _this = this
      const moveDiv = _this.$refs.moveDiv
      _this.flags = true
      var touch
      if (event.touches) {
        touch = event.touches[0]
      } else {
        touch = event
      }
      _this.position.x = touch.clientX
      _this.position.y = touch.clientY
      _this.dx = moveDiv.offsetLeft
      _this.dy = moveDiv.offsetTop
    },
    move(event) {
      const _this = this
      const moveDiv = _this.$refs.moveDiv
      if (_this.flags) {
        var touch
        if (event.touches) {
          touch = event.touches[0]
        } else {
          touch = event
        }
        _this.nx = touch.clientX - _this.position.x
        _this.ny = touch.clientY - _this.position.y
        _this.xPum = _this.dx + _this.nx
        _this.yPum = _this.dy + _this.ny
        console.log('move', _this.xPum, _this.yPum)
        moveDiv.style.left = _this.xPum + 'px'
        moveDiv.style.top = _this.yPum + 'px'
      }
    },
    //鼠标释放时候的函数
    end() {
      const _this = this
      const moveDiv = _this.$refs.moveDiv
      _this.flags = false
      // 超出边界的状态重置
      if (_this.xPum !== '' && _this.yPum !== '') {
        if (_this.xPum > _this.screenWidth - 50) {
          _this.xPum = _this.screenWidth - 50
        }
        if (_this.xPum < 60) {
          _this.xPum = 0
        }
        if (_this.yPum > _this.screenHeight - 60) {
          _this.yPum = _this.screenHeight - 70
        }
        if (_this.yPum < 60) {
          _this.yPum = 10
        }
        console.log('end', _this.xPum, _this.yPum)
        moveDiv.style.left = _this.xPum + 'px'
        moveDiv.style.top = _this.yPum + 'px'
      }
    },
    startQuestion() {
      this.$emit('question');
    }
  },
};
</script>

<style lang="scss" scoped>
  .drag {
    position: fixed;
    bottom: 246px;
    left: 11px;
    display: flex;
    align-items: center;
    z-index: 100;
    width: 50px;
      height: 56px;
    img{
      width: 50px;
      height: 56px;
    }
  }
</style>