<template>
  <div v-if="show" class="popup">
  <img src="@/assets/images/pc7thActivity/close.png" @click="hidePopup('close')" class="close" />
    <div class="juneLoan-activities">
        <img src="@/assets/images/pc7thActivity/button.png" @click="hidePopup('getMyGift')" class="button">
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'juneLoanActivities',
  computed: {
  ...mapState(['productSource']),
  },
  props: {
    show: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      count: 3,
      timer: ''
    }
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .juneLoan-activities {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 320px;
    height: 350px;
    background-image: url(@/assets/images/pc7thActivity/bg.png);
    background-size: contain;
    background-repeat: no-repeat;
  }
  .close{
    position: absolute;
    height: 57px;
    width: 20px;
    top: 94px;
    right: 50px;
  }
  .button{
    position: absolute;
    width: 240px;
    height: 48px;
    left: 50%;
    transform: translateX(-50%);
    top: 256px;
    z-index: 1;
  }
}
</style>
