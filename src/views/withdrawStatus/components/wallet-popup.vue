<template>
  <!-- 钱包提示弹框 -->
  <van-popup v-model="showWalletPopup" position="center" :close-on-click-overlay="false" class="wallet-wrap">
      <section class="wallet-content">
        <div class="faq">
          <div class="contents">
              <div class="close" @click="closeWalletPopup">
                  <img src="@/assets/images/opay-close.png" alt="png" />
              </div>
              <div class="h1">How to get cash from <span v-text="walletName"></span><img v-if="walletType" :src="require(`@/assets/images/walletType/${walletType}.png`)" alt="png"></div>
              <div class="faq-box">
                  <p>Your loan will be disbursed to your <span class="type" v-text="walletName"></span> account. </p>
                  <section class="faq-li">
                  <div><span></span><h1><span></span>Step 1</h1><h2>Download and open <span class="type" v-text="walletName"></span> after successful release of funds </h2></div>
                  <div><span></span><h1><span></span>Step 2</h1><h2>Sign in <span class="type" v-text="walletName"></span> with your Register Phone Number of <span v-text="channel"></span></h2></div>
                  <div><h1><span></span>Step 3</h1><h2>Check the Amount on the home page of <span class="type" v-text="walletName"></span> and then you can withdraw </h2><h3></h3></div>
                  </section>
                  <div class="opay_btn" @click="closeWalletPopup">OK</div>
                  <h2 @click="openWallet" v-text="`Open ${walletName}`"></h2>
              </div>
          </div>
        </div>
      </section>
  </van-popup>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'walletPopup',
  computed: {
    ...mapState(['productSource', 'channel']),
  },
  data() {
    return {
      showWalletPopup: false,
      phone: '',
      walletType: '',
      walletName: '', // 钱包名称
      walletNameObj: {
        opay: 'Opay',
        palmpay: 'PalmPay',
        fairmoney: 'FairMoney',
        branch: 'Branch'
      }
    }
  },
  mounted() {
  },
  methods: {
    openWalletPopup(type) {
      this.showWalletPopup = true;
      this.walletType = type ? type.toLocaleLowerCase() : '';
      this.walletName = type ? this.walletNameObj[this.walletType] : '';
    },
    closeWalletPopup() {
      this.showWalletPopup = false
    },
    openWallet() {
      if (this.walletType === 'opay') {
        location.href = 'https://play.google.com/store/apps/details?id=team.opay.pay&hl=en-gb&gl=ng'
      }
      if (this.walletType === 'palmpay') {
        location.href = 'https://play.google.com/store/apps/details?id=com.transsnet.palmpay&hl=en-gb&gl=ng'
      }
      if (this.walletType === 'fairmoney') {
        location.href = 'https://play.google.com/store/apps/details?id=ng.com.fairmoney.fairmoney&hl=en-gb&gl=ng'
      }
      if (this.walletType === 'branch') {
        location.href = 'https://play.google.com/store/apps/details?id=com.branch_international.branch.branch_demo_android&hl=en-gb&gl=ng'
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.wallet-wrap {
  width: 100%;
  background: none;
  top: 46%;
  .type{
    color: #000;
    font-family: Avenir;
    font-size: 12px;
    font-style: normal;
    font-weight: 900;
  }
}
.wallet-content {
    position: relative;
    .contents{
        background: #fff;
    }
    .close {
        background: #fff;
        height: 40px;
        position: relative;
        img{
            position: absolute;
            right: 20px;
            top: 15px;
            width: 15px;
            height: 15px;
        }
    }
    .faq {
    position: relative;
    padding: 40px 27.5px 0;
    .h1 {
        background-color: #fff;
        font-family: Roboto-Medium;
        font-size: 16px;
        color: #1A006B;
        font-weight: 500;
        padding: 0 25px 0px 25px;
        > img {
        width: 60px;
        height: 29px;
        margin-left: 2px;
        display: inline-block;
        vertical-align: middle;
        background-size: 100% 100%;
        }
    }
    .faq-box {
        padding: 0 25px 0px 25px;
        background-color: #fff;
        text-align: left;
        > p {
        font-family: Roboto-Medium;
        font-size: 12px;
        color: #919DB3;
        line-height: 18px;
        font-weight: 500;
        padding: 0 0 10px;
        }
        .faq-li {
        div {
            padding-bottom: 15px;
            position: relative;
            > span {
            position: absolute;
            left: 7.5px;
            top: 17.5px;
            width: 1px;
            height: 100%;
            background: $themeColor;
            }
            > h1 {
            font-family: Roboto-BoldItalic;
            font-size: 18px;
            color: #1B3155;
            font-weight: 700;
            margin-bottom: 10px;
            span {
                display: inline-block;
                margin-right: 2px;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: $themeColor;
            }
            }
            > h2 {
            font-family: Roboto-Medium;
            font-size: 13px;
            color: #536887;
            line-height: 19px;
            font-weight: 500;
            margin-left: 15px;
            margin-bottom: 5px;
            strong {
                color: #1B3155;
                font-weight: 500;
            }
            }
            > h3 {
            font-family: Roboto-Medium;
            font-size: 13px;
            color: #536887;
            line-height: 19px;
            font-weight: 500;
            img {
                position: relative;
                z-index: 2;
            }
            em {
                font-family: Roboto-Medium;
                color: #000000;
                line-height: 19px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
            }
            span {
                font-family: Roboto-Medium;
                font-size: 12px;
                color: $themeColor;
                text-align: center;
                font-weight: 500;
                border: 2px solid $themeColor;
                border-radius: 4px;
                padding: 1px 6px;
            }
            }
        }
        }
        .opay_btn {
            height: 42px;
            line-height: 42px;
            border-radius: 21px;
            font-family: Roboto-Medium;
            font-size: 16px;
            color: #FFFFFF;
            text-align: center;
            font-weight: 500;
            background: $themeColor;
            box-shadow: none;
        }
        > h2 {
          font-family: Roboto-Medium;
          font-size: 14px;
          color: $themeColor;
          text-align: center;
          font-weight: 500;
          padding: 15px 0;
          text-decoration: underline;
        }
    }
    }
}
</style>
