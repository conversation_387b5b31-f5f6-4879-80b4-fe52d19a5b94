<template>
  <div v-if="show" class="popup">
    <div class="bind-new-card">
        <img src="@/assets/images/wayacredit/bindcard.png" alt="">
        <div class="info">Bind a frequently used bank card again  is benefit to <span>improve the success rate of review</span></div>
        <CButton @buttonClick="hidePopup('bindCard')" name="Bind bank card"></CButton>
        <div @click="hidePopup()" class="close">Close</div>
    </div>
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
export default {
  name: 'bindCardWayacredit',
   props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .bind-new-card{
    border-radius: 18px;
    border: 1px solid #FFF;
    background: #26282D;
    padding: 12px 18px;
    margin-left: 30px;
    margin-right: 30px;
    img {
      width: 45px;
      height: 45px;
    }
    .info{
      color: rgba(255, 255, 255, 0.70);
      font-family: HarmonyOS Sans SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
        padding: 0 10px;
        margin-top: 9px;
        span{
          color: #00FFAD;
          font-weight: 600;
        }
    }
    .c-button{
      width: 100%;
      margin-top: 23px;
      border-radius: 10px;
      background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
      color: #282A30;
      text-align: center;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
    }
    .close{
      font-size: 14px;
      color: $themeColor;
      text-align: center;
      font-weight: 500;
      margin-top: 16px;
    }
}
}
</style>
