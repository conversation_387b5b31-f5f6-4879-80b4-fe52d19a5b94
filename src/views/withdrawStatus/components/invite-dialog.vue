<template>
  <div class="dialog" :class="className">
    <van-dialog v-model="showDialog" className="dialog" :closeOnClickOverlay="false" :showConfirmButton="false"
      :showCancelButton="false">
      <div class="control">
        <img class="bg" src="@/assets/images/inviteFriends/bg1.png" alt="" srcset="">
        <img class="money" src="@/assets/images/inviteFriends/money.png" alt="" srcset="">
        <slot name="title">
          <div class="title" v-text="title"></div>
        </slot>
        <slot name="content">
          <div class="content">
            <!-- <div class="line1">You have got a</div> -->
            <div class="line2">₦ 4,200</div>
            <div class="line3">Cash Bonus</div>
            <!-- <div class="line4">loan privilege,valid for 7 days.</div> -->
          </div>
        </slot>
        <div class="wrap">
            <slot name="button">
              <div class="dia-button">
                <div v-if="mainButton" class="confirm" @click="mainAction()" v-text="mainButton"></div>
                <div v-if="sideButton" class="cancel" @click="sideAction()" v-text="sideButton"></div>
              </div>
            </slot>
        </div>
      </div>
      <img v-if="showClose" class="close" src="@/assets/images/inviteFriends/close.png" @click="close" alt="">
    </van-dialog>
  </div>
</template>
<script>
export default {
  name: 'CDialog',
  props: {
    className: [String, Array],
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    mainButton: {
      type: String,
      default: ''
    },
    sideButton: {
      type: String,
      default: ''
    },
    show: Boolean,
    showClose: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      showDialog: false
    };
  },

  methods: {
    mainAction() {
      this.$emit('mainAction');
    },
    sideAction() {
      this.$emit('close', 'close');
    },
    close() {
      this.$emit('close');
    }
  },
  watch: {
    show: function(value) {
      this.showDialog = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog {
  background: none;
  .control{
    position: relative;
    z-index: 1;
    padding-top: 60px;
    .bg{
      position: absolute;
      width: 250px;
      height: 174px;
      left: 35px;
      top: 27px;
      z-index: 3;
    }
    .money{
      position: absolute;
      left: 83px;
      top: -2px;
      width: 153px;
      height: 87px;
      z-index: 3;
    }
  }
  .title{
    color: #FFFB00;
    text-align: center;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
    font-family: Montserrat;
    font-size: 20px;
    font-style: italic;
    font-weight: 800;
    position: absolute;
    left: 34px;
    top: 82px;
    z-index: 3;
    width: 250px;
    text-align: center;
  }
  .content{
    font-size: 12px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: left;
    color: #536887;
    line-height: 29px;
    height: auto;
    min-height: 100px;
    position: absolute;
    left: 34px;
    top: 105px;
    z-index: 3;
    width: 250px;
    .line1{
      color: #FFF;
      text-align: center;
      font-family: Montserrat;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      width: 250px;
    }
    .line2{
      color: #FFF;
      text-align: center;
      text-shadow: 0px 3px 4px rgba(0, 0, 0, 0.20);
      font-family: Montserrat;
      font-size: 32px;
      font-weight: 800;
      font-style: italic;
      width: 250px;
      margin-top: 10px;
    }
    .line3{
      color: #FFF;
      font-family: Montserrat;
      font-size: 19px;
      font-style: italic;
      font-weight: 800;
      text-align: center;
      margin-top: 10px;
    }
    .line4{
      color: #263238;
      text-align: center;
      font-family: Montserrat;
      font-size: 11px;
      font-style: normal;
      font-weight: 400;
      margin-top: 23px;
    }
  }
  .wrap{
    border-radius: 16px;
    background-color: #ffffff;
    position: relative;
    z-index: 2;
    height: auto;
    min-height: 230px;
    width: 250px;
    box-sizing: border-box;
    padding: 20px;
    margin: auto;
    .confirm{
        background: #F70 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
        line-height: 42px;
        margin: 0 auto;
        margin-top: 23px;
        position: absolute;
        bottom: 24px;
        left: 16px;
        width: 220px;
        border-radius: 42px !important;
    }
    .dia-button {
      margin-top: 10px;

      .confirm {
        height: 42px;
        background: $color;
        border-radius: 8px;
        line-height: 42px;
        color: #ffffff;
        margin-top: 25px;
        font-size: 18px;
        font-weight: 500;
      }

      .cancel {
        height: 20px;
        line-height: 20px;
        background: #ffffff;
        color: $color;
        border-radius: 5px;
        font-size: 14px;
      }
    }
  }
  .close{
    width: 16px;
    height: 16px;
    margin-top: 30px;
  }
}
</style>