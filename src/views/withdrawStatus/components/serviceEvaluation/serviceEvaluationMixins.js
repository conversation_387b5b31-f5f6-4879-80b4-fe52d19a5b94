export default {
   props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  watch: {
  },
  data() {
    return {
      evaluations: ['Very Bad', 'Dissatisfied', 'Neutral', 'Satisfied', 'Very Good'].map((name, index) => ({
        name,
        score: String(index + 1),
        type: name.toLowerCase().replace(' ', '_'),
        checkType: 'unchosen',
        img: require(`@/assets/images/serviceEvaluation/unchosen/${name.toLowerCase().replace(' ', '_')}.png`)
      })),
      productSource: ''
    }
  },
  mounted() {
    this.productSource = localStorage.getItem('productSource');
  },
  methods: {
    confirm() {
      let evaluation = '';
      this.evaluations.forEach((item) => {
        if (item.checkType === 'chosen') {
          evaluation = item;
        }
      });
      if (!evaluation) {
        this.$toast('Please rate the product experience');
      } else {
        this.$emit('confirm', evaluation);
      }
    },
    close() {
      this.$emit('close');
    },
    selectEvaluation(index) {
      this.evaluations.forEach((item, itemIndex) => {
        if (itemIndex !== index) {
          item.checkType = 'unchosen';
          item.img = require(`@/assets/images/serviceEvaluation/unchosen/${item.type}.png`);
        } else {
          item.checkType = 'chosen';
          item.img = require(`@/assets/images/serviceEvaluation/chosen/${item.type}.png`);
        }
      })
    }
  }
}
