<!-- 新客五星好评弹框 -->
<template>
  <div v-if="show" class="popup">
    <div class="popup-content">
      <img class="tips" src="@/assets/images/serviceEvaluation/tips.png" alt="">
      <div class="title">Service Evaluation</div>
      <div class="des">How would you rate the product experience of {{ productSource }}</div>
      <div class="evaluation">
        <div class="item" v-for="(evaluation, index) in evaluations" :key="evaluation.img">
          <img
            @click="selectEvaluation(index)"
            :src="evaluation.img"
            alt=""
          >
          <div class="text">{{ evaluation.name }}</div>
        </div>
      </div>
      <button class="confirm" @click="confirm">Submit</button>
      <img @click="close" src="@/assets/images/serviceEvaluation/close.png" class="close" />
    </div>
  </div>
</template>

<script>
import serviceEvaluationMixins from "./serviceEvaluationMixins.js";
export default {
  name: 'serviceEvaluation',
  mixins: [serviceEvaluationMixins],
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 25%;
  .popup-content {
    width: 304px;
    min-height: 292px;
    height: auto;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 33px 0 0 0;
    position: relative;
    box-sizing: border-box;
    .tips{
      width: 99px;
      height: 95px;
      position: absolute;
      top: -48px;
      right: 103px;
    }
    .title{
      font-family: Avenir;
      font-size: 18px;
      font-weight: 900;
      line-height: 26px;
      letter-spacing: 0px;
      text-align: center;
      color: $color;
      margin: 17px auto 0;
    }
    .des{
      font-family: Helvetica;
      font-size: 12px;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0px;
      text-align: left;
      color: #00210D;
      width: 250px;
      margin: 0 auto;
      padding-top: 2px;
      margin-top: 4px;
      text-transform: capitalize;
      text-align: center;
    }
    .evaluation{
      margin-top: 23px;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      margin-bottom: 22px;
      .item{
        margin-right: 5px;
        margin-right: 5px;
        width: 45px;
        img{
          width: 45px;
          height: 45px;
        }
        .text{
          color: #7E7E7E;
          text-align: center;
          font-family: Avenir;
          font-size: 9px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          text-transform: capitalize;
        }
      }
    }
    .confirm{
      width: 268px;
      height: 42px;
      flex-shrink: 0;
      border-radius: 21px;
      background: $themeColor;
      color: #FFF;
      text-align: center;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      line-height: normal;
      text-transform: capitalize;
      margin: 0 18px;
      border: none;
    }
     .close {
      width: 22px;
      height: 22px;
      position: absolute;
      bottom: -50px;
      right: 141px;
    }
  }
}
</style>
