<template>
  <div v-if="show" class="popup">
    <div class="christmas-activities">
        <img src="@/assets/images/halloweenActivities/close.png" @click="hidePopup('close')" class="close" />
        <img :src="require(`@/assets/images/halloweenActivities/title.png`)" class="title">
        <img :src="require(`@/assets/images/halloweenActivities/circle.png`)" class="circle">
        <img :src="require(`@/assets/images/halloweenActivities/pillar.png`)" class="pillar">
        <img src="@/assets/images/halloweenActivities/button.png" @click="hidePopup('getMyGift')" class="button">
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'halloweenActivities',
  computed: {
  ...mapState(['productSource']),
  },
  props: {
    show: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      count: 3,
      timer: ''
    }
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .close{
    position: absolute;
    height: 57px;
    width: 20px;
    top: 33px;
    left: 304px;
    z-index: 11;
  }
  .title{
    position: absolute;
    top: 40px;
    left: 0;
    width: 360px;
    height: 165px;
    z-index: 10;
  }
  .circle{
    position: absolute;
    width: 290px;
    height: 273px;
    left: 35px;
    top: 175px;
    z-index: 9;
  }
  .pillar{
    position: absolute;
    width: 190px;
    height: 52px;
    left: 78px;
    top: 449px;
  }
  .button{
    position: absolute;
    width: 289px;
    height: 53px;
    left: 35px;
    top: 466px;
  }
}
</style>
