<template>
  <div v-if="show" class="popup">
    <div class="bind-new-card">
        <img src="@/assets/images/bindcard.png" alt="">
        <div class="info">Bind a frequently used bank card again  is benefit to <span>improve the success rate of review</span></div>
        <CButton @buttonClick="hidePopup('bindCard')" name="Bind bank card"></CButton>
        <div @click="hidePopup()" class="close">Close</div>
    </div>
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
export default {
   props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .bind-new-card{
    background: #FFFFFF;
    box-shadow: 0px 2px 20px 0px rgb(0 54 101 / 5%);
    padding: 15px 15px 15px 15px;
    height: 190px;
    width: 274px;
    border-radius: 12px;
    img {
      width: 45px;
      height: 45px;
    }
    .info{
        font-size: 12px;
        color: #919DB3;
        text-align: left;
        line-height: 18px;
        padding: 0 10px;
        margin-top: 9px;
        span{
          color: #1B3155;
          font-weight: 600;
        }
    }
    .c-button{
      width: 100%;
      margin-top: 23px;
    }
    .close{
      font-size: 14px;
      color: $themeColor;
      text-align: center;
      font-weight: 500;
      margin-top: 16px;
    }
}
}
</style>
