<!-- 五星好评弹框 -->
<template>
  <div v-if="showPop" class="invite_review_pop">
    <div class="popup_content">
      <img class="close" src="@/assets/images/common/close05.png" @click="hidePopup('close')" alt="">
      <div class="main">
        <img class="top_vip" src="@/assets/images/star/top_vip.png" alt="" srcset="">
        <div class="title">Invite Review</div>
        <div class="dear">Dear VIP</div>
        <div class="content">
          We sincerely thank you for your continued support and love for our App, and sincerely invite you to evaluate our services!</div>
        <div class="tips">
          <img class="tips-img" src="@/assets/images/star/vip-tips.png" alt="" srcset="">
          <div>Can be completed in 1 minute!</div>
        </div>
        <button class="button" @click="hidePopup('confirm')" >Check</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'inviteReviewPop',
  props: {
    showPop: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {

    }
  },
  components: {
  },
  methods: {
    hidePopup(type) {
      this.$emit('closeInvitePop', type);
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.invite_review_pop {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center; 
  .popup_content {
    position: relative;
    height: 319px;
    .close {
      width: 24px;
      height: 24px;
      position: absolute;
      right: 0;
      top: -41px;
    }
    .main {
      width: 264px;
      height: 279px;
      border-radius: 12px;
      background: linear-gradient(180deg, #FFF3C7 0%, #FFF 60.73%);
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .top_vip{
      width: 105px;
      height: 77px;
      margin-top: -60px;
    }
    .title{
      color: #825716;
      text-align: center;
      font-family: Avenir;
      font-size: 20px;
      font-style: normal;
      font-weight: 800;
      margin-bottom: 4px;
    }
    .dear{
      color: #000;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      width: calc(100% - 34px);
      text-align: left;
      margin-left: 17px;
    }
    .content{
      color: #000;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      width: calc(100% - 34px);
      text-align: left;
      margin-left: 17px;
    }
    .tips{
      color: #C38C00;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      font-style:italic;
      margin-top: 14px;
      .tips-img{
        width: 21px;
        height: 18px;
      }
    }
    button{
      display: flex;
      width: 200px;
      height: 40px;
      justify-content: center;
      align-items: center;
      border-radius: 25px;
      background: #C38C00;
      border: none;
      box-shadow: 0px 4px 13px 0px rgba(195, 140, 0, 0.30);
      color: #FFF;
      text-align: center;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 800;
      line-height: 40px;
      margin-top: 9px;
    }
  }
}
</style>
