<template>
  <div v-if="show" class="popup">
    <div class="increase-limit">
        <img src="@/assets/images/common/increase-limit.png" alt="">
        <div class="info">Congratultions on your chance to <span>increase your credit limit!</span></div>
        <CButton @buttonClick="hidePopup('increaseLimit')" name="Get Now"></CButton>
        <!-- <div @click="hidePopup()" class="close">Close</div> -->
    </div>
  </div>
</template>

<script>
import CButton from '@/components/c-button.vue';
export default {
   props: {
    show: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
    }
  },
  components: {
      CButton
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .increase-limit{
    background: #FFFFFF;
    box-shadow: 0px 2px 20px 0px rgb(0 54 101 / 5%);
    padding: 15px 15px 15px 15px;
    height: 190px;
    width: 274px;
    border-radius: 12px;
    img {
      width: 100px;
      height: 100px;
      margin-top: -50px;
    }
    .info{
        color: #1B3155;
        text-align: center;
        font-family: Avenir;
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 21px;
        margin-top: 9px;
        span{
          color: #08234E;
          font-weight: 600;
        }
    }
    .c-button{
      width: 100%;
      margin-top: 23px;
    }
    .close{
      font-size: 14px;
      color: $themeColor;
      text-align: center;
      font-weight: 500;
      margin-top: 12px;
    }
}
}
</style>
