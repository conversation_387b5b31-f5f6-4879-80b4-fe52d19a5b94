<template>
  <div class="dialog" :class="className">
    <van-dialog v-model="showDialog" className="dialog" :closeOnClickOverlay="false" :showConfirmButton="false"
      :showCancelButton="false">
      <div class="top">
        <img class="vip" src="@/assets/images/facebookCommunity/vip.png" alt="">
        <img class="people" src="@/assets/images/facebookCommunity/people.png" alt="">
        <div class="club" :style="{ backgroundImage: `url(${require('@/assets/images/facebookCommunity/club-bg.png')})`}">
          Join {{ channel }} VIP Club
        </div>
        <div class="tips">to get massive benefits</div>
        <button @click="mainAction">Join us now</button>
      </div>
      <img v-if="showClose" class="close" src="@/assets/images/facebookCommunity/close.png" @click="close" alt="">
    </van-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex';
export default {
  name: 'CDialog',
  props: {
    className: [String, Array],
    show: Boolean,
    showClose: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      showDialog: false
    };
  },

  computed: {
    ...mapState(['channel', 'productSource'])
  },

  methods: {
    mainAction() {
      this.$emit('mainAction');
    },
    close() {
      this.$emit('close');
    }
  },
  watch: {
    show: function(value) {
      this.showDialog = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog {
  background: none;
  .top{
    position: relative;
    z-index: 0;
    border-radius: 16px;
    background: #36383F;
    width: 250px;
    height: 270px;
    margin: 20px auto 0;
    .vip{
      width: 227px;
      height: 95px;
      position: absolute;
      top: -10px;
      left: 11px;
      z-index: 2;
    }
    .people{
      width: 158px;
      height: 140px;
      position: absolute;
      top: 0;
      left: 42px;
      z-index: 3;
    }
    .club{
      position: absolute;
      top: 137px;
      left: 18px;
      z-index: 5;
      width: 219px;
      height: 27px;
      background-size: 219px 27px;
      background-position: 0 2px;
      background-repeat: no-repeat;
      color: #B58226;
      text-align: center;
      font-family: Montserrat;
      font-size: 15px;
      font-style: italic;
      font-weight: 700;
      line-height: 27px;
    }
    .tips{
      padding-top: 168px;
      color: #FFF;
      text-align: center;
      font-family: Montserrat;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      margin: auto;
    }
    button{
      width: 210px;
      height: 42px;
      background: linear-gradient(180deg, #EDBE6A 0%, #A87821 100%);
      border-radius: 8px;
      border: none;
      margin-top: 22px;
      color: #FFF;
      text-align: center;
      font-family: Montserrat;
      font-size: 15px;
      font-style: normal;
      font-weight: 800;
      line-height: normal;
    }
  }
  .close{
    width: 16px;
    height: 16px;
    margin-top: 30px;
  }
}
</style>