<template>
  <div class="goldman-repayment-tips">
     <van-dialog v-model="showDialog" show-cancel-button className="dialog" :showConfirmButton="false" :showCancelButton="false">
          <div class="title">Note!</div>
          <img class="close" @click="close('close')" src="@/assets/images/common/close.png" alt="">
          <div class="tips">You loan will be disbursed to your <span v-text="bankInfor"></span> in one or more installments after approval.</div>
          <img class="money" src="@/assets/images/common/money.png" alt="">
          <div class="dia-button">
              <div class="ok" @click="close('confirm')">Check Now</div>
          </div>
      </van-dialog>
  </div>
</template>

<script>
export default {
  name: 'goldmanRepaymentTips',
  props: {
    bankDetail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      showDialog: false
    };
  },
  computed: {
    bankInfor() {
      const bankDetail = this.bankDetail;
      if (bankDetail) {
        return `${bankDetail.bankName}${bankDetail.bankAcctNo ? `(${bankDetail.bankAcctNo.slice(-4, bankDetail.bankAcctNo.length)})` : ''}`
      }
      return ''
    }
  },
  mounted() {

  },

  methods: {
    showGoldmanRepaymentTips() {
      this.showDialog = true;
    },
    close(type) {
      this.showDialog = false
      this.$emit('close', type);
    }
  },
};
</script>

<style lang="scss" scoped>
.goldman-repayment-tips{
  .dialog{
    color: #536887;
    padding: 5px 27px 20px 27px;
    width: 254px;
    .van-dialog__content{
      .title{
        height: 40px;
        line-height: 40px;
        text-align: left;
        color: #536887;
        font-weight: 700;
      }
      .close{
        width: 20px;
        height: 20px;
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .tips{
          color: #536887;
          font-family: Avenir;
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          text-align: left;
          span{
            color: $themeColor;
            text-align: left;
          }
      }
      .money{
        width: 78px;
        height: 50px;
        margin-top: 10px;
      }
      .dia-button{
          margin-top: 15px;
          .ok{
            height: 40px;
            line-height: 40px;
            background: $themeColor;
            color: #ffffff;
            border-radius: 5px;
          }
      }
    }
    .van-dialog__content--isolated{
      min-height: 70px;
    }
    .van-dialog__message--left {
        padding: 15px 24px;
    }
  }
}
</style>
