<template>
  <div v-if="show" class="popup">
    <div class="juneLoan-activities">
        <img src="@/assets/images/juneActivity/close.png" @click="hidePopup('close')" class="close" />
        <img :src="require(`@/assets/images/juneActivity/title-bg.png`)" class="title">
        <img :src="require(`@/assets/images/juneActivity/circle.png`)" class="circle">
        <img src="@/assets/images/juneActivity/button.png" @click="hidePopup('getMyGift')" class="button">
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'juneLoanActivities',
  computed: {
  ...mapState(['productSource']),
  },
  props: {
    show: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      count: 3,
      timer: ''
    }
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .close{
    position: absolute;
    height: 57px;
    width: 20px;
    top: 94px;
    right: 50px;
  }
  .title{
    position: absolute;
    top: 95px;
    left: 16px;
    height: 115px;
    width: 335px;
    z-index: 2;
  }
  .circle{
    position: absolute;
    height: 310px;
    width: 320px;
    left: 20px;
    top: 210px;
    z-index: 2;
  }
  .button{
    position: absolute;
    height: 80px;
    width: 320px;
    left: 25px;
    top: 510px;
    z-index: 1;
  }
}
</style>
