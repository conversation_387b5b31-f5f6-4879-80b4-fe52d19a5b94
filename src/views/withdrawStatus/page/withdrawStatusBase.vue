<template>
    <CPage className="withdraw-status">
        <div class="wrap">
            <div class="status">
                <img :src="statusDetail.status ? require(`@/assets/images/${productSource}/success.png`) : failure" alt="">
                <div v-text="statusDetail.status ? 'Submit Successfully' : 'Withdraw Failure' "></div>
            </div>
            <div class="pass-detail">
                <div class="infor" v-if="statusDetail.status">
                    Your withdrawal is processing. We will notify you about the result by SMS in 20 minutes, please be patient.
                </div>
            </div>
        </div>
        <img class="reminder" src="@/assets/images/common/warning-banner.png" />
        <div class="banner" @click="gotoLottery()" v-if="statusDetail.status">
            <img :src="require(`@/assets/images/${productSource}/bg_withdraw_result.png`)" alt="">
        </div>
        <div class="change-bank" v-if="!statusDetail.status && statusDetail.bankDetail && deviceType !== 'AC'">
            <div class="bank-detail">
                <img class="icon" :src="backImg" />
                <div class="detail">
                    <div class="name">Disbursement Account</div>
                    <div class="num" v-text="disbursementAccount"></div>
                </div>
            </div>
            <div class="tips van-hairline--top">
                <p>The reason for your failure to repay may be: </p>
                <p>1.Bank channel maintenance;</p>
                <p>2.The system is busy, please try again later.</p>
            </div>
        </div>
        <template v-if="statusDetail.status">
            <div class="show-plan" v-if="statusDetail.repaymentDetailList.length > 0">
                <div class="title">
                    <img src="@/assets/images/schedule-ic.png" alt="">
                    <div>Repayment Schedule</div>
                </div>
                <div class="loan-detail">
                    <div class="item">
                        <div class="up">Loan Amount</div>
                        <div class="down">
                            <CMoney :currencyNum="common.thousandsToFixed(statusDetail.loanAmount)"></CMoney>
                        </div>
                    </div>
                    <div class="item">
                        <div class="up">Term</div>
                        <div class="down" v-text="statusDetail.loanTerm"></div>
                    </div>
                </div>
                <div class="term-detail-title">
                    <div class="term">Term</div>
                    <div class="due-date">Duedate</div>
                    <div class="amount">Repayment Amount</div>
                </div>
                <ul class="term-list">
                    <li v-for="(repayment, index) in statusDetail.repaymentDetailList" :key="index">
                        <div class="term">
                            <div class="index" v-text="index + 1"></div>
                            <div class="slash">/</div>
                            <div class="all-term" v-text="statusDetail.loanTerm"></div>
                        </div>
                        <div class="due-date" v-text="repayment.dueDate"></div>
                        <div class="amount">
                            <CMoney :currencyNum="common.thousandsToFixed(repayment.termAmount)"></CMoney>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="bottom-tips">The actual loan amount and interest rate are subject to final approvals</div>
            <div @click="goHome()" class="back-home fix">Go Home</div>
        </template>
        <!-- <CButton v-if="statusDetail.status" @buttonClick="gotoLoanList()" className="suc-button" name="OK"></CButton> -->
        <CButton v-else @buttonClick="tryAgain()" className="fail-button" name="Try Again"></CButton>
        <div v-if="!statusDetail.status" @click="backHomePage()" class="back-home">Back Homepage</div>
        <!-- <img v-if="statusDetail.status == false" class="advertisement" :src="advertisement" alt=""> -->
        <bindCard :show="showBindCard" @hidePopup="hideBindCard"></bindCard>
        <increaseLimit :show="showIncreaseLimit" @hidePopup="hideIncreaseLimit"></increaseLimit>
        <!-- 邀请弹窗 -->
        <inviteDialog showClose title="Invite a Friend to Get" :show="showInviteFriends" @close="closeDialog" mainButton="Invite Now" @mainAction="hideInviteFriends"></inviteDialog>
        <!-- 社群弹窗 -->
        <facebookCommunityDialog showClose :show="showFacebookCommunity" @close="closeFacebookCommunityDialog" @mainAction="hideFacebookCommunity"></facebookCommunityDialog>
        <Pc7thActivities :show="showchristmasActivities" @hidePopup="hidechristmasActivities"></Pc7thActivities>
        <!-- 六月贷款活动弹窗 -->
        <!--<JuneLoanActivities :show="showchristmasActivities" @hidePopup="hidechristmasActivities"></JuneLoanActivities>-->
        <!-- 圣诞活动弹窗 -->
        <!-- <christmasActivities :show="showchristmasActivities" @hidePopup="hidechristmasActivities"></christmasActivities> -->
        <!-- 万圣节活动弹窗 -->
        <!-- <halloweenActivities :show="showchristmasActivities" @hidePopup="hidechristmasActivities"></halloweenActivities> -->
        <!-- 钱包提示弹框 -->
        <walletPopup ref="walletPopup"></walletPopup>
        <!-- goldman还款提示 -->
        <!-- <goldmanRepaymentTips ref="goldmanRepaymentTips" :bankDetail="statusDetail.bankDetail" @close="closeTips"></goldmanRepaymentTips> -->
        <faqGoldman :setting="faqGoldmanSetting" @close="closeTips" ref="faqGoldmanDom"></faqGoldman>
        <!-- 老客邀请评价弹框 -->
        <inviteReviewPop :showPop="inviteReviewPopVisible" @closeInvitePop="closeInviteReview"></inviteReviewPop>
        <!-- 新客五星好评 -->
        <ServiceEvaluation :show="serviceEvaluationPopVisible" @close="closeServiceEvaluation" @confirm="confirmServiceEvaluation"></ServiceEvaluation>
        <!-- 帮助中心入口 -->
        <Question @question="startQuestion"></Question>
    </CPage>
</template>

<script>
import publicMixns from '../../withdrawStatus/withdrawStatusMixins.js'
export default {
    name: 'withdrawStatusBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.withdraw-status{
    &.c-page{
        margin-top: 0px;
        background: #ffffff;
        padding-left: 0;
        padding-right: 0;
    }
    .wrap{
        background: #ffffff;
        padding-left: 12px;
        padding-right: 12px;
    }
    .status {
        padding-top: 56px;
        img{
            width: 120px;
            height: 75px;
        }
        div{
            height: 25px;
            font-size: 18px;
            font-family: Avenir, Avenir-Heavy;
            font-weight: 800;
            text-align: CENTER;
            color: #1b3155;
            line-height: 25px;
            margin-top: 5px;
        }
    }
    .pass-detail{
        .application-time{
            text-align: left;
            text-align: left;
            height: 30px;
            line-height: 30px;
            color: #919db3;
        }
        .application-time-value{
            height: 50px;
            line-height: 50px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 7px;
            .item{
                font-size: 14px;
                color: #919db3;
            }
            .value{
                font-size: 16px;
                font-family: DIN Alternate, DIN Alternate-Bold;
                font-weight: 700;
                text-align: RIGHT;
                color: #1b3155;
                line-height: 18px;
            }
        }
        .loan-amount{
            display: flex;
            justify-content: space-between;
            padding: 0 7px;
            height: 50px;
            align-items: center;
            color: #919db3;
            .currency{
                font-size: 12px;
                font-family: DIN Alternate, DIN Alternate-Bold;
                font-weight: 700;
                color: #1753a4;
                line-height: 18px;
                margin-right: 5px;
            }
            .num{
                color: $themeColor;
                font-size: 15px;
                font-weight: 700;
            }
            .item-title{
                font-size: 14px;
                color: #919db3;
            }
        }
        .infor{
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: CENTER;
            color: #919db3;
            line-height: 18px;
            padding-bottom: 10px;
            span{
                color: #1b3155;
                font-weight: 700;
            }
        }
    }
    .reminder{
        width: 336px;
        margin: 10px auto;
    }
    .banner {
    position: relative;
    height: 90px;

    >img {
      display: block;
      max-width: 100%;
      width: 720px;
    }
  }
    .change-bank{
      border-radius: 12px;
      background: #ffffff;
      .bank-detail{
        height: 40px;
        font-weight: 600;
        box-shadow: 0px 1px 10px 0px rgb(0 54 101 / 6%);
        display: flex;
        align-items: center;
        padding: 10px 18px;
        margin-top: 10px;
        img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
        }
        .detail{
            text-align: left;
            width: calc(100% - 70px);
            .name{
            font-size: 14px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #1b3155;
            line-height: 19px;
            }
            .num{
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #919db3;
            line-height: 16px;
            }
        }
      }
      .tips{
        padding: 10px 18px;
        &.van-hairline--top::after{
            left: -145px;
        }
        p{
            text-align: left;
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            color: #919db3;
            line-height: 18px;
        }
      }
    }
    .suc-button{
        margin-top: 25px;
    }
    .fail-button{
        margin-top: 23px;
    }
    .succ-tips{
        background: #ffffff;
        margin-top: 9px;
        text-align: left;
        padding: 15px 18px;
        font-size: 12px;
        font-family: Avenir, Avenir-Heavy;
        text-align: left;
        .title{
            font-weight: 800;
            line-height: 33px;
            color: #1b3155;
        }
        p{
            color: #919db3;
        }
    }
    .show-plan{
        background: #ffffff;
        border-top: 1px dashed #C4CAD5;
        padding-left: 40px;
        text-align: left;
        padding: 0 40px 0 60px;
        .title{
            display: flex;
            align-items: center;
            margin-top: 17px;
            margin-left: -20px;
            img{
                width: 14px;
                height: 14px;
                margin-right: 6px;
            }
            div{
                font-family: Avenir-Medium;
                font-size: 14px;
                color: #1B3155;
                font-weight: 500;
            }
        }
        .loan-detail{
            display: flex;
            margin-top: 12px;
            .item{
                width: 140px;
                .up{
                    font-size: 12px;
                    color: #919DB3;
                    font-weight: 500;
                    margin-bottom: 4px;
                }
                .down{
                    font-size: 16px;
                    color: #333333;
                    font-weight: 700;
                    ::v-deep .c-money{
                        .currency-num{
                            font-size: 16px;
                            color: #333333;
                            font-weight: 700;
                            margin-left: 2px;
                        }
                        .monetary-unit{
                            color: #1B3155;
                            font-weight: 700;
                            transform: initial;
                        }
                    }
                }
            }
        }
        .term-detail-title{
            display: flex;
            margin-top: 20px;
            color: #1B3155;
            font-weight: 500;
        }
        .term-list{
            li {
                display: flex;
                height: 50px;
                align-items: center;
                .term{
                    height: 50px;
                    display: flex;
                    align-items: center;
                    .index{
                        color: #1B3155;
                        font-weight: 700;
                        font-size: 14px;
                        width: 10px;
                    }
                    .slash{
                        font-size: 14px;
                        width: 5px;
                        color: #C4CAD5;
                    }
                    .all-term{
                        font-size: 12px;
                        width: 10px;
                        color: #C4CAD5;
                    }
                }
                .due-date{
                    line-height: 50px;
                    height: 50px;
                    font-size: 12px;
                    font-weight: 700;
                }
                .amount{
                    height: 50px;
                    line-height: 46px;
                    ::v-deep .c-money{
                        .currency-num{
                            font-size: 16px;
                            color: #333333;
                            font-weight: 700;
                            margin-left: 2px;
                        }
                        .monetary-unit{
                            color: #1B3155;
                            font-weight: 700;
                            transform: initial;
                        }
                    }
                }
            }
        }
        .term{
            width: 50px;
            font-size: 12px;
        }
        .due-date{
            width: 95px;
            font-size: 12px;
        }
        .amount{
            width: 140px;
            font-size: 12px;
        }
    }
    .bottom-tips{
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        color: #919db3;
        line-height: 18px;
        padding-bottom: 10px;
        margin-top: 10px;
        padding-left: 12px;
        padding-right: 12px;
    }
    .back-home{
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: CENTER;
        color: $themeColor;
        margin-top: 18px;
        width: 65px;
        &.fix{
            position: fixed;
            left: calc( 50% - 32px );
            bottom: 4px;
        }
    }
    .advertisement{
        width: 325px;
        height: 83px;
        position: fixed;
        bottom: 10px;
        left: 17px;
    }
}
</style>
