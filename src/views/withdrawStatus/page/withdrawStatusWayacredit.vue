<template>
  <CPage className="withdraw-status">
    <div class="wrap">
      <div class="status">
        <img
          :src="
            statusDetail.status
              ? require(`@/assets/images/wayacredit/bg-success.png`)
              : require(`@/assets/images/wayacredit/bg-fail.png`)
          "
          alt=""
        />
        <div
          v-text="
            statusDetail.status ? 'Submit Successfully' : 'Withdraw Failure'
          "
        ></div>
      </div>
      <div class="pass-detail">
        <div class="infor" v-if="statusDetail.status">
          Your withdrawal is processing. We will notify you about the result by
          SMS in 20 minutes, please be patient.
        </div>
      </div>
    </div>

    <div
      class="change-bank"
      v-if="!statusDetail.status && statusDetail.bankDetail && deviceType !== 'AC'"
    >
      <div class="bank-detail">
        <img class="icon" :src="backImg" />
        <div class="detail">
          <div class="name">Disbursement Account</div>
          <div class="num" v-text="disbursementAccount"></div>
        </div>
      </div>
      <div class="tips van-hairline--top">
        <p>The reason for your failure to repay may be:</p>
        <p>1.Bank channel maintenance;</p>
        <p>2.The system is busy, please try again later.</p>
      </div>
    </div>
    <template v-if="statusDetail.status">
      <div class="circle-top" v-if="statusDetail.repaymentDetailList.length > 0"></div>
      <div class="show-plan" v-if="statusDetail.repaymentDetailList.length > 0">
        <div class="loan-detail">
          <div class="item item-amount">
            <div class="up">Loan Amount</div>
            <div class="down">
              <CMoney
                :currencyNum="common.thousandsToFixed(statusDetail.loanAmount)"
              ></CMoney>
            </div>
          </div>
          <div class="item item-term">
            <div class="up">Term</div>
            <div class="down" v-text="statusDetail.loanTerm"></div>
          </div>
        </div>
        <div class="plan-list">
          <div class="item-box" v-for="(repayment, index) in statusDetail.repaymentDetailList" :key="index">
            <div class="due-date">
              <img src="@/assets/images/wayacredit/ic_point.png" alt="">
              <span>{{ repayment.dueDate }}</span>
            </div>
            <div class="amount-box">
              <div class="left">
                <div class="index" v-text="index + 1"></div>
                <div class="slash">/</div>
                <div class="all-term" v-text="statusDetail.loanTerm"></div>
              </div>
              <div class="right">
                <CMoney
                  :currencyNum="common.thousandsToFixed(repayment.termAmount)"
                ></CMoney>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-tips">
        The actual loan amount and interest rate are subject to final approvals
      </div>
      <div @click="goHome()" class="back-home fix">Go Home</div>
    </template>
    <CButton
      v-else
      @buttonClick="tryAgain()"
      className="fail-button"
      name="Try Again"
    ></CButton>

    <div v-if="!statusDetail.status" @click="backHomePage()" class="back-home fix">
      Back Homepage
    </div>
    <bindCardWayacredit :show="showBindCard" @hidePopup="hideBindCard"></bindCardWayacredit>
    <increaseLimit :show="showIncreaseLimit" @hidePopup="hideIncreaseLimit"></increaseLimit>
    <!-- 钱包提示弹框 -->
    <walletPopup ref="walletPopup"></walletPopup>
    <!-- goldman还款提示 -->
    <goldmanRepaymentTips ref="goldmanRepaymentTips" :bankDetail="statusDetail.bankDetail" @close="closeTips"></goldmanRepaymentTips>
    <!-- 老客邀请评价弹框 -->
    <inviteReviewPop :showPop="inviteReviewPopVisible" @closeInvitePop="closeInviteReview"></inviteReviewPop>
    <!-- 新客五星好评 -->
    <ServiceEvaluation :show="serviceEvaluationPopVisible" @close="closeServiceEvaluation" @confirm="confirmServiceEvaluation"></ServiceEvaluation>
    <!-- 帮助中心入口 -->
    <Question @question="startQuestion"></Question>
  </CPage>
</template>

<script>
import publicMixns from "../../withdrawStatus/withdrawStatusMixins.js";
export default {
  name: "withdrawStatusWayacredit",
  mixins: [publicMixns],
  data() {
    return {};
  },
  mounted() {
    // this.statusDetail.loanAmount = 100000
    // this.statusDetail.loanTerm = 4
    // this.statusDetail.repaymentDetailList = [
    //   {dueDate: '2023-04-22', termAmount: 20000},
    //   {dueDate: '2023-04-22', termAmount: 20000},
    //   {dueDate: '2023-04-22', termAmount: 20000}
    // ]
  },
};
</script>

<style lang="scss" scoped>
.withdraw-status {
  background: #1E1E20;
  padding-bottom: 112px;
  &.c-page {
    margin-top: 0px;
    padding-left: 0;
    padding-right: 0;
  }
  .wrap {
    padding-left: 12px;
    padding-right: 12px;
  }
  .status {
    padding-top: 56px;
    img {
      width: 88px;
      height: 88px;
    }
    div {
      height: 25px;
      font-size: 18px;
      font-family: Avenir, Avenir-Heavy;
      font-weight: 800;
      text-align: CENTER;
      color: #fff;
      line-height: 25px;
      margin-top: 5px;
    }
  }
  .pass-detail {
    .application-time {
      text-align: left;
      text-align: left;
      height: 30px;
      line-height: 30px;
      color: rgba(255, 255, 255, 0.70);
    }
    .application-time-value {
      height: 50px;
      line-height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 7px;
      .item {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.70);
      }
      .value {
        font-size: 16px;
        font-family: DIN Alternate, DIN Alternate-Bold;
        font-weight: 700;
        text-align: RIGHT;
        color: #fff;
        line-height: 18px;
      }
    }
    .loan-amount {
      display: flex;
      justify-content: space-between;
      padding: 0 7px;
      height: 50px;
      align-items: center;
      color: #fff;
      .currency {
        font-size: 12px;
        font-family: DIN Alternate, DIN Alternate-Bold;
        font-weight: 700;
        color: #00FFAD;
        line-height: 18px;
        margin-right: 5px;
      }
      .num {
        color: $themeColor;
        font-size: 15px;
        font-weight: 700;
      }
      .item-title {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.70);
      }
    }
    .infor {
      font-size: 12px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: CENTER;
      color: rgba(255, 255, 255, 0.70);
      line-height: 18px;
      padding-bottom: 10px;
      span {
        color: #00FFAD;
        font-weight: 700;
      }
    }
  }
  .change-bank {
    border-radius: 12px;
    background: #ffffff;
    .bank-detail {
      height: 40px;
      font-weight: 600;
      box-shadow: 0px 1px 10px 0px rgb(0 54 101 / 6%);
      display: flex;
      align-items: center;
      padding: 10px 18px;
      margin-top: 10px;
      img {
        width: 40px;
        height: 40px;
        margin-right: 10px;
      }
      .detail {
        text-align: left;
        width: calc(100% - 70px);
        .name {
          font-size: 14px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #00FFAD;
          line-height: 19px;
        }
        .num {
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #00FFAD;
          line-height: 16px;
        }
      }
    }
    .tips {
      padding: 10px 18px;
      &.van-hairline--top::after {
        left: -145px;
      }
      p {
        text-align: left;
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.70);
        line-height: 18px;
      }
    }
  }
  .suc-button {
    margin-top: 25px;
  }
  .fail-button {
    margin-top: 23px;
    color: #282A30;
    border-radius: 10px;
    background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
    width: calc(100% - 44px);
  }
  .succ-tips {
    background: #ffffff;
    margin-top: 9px;
    text-align: left;
    padding: 15px 18px;
    font-size: 12px;
    font-family: Avenir, Avenir-Heavy;
    text-align: left;
    .title {
      font-weight: 800;
      line-height: 33px;
      color: #00FFAD;
    }
    p {
      color: rgba(255, 255, 255, 0.70);
    }
  }

  .circle-top {
    height: 28px;
    border-radius: 30px;
    border: 6px solid #E4E4E4;
    background: #1E1E20;
    margin-left: 22px;
    margin-right: 22px;
  }
  .show-plan {
    border-radius: 0px 0px 18px 18px;
    border: 0.2px solid rgba(255, 255, 255, 0.20);
    background: rgba(40, 42, 48, 1);
    text-align: left;
    margin-left: 32px;
    margin-right: 32px;
    margin-top: -20px;
    .loan-detail {
      display: flex;
      margin-top: 12px;
      padding-left: 20px;
      padding-right: 20px;
      .item {
        .up {
          color: #FFF;
          font-family: Noto Sans;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          margin-bottom: 4px;
        }
        .down {
          font-size: 24px;
          color: #00FFAD;
          font-weight: 700;
          ::v-deep .c-money {
            .currency-num {
              font-size: 24px;
              color: #00FFAD;
              font-weight: 700;
              margin-left: 2px;
            }
            .monetary-unit {
              color: #00FFAD;
              font-weight: 700;
              transform: initial;
              font-size: 24px;
            }
          }
        }
      }
      .item-amount {
        width: 60%;
      }
      .item-term {
        .down {
          padding-top: 2px;
        }
      }
    }
    .plan-list {
      padding-left: 20px;
      padding-right: 20px;
      margin-top: 30px;
      .item-box {
        border-bottom: 1px dashed rgba(255, 255, 255, 0.10);
        padding-bottom: 16px;
        .due-date {
          margin-bottom: 12px;
          margin-top: 16px;
          img {
            width: 10px;
            height: 10px;
            margin-right: 4px;
          }
          span {
            color: rgba(255, 255, 255, 0.50);
            font-family: Noto Sans;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
          }
        }
        .amount-box {
          display: flex;
          align-items: center;
          .left {
            display: flex;
            width: 60%;
            padding-left: 16px;
            box-sizing: border-box;
            .index {
              color: #FFF;
              font-family: Noto Sans;
              font-size: 14px;
              font-style: normal;
              font-weight: 700;
            }
            .slash {
              color: rgba(255, 255, 255, 0.50);
              font-family: Noto Sans;
              font-size: 10px;
              font-style: normal;
              font-weight: 700;
              transform: scale(0.84);
            }
            .all-term {
              color: rgba(255, 255, 255, 0.50);
              font-family: Noto Sans;
              font-size: 14px;
              font-style: normal;
              font-weight: 700;
            }
          }
          .right {
            ::v-deep .c-money .monetary-unit, ::v-deep .c-money .currency-num {
              color: #00FFAD;
              font-family: Impact;
              font-size: 18px;
              font-style: normal;
              font-weight: 900;
              transform: scale(1)
            }
          }
        }
      }
      .item-box:last-child {
        border-bottom: none;
      }
    }
    .term {
      width: 50px;
      font-size: 12px;
    }
    .due-date {
      width: 95px;
      font-size: 12px;
      color: #fff;
    }
    .amount {
      width: 140px;
      font-size: 12px;
    }
  }
  .bottom-tips {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    color: rgba(255, 255, 255, 0.70);
    line-height: 18px;
    padding-bottom: 10px;
    margin-top: 10px;
    padding-left: 12px;
    padding-right: 12px;
  }
  .back-home {
    width: 100%;
    font-size: 14px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: CENTER;
    color: $themeColor;
    margin-top: 18px;
    width: 65px;
    &.fix {
      position: fixed;
      left: calc(50% - 32px);
      bottom: 30px;
    }
  }
  .advertisement {
    width: 325px;
    height: 83px;
    position: fixed;
    bottom: 10px;
    left: 17px;
  }
}
</style>
