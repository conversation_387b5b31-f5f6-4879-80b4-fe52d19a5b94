import { mapState } from 'vuex';
import { judgeClient } from '@/assets/js/common';
import Clipboard from 'clipboard';
import api from "@/api/interface";
import { gotoHomeActivity, gotoLoanList, gotoGooglePlay, openAppStore,  getPhone, getOsVersionCode, gotoBalanceTab } from "@/assets/js/native";
import advertisement from '@/assets/images/advertisement.png';
import backImg from '@/assets/images/UBA.png';
import CPage from '@/components/c-page.vue';
import CButton from '@/components/c-button.vue';
import bindCard from './components/bind-card.vue';
import bindCardWayacredit from './components/bind-card-wayacredit.vue';
import failure from '@/assets/images/failure.png';
import CMoney from '@/components/c-money.vue';
import faqGoldman from '@/components/faq-goldman.vue';
import increaseLimit from './components/increase-limit-pop.vue';
import inviteDialog from './components/invite-dialog.vue';
import facebookCommunityDialog from './components/facebook-community-dialog.vue';
import christmasActivities from './components/christmas-activities.vue';
import halloweenActivities from './components/halloween-activities.vue';
import JuneLoanActivities from './components/JuneLoanActivities.vue';
import Pc7thActivities from './components/Pc7thActivities.vue';
import walletPopup from './components/wallet-popup.vue';
import goldmanRepaymentTips from './components/goldman-repayment-tips.vue';
import Question from "@/components/question.vue";
import ServiceEvaluation from "./components/serviceEvaluation/serviceEvaluation.vue";
import inviteReviewPop from './components/inviteReviewPop.vue';


export default {
    name: 'withdrawStatus',
    computed: {
    ...mapState(['coupon', 'deviceType', 'userInfor', 'addHeader', 'channel', 'productSource', 'channelPackageName', 'notComplaintArr', 'supportedFeatures']),
        disbursementAccount() {
        const bankDetail = this.statusDetail.bankDetail;
        if (bankDetail) {
          return bankDetail.bankName + (bankDetail.bankAcctNo ? `(${bankDetail.bankAcctNo.slice(-4, bankDetail.bankAcctNo.length)})` : '')
        }
        return ''
      },
    },
    data() {
        return {
            backImg,
            failure,
            advertisement,
            statusDetail: {
                status: true,
                loanAmount: 0,
                loanTerm: 0,
                dueDate: '',
                bankDetail: {
                    bankType: '',
                    bankName: '',
                    cardNum: ''
                },
                repaymentDetailList: []
            },
            time: '',
            showBindCard: false,
            showIncreaseLimit: false, // 增信弹框
            showWalletPopup: false,
            phone: '',
            hidden: false,
            fromName: '',
            device: '',
            androidVersion: 0,
            showInviteFriends: false,
            showFacebookCommunity: false, // 社群弹窗
            inviteUrl: '',
            showchristmasActivities: false,
            activityUrl: '', // 活动链接
            faqGoldmanSetting: {},
            inviteReviewPopVisible: false,
            serviceEvaluationPopVisible: false,
        };
    },

    components: {
        CPage,
        CButton,
        CMoney,
        bindCard,
        Question,
        faqGoldman,
        walletPopup,
        inviteDialog,
        increaseLimit,
        inviteReviewPop,
        ServiceEvaluation,
        bindCardWayacredit,
        christmasActivities,
        halloweenActivities,
        JuneLoanActivities,
        Pc7thActivities,
        goldmanRepaymentTips,
        facebookCommunityDialog
    },
    mounted() {
        let vm = this;
        // vm.openFaqGoldman();
        vm.device = judgeClient();
        vm.beforeRouteEnter(vm.$parent.to, vm.$parent.from);
        vm.androidVersion = getOsVersionCode();
        console.log('vm.$route.query.data', vm.$route.query.data)
        if (vm.$route.query.data) {
            vm.statusDetail = JSON.parse(vm.$route.query.data);
            localStorage.setItem('saveStatusDetail', JSON.stringify(vm.statusDetail));
        }
        if (localStorage.getItem('surveyActivity')) {
            vm.$toast('The credit limit is increased by ₦20.')
        }
        // 本地缓存，通过首页进入可以展示
        if (!vm.$route.query.data) {
            // 从首页进入。
            const statusDetail = localStorage.getItem('saveStatusDetail');
            console.log('statusDetail', statusDetail)
            if (statusDetail) {
                vm.statusDetail = JSON.parse(statusDetail);
            }
        }
        // 弹窗初始化
        this.initPop();
        vm.report('withdraw_status_view');
        // vm.showFacebookCommunity = true;
        // vm.time = dateFormat(new Date(), 'dd/MM/yyyy');
    },
    beforeDestroy() {
      localStorage.removeItem('surveyActivity')
    },
    destroyed() {
      localStorage.removeItem('surveyActivity')
    },
    methods: {
        initPop() {
            const vm = this;
            vm.starsPopup().then(res => {
            const isWalletWithdrawalUser = localStorage.getItem('userAccountType') === 'isWalletWithdrawalUser';
            // vm.inviteReviewPopVisible = true;
            // vm.serviceEvaluationPopVisible = true;
            // 基础弹窗优先级：Opay优先>goldman bank放款引导弹窗>五星好评>邀请好友>绑卡>增信上传。
            // 变更的优先级以需求为准
            // 只有从提现页跳转的才显示弹窗
            if (vm.fromName === 'index') {
                // 当前活动为PC七周年活动，优先级最高
                const activityStatus = localStorage.getItem('activityStatus');
                vm.activityUrl = res.activityUrl;
                if (vm.productSource === 'palmcredit' && activityStatus === 'Valid' && vm.activityUrl) {
                    vm.showchristmasActivities = true;
                    vm.report('submit_result_christmas_pop_view');
                    return;
                }
                 // 老客五星好评
                if (res.showStarsPopup) {
                    vm.inviteReviewPopVisible = res.showStarsPopup;
                    return
                }
                // 新客五星好评
                if (res.newCustShowStarsPopup) {
                    vm.report('service_evaluation_pop_view');
                    vm.serviceEvaluationPopVisible = res.newCustShowStarsPopup;
                    return
                }
                // facebook社群弹窗
                if (res.emailSubmitUrl) {
                    vm.showFacebookCommunity = true;
                    return;
                }
                // 邀请好友
                if (res.inviteUrl) {
                    vm.inviteUrl = res.inviteUrl;
                    vm.showInviteFriends = true;
                    return;
                }
                // 钱包还款引导
                const bankDetail = vm.statusDetail.bankDetail;
                if (isWalletWithdrawalUser && bankDetail && bankDetail.bankName) {
                    vm.phone = getPhone()
                    this.$refs.walletPopup.openWalletPopup(bankDetail.bankName);
                    return
                }
                // goldman bank放款引导弹窗
                console.log('bankDetail', bankDetail)
                if (bankDetail && bankDetail.bankCode === '090574') {
                    vm.openFaqGoldman();
                    vm.report('goldman_disburse_popup_view');
                    return;
                }
                // 绑卡
                if (!res.showStarsPopup) {
                    if (!isWalletWithdrawalUser) { // 钱包提现用户不展示引导绑卡
                        vm.showBindCard = !res.showStarsPopup;
                        vm.report('bind_card_activity');
                        return;
                    }
                }
                // 增信
                if (vm.deviceType !== 'AC' && vm.fromName !== 'complaint' && vm.fromName !== 'increaseLimit') { // AC不需要这个逻辑。IOS暂时不跳转
                    if (res.afterWithDrawToCollectPageFlag === 'Y' && vm.androidVersion > 28) { // 采集征信
                        vm.showIncreaseLimit = true;
                        vm.report('increase_limit_pop_view');
                        return;
                    }
                }
            }
        });
        },
        // 返回首页
        goHome() {
            this.report('go_home_click');
            if (this.fromName === 'bookNextLoan') {
                this.report('bookloan_submit_success_ok_click');
            }
            gotoHomeActivity('withdrawStatus');
        },
        backHomePage() {
            if (this.fromName === 'bookNextLoan') {
                this.report('bookloan_submit_success_ok_click');
            }
            gotoHomeActivity('withdrawStatus');
        },
        // 跳转到借款列表
        gotoLoanList() {
            gotoHomeActivity('withdrawStatus');
            gotoLoanList();
        },
        tryAgain() {
            this.$router.go(-1);
        },
        // 跳转绑卡中台的逻辑
        bindCard() {
            let vm = this;
            let obj = {};
            vm.report('bind_bank_card_click');
            if (vm.productSource === 'palmcredit') {
                obj.businessChannel = 'palmcreditnew';
            }
            // 缓存绑卡标识。用于绑卡结果页判断跳转
            localStorage.setItem('bindCardType', 'widthdrawStatusAddBankCard');
            return  new Promise((resolve, reject) => {
                vm.$http(api.commonBlc.routeRedirect, {
                "data":{
                    "scene":"4",
                    ...obj
                } // 目前是写死的
                }).then(res => {
                // 跳转绑卡中台的地址
                localStorage.setItem('statusDetail', JSON.stringify(vm.statusDetail))
                location.href = res.redirectUrl;
                resolve();
                }).catch(e => {
                    // 后续这里报错，需要退出，返回到首页。
                    // vm.$toast({
                    //   message: `${e.code}:${e.msg}`
                    // });
                    reject(e);
                })
            })
        },
        // 用户代扣Key≥2时，弹出弹窗
        starsPopup() {
            let vm = this;
            return  new Promise((resolve, reject) => {
                vm.$http(api.commonBlc.starsPopup, {
                "data":{
                    scene: 'withdrawStatusSwitch',
                    inviteActivityName:"invitedFriendsActivity", // 获取邀请的链接
                    businessSource: 'nc-from-h5' // 修复IOS原生请求头被覆盖问题。
                }
                }).then(res => {
                resolve(res);
                }).catch(e => {
                reject(e);
                })
            })
        },
        // 保存用户分数
        saveStarsScore(score) {
            let vm = this;
            return  new Promise((resolve, reject) => {
                vm.$http(api.commonBlc.saveStarsScore, {
                    "data":{
                        score: score
                    }
                }).then(res => {
                resolve(res);
                }).catch(e => {
                reject(e);
                })
            })
        },
        report(eventName) {
            let vm = this;
            vm.$store.dispatch('reportEvent', {
                page: 'withdrawStatus',
                eventName: eventName,
                eventData: {
                event_time: Date.now()
                }
            });
        },
        jumpAPPStore(score) {
            const vm = this;
            if (score <= 3) {
                vm.$hideLoading();
                // 新包关闭客诉功能
                // 新包客诉功能的关闭
                if (!this.notComplaintArr.includes(vm.productSource)) {
                    vm.$router.push({ path: '/complaint'})
                }
            } else {
                vm.hidden = false;
                document.addEventListener('visibilitychange', vm.visibilityFun, false);
                if (vm.device === 'Android') {
                    // 打开对应的google play页面
                    gotoGooglePlay(vm.channelPackageName);
                } else {
                    openAppStore();
                }
                // 如果没有打开，则调用以下逻辑。
                setTimeout(() => {
                    vm.$hideLoading();
                    if (!vm.hidden) {
                        if (vm.device === 'Android') {
                            location.href = `https://play.google.com/store/apps/details?id=${vm.channelPackageName}`
                        }
                    } else {
                        document.removeEventListener('visibilitychange', vm.visibilityFun, false);
                    }
                }, 2000);
            }
        },
        hideBindCard(type) {
            if (type === 'bindCard') {
                this.report('bind_card_click')
                this.bindCard();
            } else {
                this.report('close_click')
            }
            this.showBindCard = false;
        },
        hideIncreaseLimit(type) {
            if (type === 'increaseLimit') {
                this.report('increase_limit_get_now_click');
                this.$router.push({ path: '/increaseLimit'})
            } else {
                this.report('increase_limit_close_click');
            }
            this.showIncreaseLimit = false;
        },
        hideInviteFriends() {
            this.showInviteFriends = false;
            this.report('invite_join_now_click');
            gotoHomeActivity();
            // if (type === 'joinNow') {
            //     this.report('invite_join_now_click');
            //     // window.location.href = this.inviteUrl;
            //     gotoHomeActivity();
            // } else {
            //     this.report('invite_close_click');
            // }
        },
        closeDialog() {
            this.showInviteFriends = false;
            this.report('invite_close_click');
        },
        hidechristmasActivities(type) {
            this.showchristmasActivities = false;
            if (type === 'getMyGift') {
                this.report('submit_result_christmas_pop_get_click');
                this.backHomePage();
            } else {
                this.report('submit_result_christmas_pop_close_click');
            }
        },
        visibilityFun() {
            let vm = this;
            if (document.hidden) {
                vm.hidden = true;
            } else {
                vm.hidden = false;
            }
        },
        closeFaq() {
            this.showWalletPopup = false
        },
        copy() {
            const clipboard = new Clipboard('.copy', {
                text: () => {
                return this.phone;
                }
            });
            clipboard.on('success', () => {
                this.$toast('copy successful')
            });
        },
        faq() {
            this.showWalletPopup = false
            window.location.href = process.env.VUE_APP_FAQ_URL
        },
        openOpay() {
            location.href = 'https://play.google.com/store/apps/details?id=team.opay.pay&hl=en-gb&gl=ng'
        },
        beforeRouteEnter (to, from) {
            console.log('from', from)
            this.fromName = from.name;
            if (this.fromName === 'bookNextLoan') {
                this.report('bookloan_submit_success_activity');
            }
        },
        gotoLottery() {//跳转到抽奖
            //todo:需要后台给到抽奖链接（暂不支持），跳转首页
            this.report('go_home_click');
            gotoHomeActivity('withdrawStatus');
        },
        closeTips(type) {
            if (type === 'confirm') {
                this.report('goldman_disburse_comfirm_click')
                if (this.supportedFeatures.goldbank) {
                    gotoBalanceTab()
                }
            } else {
                this.report('goldman_disburse_close_click')
            }
        },
        openFaqGoldman() {
            const chooseBankAcct = this.statusDetail.bankDetail;
            let account = ''
            if (chooseBankAcct) {
                account = chooseBankAcct.bankAcctNo ? `(${chooseBankAcct.bankAcctNo.slice(-4, chooseBankAcct.bankAcctNo.length)})` : ''
            }
            this.faqGoldmanSetting = {
                title: 'How to get cash from Goldman',
                account: account,
                button: 'Go to Goldman Balance'
            };
            this.$refs.faqGoldmanDom.openFaq()
        },
        closeFacebookCommunityDialog() {
            this.showFacebookCommunity = false;
        },
        hideFacebookCommunity() {
            this.showFacebookCommunity = false;
            this.backHomePage();
        },
        startQuestion() {
            this.report('withdraw_result_get_help_click');
            this.$router.push({
                path: '/helpCenter'
            })
        },
        closeServiceEvaluation() {
            this.serviceEvaluationPopVisible = false;
            this.report('service_evaluation_pop_close_click');
        },
        confirmServiceEvaluation(evaluation) {
            this.serviceEvaluationPopVisible = false;
            this.report('service_evaluation_pop_submit_click');
            this.saveStarsScore(evaluation.score).then(() => {
              // 若用户选择Satisfied或Very Good，则提交后跳转邀请评价页
              const inTypeArr = ['satisfied', 'very_good'];
              // 若用户选择Very Bad或Dissatisfied或Neutral，则提交后跳转帮助中心页面
              const outTypeArr = ['very_bad', 'dissatisfied', 'neutral'];
              if (inTypeArr.includes(evaluation.type)) {
                this.$router.push({
                  path: '/inviteReview'
                })
              } else if (outTypeArr.includes(evaluation.type)) { // 其他的跳转帮助中心页面
                this.$router.push({
                  path: '/helpCenter'
                })
              }
            }).catch(e => {
              console.log(e);
            })
        },
        closeInviteReview(type) {
            if (type === 'confirm') {
                this.report('withdrawal_results_review_click');
                this.$router.push({
                path: '/inviteReview'
                })
            } else {
                this.inviteReviewPopVisible = false
            }
        }
    }
};
