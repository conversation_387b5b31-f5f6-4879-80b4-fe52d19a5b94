
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
    name: 'withdrawStatus',
    components: {
      baseWS: () => import(/* webpackChunkName: "baseWS" */ './page/withdrawStatusBase.vue'),
      wayacredit: () => import(/* webpackChunkName: "withdrawStatusWayacredit" */ './page/withdrawStatusWayacredit.vue'),
      withdrawStatusCommon1: () => import(/* webpackChunkName: "withdrawStatusCommon1" */ './page/withdrawStatusCommon1.vue'),
    },
    data() {
      return {
        componentTag: 'baseWS',
        to: '',
        from: '',
      }
    },
    created() {
      if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.withdrawStatus) {
        this.componentTag = globalConfig.pageStyleSetting.withdrawStatus
      }
    },
    beforeRouteEnter (to, from, next) {
      next(vm => {
        vm.to = to;
        vm.from = from;
        // if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        //   vm.$refs.child.beforeRouteEnter(to, from);
        // }
      })
    }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
