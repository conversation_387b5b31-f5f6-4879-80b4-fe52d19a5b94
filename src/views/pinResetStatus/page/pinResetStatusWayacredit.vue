<template>
  <CPage className="pin-reset">
    <div class="wrap">
      <div class="status">
        <img
          :src="require(`@/assets/images/wayacredit/bg-success.png`)"
          alt=""
        />
        <div>Congratulations</div>
      </div>
      <div class="pass-detail">
        <div
          class="infor"
          v-text="
            `Your ${channel} PIN has been ${
              fromName === 'forgetPin' ? 'reset' : 'set'
            }.`
          "
        ></div>
      </div>
    </div>
    <CButton
      @buttonClick="backHomePage()"
      className="suc-button"
      name="OK"
    ></CButton>
  </CPage>
</template>

<script>
import publicMixns from "../pinResetStatusMixins.js";
export default {
  name: "pinResetStatusWayacredit",
  mixins: [publicMixns],
  data() {
    return {};
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.pin-reset {
  &.c-page {
    margin-top: 0px;
    background: #1e1e20;
    padding-left: 0;
    padding-right: 0;
  }
  .wrap {
    padding-left: 12px;
    padding-right: 12px;
  }
  .status {
    padding-top: 56px;
    img {
      width: 88px;
      height: 88px;
    }
    div {
      height: 25px;
      font-size: 18px;
      font-family: Avenir, Avenir-Heavy;
      font-weight: 800;
      text-align: CENTER;
      color: #fff;
      line-height: 25px;
      margin-top: 5px;
    }
  }
  .pass-detail {
    margin-top: 24px;
    .infor {
      font-size: 12px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: CENTER;
      color: rgba(255, 255, 255, 0.50);
      line-height: 18px;
      span {
        color: #fff;
        font-weight: 700;
      }
    }
  }
  .suc-button {
    margin-top: 25px;
    border-radius: 10px;
    background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
    width: calc(100% - 44px);
    color: #282A30;
    text-align: center;
    font-family: Noto Sans;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
  }
  .back-home {
    font-size: 14px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: CENTER;
    color: $themeColor;
    margin-top: 18px;
  }
}
</style>
