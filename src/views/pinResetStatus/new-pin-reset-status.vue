
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
    name: 'pinResetStatus',
    components: {
      basePRS: () => import(/* webpackChunkName: "basePRS" */ './page/pinResetStatusBase.vue'),
      wayacredit: () => import(/* webpackChunkName: "pinResetStatusWayacredit" */ './page/pinResetStatusWayacredit.vue'),
      pinResetStatusCommon1: () => import(/* webpackChunkName: "pinResetStatusCommon1" */ './page/pinResetStatusCommon1.vue'),
    },
    data() {
        return {
          componentTag: 'basePRS',
          to: '',
          from: '',
        }
    },
    created() {
      if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.pinResetStatus) {
        this.componentTag = globalConfig.pageStyleSetting.pinResetStatus
      }
    },
    beforeRouteEnter (to, from, next) {
      next(vm => {
        vm.to = to;
        vm.from = from;
        if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
          vm.$refs.child.beforeRouteEnter(to, from);
        }
      })
    }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
