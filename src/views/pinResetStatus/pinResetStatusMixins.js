import CPage from '@/components/c-page.vue';
import CButton from '../../components/c-button.vue';
import { mapState } from 'vuex';


export default {
    name: 'pinResetStatus',
    computed: {
    ...mapState(['channel', 'productSource']),
    },
    data() {
        return {
            fromName: ''
        };
    },

    components: {
        CPage,
        CButton
    },

    methods: {
        // 返回首页
        backHomePage() {
          let self = this;
          if (self.fromName === 'forgetPin') {
            self.$router.go(-3);
          } else {
            self.$router.go(-2);
          }
        },
        beforeRouteEnter () {
          const vm = this;
          vm.fromName = vm.$route.query.fromName;
        }
    },

    mounted() {
      window.scrollTo(0, 0);   
      this.beforeRouteEnter(this.$parent.to, this.$parent.from);
    }
};