<template>
  <div class="important-tips">
<!--    <c-header :backFun="myBackFun"></c-header>-->
    <div class="title">
      <img src="@/assets/images/common/important-tips-title.png" alt="">
    </div>
    <div class="content">
      <p>We would like to inform our customers that the {{ channel }} app is now managed by Goldman Microfinance Bank Limited. As a result, all new transactions on this app will be conducted between the customer and Goldman Microfinance Bank Limited.</p>
      <p>For existing customers, please note that these changes do not affect the terms and conditions of your active loans in any way. Loan terms, including repayment amount and duration, will remain unchanged. Customers are advised to make payments only to the account number displayed on their dashboard. If you do not accept this change, please make a one-time full repayment in advance or make repayments to the original repayment account. Continued use of the app or making repayments to the new repayment account will be deemed as acceptance and consent of this change.</p>
      <p>If you have any questions or concerns, please reach out to customer support at {{ email }} only. Please be aware that any other communication channels aside from the provided email are not official and should be considered fraudulent.</p>
    </div>
  </div>
</template>

<script>
import { gotoHomeActivity } from "@/assets/js/native";
import { mapState } from "vuex";
export default {
  name: 'ImportantTips',
  data() {
    return {
    };
  },
  computed: {
    ...mapState(['productSource', 'channel']),
    email() {
      const map = {
        palmcredit: '<EMAIL>',
        newcredit: '<EMAIL>',
        xcash: '<EMAIL>',
        xcrosscash: '<EMAIL>'
      }
      return map[this.productSource] || '';
    }
  },
  mounted() {
  },
  methods: {
    myBackFun() {
      gotoHomeActivity('importantTips');
    },
  },
};
</script>

<style lang="scss" scoped>
.important-tips {
  //padding-top: 56px;
  .title{
    margin-top: 6px;
    margin-left: 14px;
    margin-bottom: 16px;
    img {
      display: block;
      width: 127px;
    }
  }
  .content {
    padding: 0 18px;
    p {
      margin-bottom: 5px;
      color: #1B3155;
      font-size: 12px;
      line-height: 1.3;
      text-align: left;
    }
  }
}
</style>
