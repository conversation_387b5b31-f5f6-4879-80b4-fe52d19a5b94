<template>
  <div class="container">
    <component :is="componentTag"></component>
  </div>
</template>
<script>
import { globalConfig } from '@/api/config'

export default {
  name: "inviteReview",
  components: {
    inviteReviewStyle: () =>
      import(
        /* webpackChunkName: "inviteReviewStyle" */ "./page/inviteReviewStyle.vue"
      )
  },
  data() {
    return {
      componentTag: "inviteReviewStyle"
    };
  },
  created() {
    const productSource = localStorage.getItem("productSource");
    console.log("中转组件", localStorage.getItem("productSource"));
    document.getElementsByTagName("body")[0].setAttribute("data-theme", productSource);
    if (globalConfig.inviteReviewStyle !== undefined) {
      this.componentTag = globalConfig.inviteReviewStyle
    }
  }
};
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
