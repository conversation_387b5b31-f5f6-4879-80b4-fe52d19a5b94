<template>
  <div class="wrap">
    <div class="invite-review-container">
      <div class="top">
        <img class="back" @click="goBack" src="@/assets/images/star/ic_title_back.png" alt="">
      </div>
      <div class="content">
        <div class="dear">Dear VIP, hello!</div>
        <div class="note">
          <div class="text">
            First of all, we sincerely thank you for your continued support and love for our App. Your every use and feedback is a valuable asset for our continuous progress and improvement.
          </div>
          <div class="text">
            If you are satisfied with our App and are willing to share your experience, we sincerely invite you to give us a positive review. Your praise will be the best affirmation of our team's unremitting efforts and will also be of great reference value to other users.
          </div>
          <div class="text">
            Thank you again for your continued support and trust! We will continue to work hard to provide better services and user experience. If you have any questions or suggestions, please feel free to contact us. Looking forward to your review!
          </div>
        </div>
        <div class="bottom-text">
          With best regards,
        </div>
        <div class="bottom-text">
          Your {{ productName }} team
        </div>
      </div>
      <button class="review" @click="goToReview">Go to review</button>
      <button class="how-to" @click="goHowToReview">How to review?</button>
      <HowToReviewStyle v-model="showHowToReview" @close="hidePopup" @openStore="goToReview"></HowToReviewStyle>
    </div>
  </div>
</template>

<script>
import publicMixns from "../inviteReview.js";
export default {
  name: "inviteReviewStyle1",
  mixins: [publicMixns],
  data() {
    return {
    }
  },
  created() {}
};
</script>

<style lang="scss" scoped>
.wrap{
  height: 100%;
  width: 100%;
  background: linear-gradient(180deg, #20322D 0%, #2C4942 100%);
}
.invite-review-container {
  width: 360px;
  height: 640px;
  box-sizing: border-box;
  background-image: url(../../../assets/images/star/vite_review_bg1.png);
  background-size: 360px 640px;
  .top{
    padding-top: 13px;
    padding-left: 18px;
    margin-bottom: 25px;
    text-align: left;
    .back{
      width: 24px;
      height: 19px;
    }
  }
  .content{
    padding-top: 18px;
    margin: 0 65px 0 65px;
  }
  .dear{
    color: #000;
    font-family: Avenir;
    font-size: 16px;
    font-style: normal;
    font-weight: 800;
    margin-bottom: 10px;
    text-align: left;
  }
  .note{
    color: #000;
    font-family: Avenir;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    .text{
      text-align: left;
    }
  }
  .bottom-text{
    color: #000;
    font-family: Avenir;
    font-size: 12px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    margin-top: 6px;
    text-align: left;
  }
  button{
    width: 310px;
    height: 45px;
    margin: 0 25px 0 25px;
    position: absolute;
    left: 0px;
    &.review{
      border-radius: 8px;
      background: linear-gradient(113deg, #FEE5C0 -2.36%, #E1B26C 92.78%);
      box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.25);
      margin-top: 70px;
      bottom: 80px;
      color: #6F4910;
      text-align: center;
      font-family: "Noto Sans";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      &::after {
        width: 165px;
        height: 26px;
        content: "Review to get higher credit limits";
        position: absolute;
        top: -15px;
        right: -8px;
        background: url(../../../assets/images/star/union.png) no-repeat;
        background-size: 100% 100%;
        padding: 2px 6px;
        white-space: nowrap;
        color: #C67C11;
        font-size: 10px;
        font-weight: 500;
      }
    }
    &.how-to{
      margin-top: 12px;
      bottom: 24px;
      border-radius: 8px;
      background: linear-gradient(113deg, #FFF -2.36%, #FFF3E2 92.78%);
      box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.25);
      color: #6F4910;
      text-align: center;
      font-family: "Noto Sans";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
    }
  }
}
</style>
