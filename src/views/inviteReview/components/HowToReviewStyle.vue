<!-- 五星好评弹框 -->
<template>
  <div v-if="value" class="how_to_view_popup">
    <img class="back" @click="hidePopup('close')" src="@/assets/images/star/ic_title_back.png" alt="">
    <div class="popup-content">
      <div class="title">How To Review?</div>
      <div class="sub-title">Giving a five-star review is easy!</div>
      <div class="wrap">
        <ul class="steps">
          <li v-for="step in stepList" :key="step.content">
            <div class="step" v-text="step.content"></div>
            <img :src="step.img" alt="">
          </li>
        </ul>
      </div>
    </div>
    <button @click="openStore">Go to review</button>
  </div>
</template>

<script>
import { judgeClient } from "@/assets/js/common";
export default {
  name: 'HowToReviewStyle',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  computed: {
    stepList() {
      const isPhone = judgeClient() === 'IOS';
      const linkName = isPhone ? 'app' : 'Google play'
      return [{
        content: `1. Just find our App in the ${linkName}.`,
        img: isPhone ? require('@/assets/images/star/ios/step1.png') : require('@/assets/images/star/android/step1.png')
      }, {
        content: '2. Click the review button and select a five-star rating.',
        img: isPhone ? require('@/assets/images/star/ios/step2.png') : require('@/assets/images/star/android/step2.png')
      }, {
        content: `3. If you wish,you can also leave a short comment to share your feelings and experience with our App.`,
        img: isPhone ? require('@/assets/images/star/ios/step3.png') : require('@/assets/images/star/android/step3.png')
      }]
    }
  },
  mounted() {
    this.report('reviewfaq_view');
    window.nativeBackCallback = () => {
      this.hidePopup('close');
      // 1 原生不处理返回键  0 原生处理返回键 
      return 1
    }
  },
  methods: {
    hidePopup(type) {
      this.report('reviewfaq_click');
      this.$emit('input');
      this.$emit('close', type);
    },
    openStore() {
      this.$emit('openStore');
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
          page: 'inviteReview',
          eventName: eventName,
          eventData: {
          event_time: Date.now()
          }
      });
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.how_to_view_popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: #20322D;
  text-align: left;
  .back{
    width: 24px;
    height: 19px;
    margin-top: 13px;
    margin-left: 18px;
  }
  .popup-content {
    height: 80%;
    .title{
      text-align: center;
      text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
      font-family: Avenir;
      font-size: 25px;
      font-style: italic;
      font-weight: 900;
      background: linear-gradient(180deg, #FFF 0%, #FFFDD8 73.44%, #FFECA8 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: 13px;
    }
    .sub-title{
      color: #EDC123;
      text-align: center;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      margin-top: 13px;
      margin-bottom: 13px;
    }
    .wrap{
      height: 80%;
      overflow: scroll;
      .steps{
        margin: auto;
        width: 300px;
        li{
          width: 300px;
          height: auto;
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.15);
          padding: 9px 20px 15px 20px;
          margin-bottom: 12px;
          box-sizing: border-box;
          .step{
            color: #FFF;
            font-family: Avenir;
            font-size: 14px;
            font-style: normal;
            font-weight: 800;
            margin-bottom: 5px;
          }
          img{
            width: 150px;
            height: auto;
            margin: 9px auto 0;
          }
        }
      }
    }
  }
  button{
    width: 310px;
    height: 45px;
    line-height: 45px;
    border-radius: 8px;
    background: linear-gradient(113deg, #FEE5C0 -2.36%, #E1B26C 92.78%);
    box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.25);
    position: absolute;
    bottom: 45px;
    left: 25px;
    color: #333;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
  }
}
</style>
