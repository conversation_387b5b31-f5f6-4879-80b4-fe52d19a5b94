
import { globalConfig } from '@/api/config';
import HowToReviewStyle from './components/HowToReviewStyle.vue'
import { 
  gotoGooglePlay, 
  openAppStore,
  gotoHomeActivity
} from "@/assets/js/native";
import { mapState } from 'vuex';
import { judgeClient } from '@/assets/js/common';

export default {
  data() {
    return {
      productName: globalConfig.productName,
      showHowToReview: false,
      device: '',
      hidden: false
    }
  },
  computed: {
    ...mapState(['productSource', 'channelPackageName']),
    },
  components: {
    HowToReviewStyle
  },
  created() {

  },
  mounted() {
    this.device = judgeClient();
    const queryObj = this.$route.query
    this.report('review_view');
    
    window.nativeBackCallback = () => {
      if (queryObj.enterfrom) {
        gotoHomeActivity()
      } else {
        this.$router.back();
      }
      
      // 1 原生不处理返回键  0 原生处理返回键 
      return 1
    }
  },
  methods: {
    // 处理五星好评调用
    hidePopup() {
      const queryObj = this.$route.query
      window.nativeBackCallback = () => {
        if (queryObj.enterfrom) {
          if (this.showHowToReview) {
            this.showHowToReview = false
          } else {
            gotoHomeActivity()
          }
        } else {
          this.$router.back();
        }
        // 1 原生不处理返回键  0 原生处理返回键 
        return 1
      }
    },
    goBack() {
      const queryObj = this.$route.query
      if (queryObj.enterfrom) {
        gotoHomeActivity()
      } else {
        this.$router.back();
      }
    },
    goToReview() {
      this.report('review_goto_button_click')
      this.comfirmHandler();
    },
    goHowToReview() {
      this.showHowToReview = true;
      this.report('review_faq_button_click');
    },
    comfirmHandler() {
      this.hidden = false;
      document.addEventListener('visibilitychange', this.visibilityFun, false);
      if (this.device === 'Android') {
          // 打开对应的google play页面
          gotoGooglePlay(this.channelPackageName);
      } else {
          openAppStore();
      }
      // 如果没有打开，则调用以下逻辑。
      setTimeout(() => {
          this.$hideLoading();
          if (!this.hidden) {
              if (this.device === 'Android') {
                location.href = `https://play.google.com/store/apps/details?id=${this.channelPackageName}`;
              } else {
                location.href = process.env.VUE_APP_API_iosLink;
              }
          } else {
              document.removeEventListener('visibilitychange', this.visibilityFun, false);
          }
      }, 2000);
    },
    visibilityFun() {
      if (document.hidden) {
          this.hidden = true;
      } else {
          this.hidden = false;
      }
  },
    report(eventName) {
      this.$store.dispatch('reportEvent', {
          page: 'inviteReview',
          eventName: eventName,
          eventData: {
          event_time: Date.now()
          }
      });
    }
  }
}
