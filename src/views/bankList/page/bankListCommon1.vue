<template>
  <div class="bank-list-container">
    <c-header titleName="Bank Name" :styType="'c'"></c-header>
    <div class="bank-list">
      <van-search v-model="searchKey" shape="round" placeholder="Search" clearable />
      <template v-if="bankList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text=""
          @load="onLoad"
        >
          <van-cell
            v-for="item in bankList"
            :key="item.bankCardName"
            :title="item.bankCardName"
            @click="chooseBank(item)"
          />
        </van-list>
      </template>
    </div>
  </div>
</template>

<script>
import publicMixns from "../bankListMixins.js";
export default {
  name: "bankListCommon1",
  mixins: [publicMixns],
  data() {
    return {};
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.bank-list-container {
  height: 100%;
  background: #fff;
}
.bank-list {
  height: 100%;
  padding-top: 56px;
  box-sizing: border-box;
  ::v-deep .van-search {
    background: $color;
    .van-search__content {
      border-radius: 50px;
      background: #F8F8F8;
    }
    input {
      color: #8E8E8E;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
    .van-icon-search {
      color: #8E8E8E;
    }
  }
  .van-list {
    overflow: scroll;
    max-height: calc(100% - 94px);
    box-sizing: border-box;
    border-radius: 16px;
    border: 1px solid #1B40FF;
    border-color: $color;
    background: #FFF;
    margin: 22px;
    padding: 0 16px;
    .van-cell__title {
      text-align: left;
      color: #231815;
      text-align: left;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
    }
    .van-cell::after {
      right: 0;
      left: 0;
    }
    .van-cell {
      background: transparent;
      padding: 12px 0;
    }
  }
}
</style>
