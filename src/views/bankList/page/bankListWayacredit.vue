<template>
  <div class="bank-list-container">
    <c-header titleName="Bank Name" :styType="'b'"></c-header>
    <div class="bank-list">
      <van-search v-model="searchKey" shape="round" placeholder="Search" clearable />
      <template v-if="bankList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text=""
          @load="onLoad"
        >
          <van-cell
            v-for="item in bankList"
            :key="item.bankCardName"
            :title="item.bankCardName"
            @click="chooseBank(item)"
          />
        </van-list>
      </template>
    </div>
  </div>
</template>

<script>
import publicMixns from "../bankListMixins.js";
export default {
  name: "bankListWayacredit",
  mixins: [publicMixns],
  data() {
    return {};
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.bank-list-container {
  height: 100%;
  background: #1e1e20;
}
.bank-list {
  height: 100%;
  padding-top: 56px;
  box-sizing: border-box;
  ::v-deep .van-search {
    background: rgba(40, 42, 48, 0.8);
    .van-search__content {
      border-radius: 35px;
      background: #141416;
    }
    input {
      color: #fff;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
    }
    .van-icon-search {
      color: rgba(255, 255, 255, 0.30);
    }
  }
  .van-list {
    overflow: scroll;
    max-height: calc(100% - 94px);
    box-sizing: border-box;
    border-radius: 18px;
    border: 0.5px solid #FFF;
    background: rgba(40, 42, 48, 0.80);
    margin: 22px;
    padding: 0 16px;
    .van-cell__title {
      text-align: left;
      color: rgba(255, 255, 255, 0.70);
      text-align: left;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
    }
    .van-cell::after {
      right: 0;
      left: 0;
    }
    .van-cell {
      background: transparent;
      padding: 12px 0;
    }
  }
}
</style>
