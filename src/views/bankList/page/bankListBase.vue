<template>
  <div>
    <c-header titleName="Bank Name"></c-header>
    <div class="bank-list">
      <van-search
        v-model="searchKey"
        shape="round"
        background="#e3e3e3"
        placeholder="Search"
      />
    <van-list
          v-model="loading"
          :finished="finished"
          finished-text=""
          @load="onLoad"
          :style="{ height: containerHeight }"
        >
        <van-cell v-for="item in bankList" :key="item.bankCardName" :title="item.bankCardName" @click="chooseBank(item)" />
      </van-list>
    </div>
  </div>
</template>

<script>
import publicMixns from '../bankListMixins.js'
export default {
    name: 'bankListBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.bank-list{
  margin-top: 56px;
  .van-list{
    overflow: scroll;
    .van-cell__title{
      text-align: left;
    }
    .van-cell::after{
      right: -1px;
    }
  }
}
</style>