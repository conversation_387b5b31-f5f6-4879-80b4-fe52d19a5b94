
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'bankList',
  components: {
    baseBankList: () => import(/* webpackChunkName: "baseBl" */ './page/bankListBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "bankListWayacredit" */ './page/bankListWayacredit.vue'),
    bankListCommon1: () => import(/* webpackChunkName: "bankListCommon1" */ './page/bankListCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseBankList',
        to: '',
        from: '',
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.bankList) {
      this.componentTag = globalConfig.pageStyleSetting.bankList
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
