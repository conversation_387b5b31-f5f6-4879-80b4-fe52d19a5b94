import api from "@/api/interface";
export default {
  name: 'bankList',
  data() {
    return {
      searchKey: '',
      bankList: [],
      loading: false,
      finished: true,
      containerHeight: '',
      saveBankList: []
    };
  },

  methods: {
    onLoad() {},
    queryBankList4nc() {
      let self = this;
      self.$loading();
      return new Promise((resolve, reject) => {
        self.$http(api.bankAccount.queryBankList4nc, {}).then(res => {
          self.bankList = res;
          self.saveBankList = res;
          self.$hideLoading();
          resolve();
        }).catch(e => {
          console.log(e);
          self.$hideLoading();
          reject(e);
        });
      })
    },
    chooseBank(item) {
      let self = this;
      self.$store.commit('SET_BANK', item);
      self.$router.go(-1);
    }
  },
  watch: {
    searchKey: function(value) {
      let self = this;
      let bankList = [];
      self.saveBankList.forEach((item) => {
        console.log(item.bankCardName);
        let name = item.bankCardName;
        if (name.indexOf(value.toUpperCase()) === 0) {
          bankList.push(item);
        }
      });
      self.bankList = bankList;
    }
  },
  mounted() {
    const headerEl = document.querySelector('.van-search')
    if (headerEl) {
      const headerHeight = headerEl.getBoundingClientRect().height
      this.containerHeight = window.innerHeight - headerHeight + 'px'
    }
    this.queryBankList4nc();
    window.scrollTo(0, 0);
  }
};