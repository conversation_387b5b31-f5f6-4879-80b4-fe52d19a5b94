
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
export default {
  name: 'couponsFaq',
  components: {
    baseCouponsFaq: () => import(/* webpackChunkName: "basecoufaq" */ './page/couponsFaqBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "couponsFaqWayacredit" */ './page/couponsFaqWayacredit.vue'),
  },
  data() {
      return {
        componentTag: 'baseCouponsFaq',
        to: '',
        from: '',
      }
  },
  created() {
      const productSource = localStorage.getItem('productSource')
      if (productSource === 'ponykash') {
        // this.componentTag = 'sasa'
      } else if (productSource === 'wayacredit') {
        this.componentTag = 'wayacredit'
      }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
