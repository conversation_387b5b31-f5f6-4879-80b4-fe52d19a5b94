<template>
  <div class="coupons-faq">
    <c-header titleName="Coupon Instructions"></c-header>
    <!-- <div class="coupons-faq-content" v-html="coupons-faq">
    </div> -->
    <iframe width="100%" height="auto" id="iframe" frameborder="0" :src="src"></iframe>
  </div>
</template>

<script>
import publicMixns from '../couponsFaqMixins.js'
export default {
    name: 'couponsFaqBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
  .coupons-faq{
    height: 100%;
  }
  #iframe{
    height: 100%;
    margin-top: 56px;
  }
</style>