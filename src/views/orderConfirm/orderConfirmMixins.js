import CPage from '@/components/c-page.vue';
import repaymentDetailPopup from './components/repayment-detail-popup.vue';
import repaymentPlanWayacredit from './components/repayment-plan-wayacredit.vue';
import api from "@/api/interface";
import { mapState } from 'vuex';


export default {
  name: 'orderConfirm',
  components: {
    CPage,
    repaymentDetailPopup,
    repaymentPlanWayacredit
  },
  computed: {
    ...mapState(['productSource'])
  },
  data() {
    return {
      show: false,
      orderConfirmObj: {}
    };
  },

  created() {
    const orderConfirmObj = localStorage.getItem('orderConfirm');
    if (orderConfirmObj) {
      this.orderConfirmObj = JSON.parse(orderConfirmObj);
      this.orderConfirmObj.loanCalcVOList.forEach(item => {
        if (item.key === 'Credit Protection Service') { // 若字段改变，后端也要变更这个字段
          this.orderConfirmObj.insuranceFee = item.value;
        }
      })
    }
    console.log(this.orderConfirmObj)
  },

  mounted() {
  },

  methods: {
    showPlan() {
      this.show = true;
    },
    closePlan() {
      this.show = false;
    },
    // 跳转绑卡中台的逻辑
    bindCard() {
      let obj = {};
      this.$loading();
      if (this.productSource === 'palmcredit') {
        obj.businessChannel = 'palmcreditnew';
      }
      // 缓存绑卡标识。用于绑卡结果页判断跳转。交互跟提现结果页一样。
      localStorage.setItem('bindCardType', 'widthdrawStatusAddBankCard');
      let scene = '2';
      // // 暂时开放这两种场景值。
      // if (this.productSource === 'xcash' || this.productSource === 'xcrosscash') {
      //   scene = '2';
      // }
      return  new Promise((resolve, reject) => {
          this.$http(api.commonBlc.routeRedirect, {
          "data":{
              "scene": scene,
              ...obj
          } // 目前是写死的
          }).then(res => {
          
          this.$hideLoading();
          // 跳转绑卡中台的地址
          location.href = res.redirectUrl;
          resolve();
          }).catch(e => {
            this.$hideLoading();
            reject(e);
          })
      })
    },
    modify() {
      this.$router.go(-1);
    }
  },
};