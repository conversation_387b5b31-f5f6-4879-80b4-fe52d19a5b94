<!-- 预提现订单确认页 -->
<template>
  <div class="order-confirm">
    <c-header titleName="Order Confirm"></c-header>
    <CPage>
      <div class="tips">You have a cash loan order to be confirmed</div>
      <!-- 借款信息 -->
      <div class="repayment-schedule">
        <div class="repayment-list">
          <div class="custom loan-amount">
            <div class="item-title">Loan Amount</div>
            <div class="right">
              <div class="after">
                <span class="currency">₦</span>
                <span class="value">{{ orderConfirmObj.actualTotalAmount | singleToThousands }}</span>
              </div>
            </div>
          </div>
          <div class="custom loan-term">
            <div class="item-title">Loan Terms</div>
            <div class="right">
              <div class="after">
                <span class="days" v-text="`${orderConfirmObj.loanSpan * orderConfirmObj.loanTerm} Days`"></span>
                <span class="term" v-if="orderConfirmObj.loanTerm > 1" v-text="`Every ${orderConfirmObj.loanSpan} days in ${orderConfirmObj.loanTerm} installment`"></span>
              </div>
            </div>
          </div>
          <div class="custom">
            <div class="item-title">Platform Fee</div>
            <div class="right">
              <div class="after">
                <span class="currency">₦</span>
                <span class="value">{{ common.thousandsToFixed(orderConfirmObj.pltfFeeAmt)}}</span>
              </div>
            </div>
          </div>
          <div class="custom" v-if="orderConfirmObj.insuranceFee">
            <div class="acc-pro-ser">
              <div class="item-title">Credit Protection Service</div>
              <img class="icon" src="@/assets/images/protection-service.png" alt="">
            </div>
            <div class="right">
              <div class="after check-acc">
                <span class="currency">₦</span>
                <span class="value" v-text="common.thousandsToFixed(orderConfirmObj.insuranceFee)"></span>
              </div>
            </div>
          </div>
          <div class="custom">
            <div class="item-title">Received</div>
            <div class="right">
              <div class="after">
                <span class="currency">₦</span>
                <span class="value">{{ common.thousandsToFixed(orderConfirmObj.actualTotalAmount && orderConfirmObj.actualTotalAmount - (orderConfirmObj.pltfFeeAmt && orderConfirmObj.pltfFeeAmt)) }}</span>
              </div>

            </div>
          </div>
          <!-- 单期显示 -->
          <template v-if="orderConfirmObj.plans.length === 1">
            <div class="repayment-plan custom">
              <div class="item-title">Due Date</div>
              <div class="right">
                <div class="after">
                  <span class="value">{{ (orderConfirmObj.deadline && orderConfirmObj.deadline)}}</span>
                </div>
              </div>
            </div>
            <div class="repayment-plan custom">
              <div class="item-title">Repayment Amount</div>
              <div class="right">
                <div class="after">
                  <span class="currency">₦</span>
                  <span class="value">{{ common.thousandsToFixed(parseInt(orderConfirmObj.plans[0].termAmount && orderConfirmObj.plans[0].termAmount))}}</span>
                </div>
              </div>
            </div>
          </template>
          <!-- 多期显示 -->
          <template v-else>
            <div class="repayment-plan custom" @click="showPlan()">
              <div class="item-title">Repayment Plan</div>
              <div class="right">
                <span>Check</span>
                <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="change-bank">
        <img class="icon" :src="require('@/assets/images/UBA.png')" />
        <div class="detail">
          <div class="name">Disbursement Account</div>
          <div class="num" v-text="orderConfirmObj.bankName + '(' + orderConfirmObj.bankAccNo.slice(-4, orderConfirmObj.bankAccNo.length) + ')'"></div>
        </div>
      </div>
      <c-button name="Confirm" @buttonClick="bindCard()" class="confirm"></c-button>
      <div class="modify" @click="modify()">Modify order</div>
      <repaymentDetailPopup :repaymentDetailList="orderConfirmObj.plans" @close="closePlan()" :showRepayDetailPop="show" />
    </CPage>
  </div>
</template>

<script>
import publicMixns from '../orderConfirmMixins.js'
export default {
    name: 'orderConfirmBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.order-confirm{
  height : 100%;
  .c-page{
    background: #F2F3F8;
    padding-left: 0;
    padding-right: 0;
    height : 100%;
    .tips{
      background: #F3FDFA;
      font-family: Avenir;
      font-size: 15px;
      font-weight: 800;
      line-height: 33px;
      text-align: left;
      height: 33px;
      color: #02B17B;
      padding-left: 18px;
    }
    .repayment-schedule{
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0px 1px 10px 0px rgb(0 54 101 / 6%);
      text-align: left;
      padding: 15px 0 15px 18px;
      margin-top: 10px;
      margin-left: 12px;
      margin-right: 12px;
      .title{
        height: 20px;
        font-size: 12px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #919db3;
        line-height: 20px;
      }
      .custom{
          display: flex;
          justify-content: space-between;
          height: 42px;
          align-items: center;
          position: relative;
          &.interest{
            .right{
              align-items: flex-end;
              font-weight: 700;
              flex-direction: column-reverse;
            }
          }
          &.repayment-plan{
            .right{
              display: flex;
              align-items: center;
              width: 40px;
              height: 40px;
              text-align: right;
              justify-content: flex-end;
              span{
                font-family: DINPro;
                font-size: 14px;
                font-weight: 500;
                text-align: right;
              }
            }
          }
          .acc-pro-ser{
            display: flex;
            align-items: center;
            .icon{
              width: 14px;
              height: 14px;
              margin-top: 1px;
              margin-right: 2px;
            }
            ::v-deep .c-popover{
              margin-left: 2px;
              .wrap{
                width: 14px;
                height: 14px;
                display: flex;
                align-items: center;
              }
            }
          }
          .item-title{
            height: 19px;
            font-size: 14px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: left;
            color: #536887;
            line-height: 19px;
          }
          .right{
            text-align: right;
            margin-right: 18px;
            display: flex;
            align-items: center;
            font-weight: 700;
            .after{
              display: flex;
              align-items: center;
              &.check-acc{
                display: flex;
                align-items: center;
                img{
                  width: 18px;
                  height: 18px;
                  margin-left: 5px;
                }
              }
              .currency{
                font-family: DINPro;
                font-size: 14px;
                font-weight: 500;
                line-height: 18px;
                letter-spacing: 0px;
                text-align: right;
                color: #536887;

              }
              .value{
                font-family: DINPro;
                font-size: 14px;
                font-weight: 500;
                line-height: 18px;
                letter-spacing: 0px;
                text-align: right;
                color: #536887;
                .interest{
                  margin-left: 2px;
                }
              }
            }
            .before{
              &.reduce{
                margin-top: 6px;
                text-decoration: line-through;
                margin-right: 5px;
                .currency{
                  color: #919db3;
                  font-size: 14px;
                }
                .value{
                  margin-left: 0px;
                }
              }
              .currency{
                height: 12px;
                font-size: 12px;
                font-family: Montserrat, Montserrat-Bold;
                font-weight: 700;
                text-align: right;
                line-height: 12px;
                color: #929eb4;
              }
              .value{
                height: 14px;
                font-size: 14px;
                font-family: DINAlternate, DINAlternate-Bold;
                // font-weight: 700;
                // text-decoration: line-through;
                text-align: right;
                color: #919db3;
                line-height: 14px;
                margin-left: 4px;
              }
            }
          }
          &.loan-amount{
            height: 50px;
            .after{
              .currency{
                font-family: DINPro;
                font-size: 37px;
                font-weight: 500;
                text-align: right;
                color: $themeColor;
              }
              .value{
                font-family: DINPro;
                font-size: 37px;
                font-weight: 500;
                text-align: right;
                color: $themeColor;
              }
            }
          }
          &.loan-term{
            min-height: 50px;
            .after{
              align-items: flex-end;
              flex-direction: column;
              font-family: Avenir;
              .days{
                font-size: 18px;
                font-weight: 800;
                text-align: center;
                color: $themeColor;
              }
              .term{
                font-size: 12px;
                font-weight: 500;
                text-align: right;
                transform: scale(.85);
                margin-right: -13px;
              }
              &::after {
                position: absolute;
                box-sizing: border-box;
                content: " ";
                pointer-events: none;
                right: 2px;
                bottom: 0;
                left: 0px;
                border-bottom: 1px solid #ebedf0;
                transform: scaleY(0.5);
              }
            }
          }
      }
      .van-cell-group{
        margin-top: 10px;
        border-radius: 8px;
        &::after{
          border: none !important;
        }
        .van-cell{
          padding: 18px 0;
          height: 58px;
          .van-cell__title{
            span{
              font-size: 14px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              color: #536887;
            }
          }
          .van-cell__value{
            margin-right: 5px;
            span{
              height: 23px;
              font-size: 16px;
              font-family: DINAlternate, DINAlternate-Bold;
              font-weight: 700;
              text-align: right;
              color: #1b3155;
              line-height: 23px;
            }
          }
          .van-icon-arrow:before{
            color: #919db3;
          }
          .van-cell__right-icon{
            margin-right: 18px;
          }
        }
      }
    }
    .change-bank{
      height: 40px;
      font-weight: 600;
      border-radius: 12px;
      box-shadow: 0px 1px 10px 0px rgb(0 54 101 / 6%);
      background: #ffffff;
      display: flex;
      align-items: center;
      padding: 10px 18px;
      margin-top: 13px;
      margin-left: 12px;
      margin-right: 12px;
      img {
        flex: none;
        width: 40px;
        height: 40px;
        margin-right: 5px;
      }
      .detail{
        text-align: left;
        flex: 1;
        word-break: break-all;
        .name{
          font-size: 12px;
          font-family: Avenir, Avenir-Medium;
          font-weight: 500;
          text-align: left;
          color: #1b3155;
          line-height: 19px;
        }
        .num{
          font-size: 14px;
          // font-family: Avenir, Avenir-Medium;
          font-weight: 700;
          text-align: left;
          color: #1b3155;
          line-height: 16px;
        }
      }
    }
    .confirm{
      position: fixed;
      bottom: 45px;
      left: calc((100% - 328px)/2);
      background: $themeColor;
    }
    .modify{
      position: fixed;
      bottom: 10px;
      left: 0px;
      font-family: Avenir;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
      color: $themeColor;
      background-image: linear-gradient(180deg, rgba(242, 243, 248, 0) 0%, rgba(242, 243, 248, 0.8) 43%, #F2F3F8 100%);
      width: 100%;
    }
  }
}
</style>
