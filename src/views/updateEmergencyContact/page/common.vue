<template>
  <div class="emergencyContactInfo-container">
    <c-header titleName="Update Emergency Contact"></c-header>
    <div class="credit-index">
      <ValidationObserver slim ref="form">
        <form
          class="emergencyContactForm"
          @submit.prevent="goNext()"
          @focus.capture="focusCapture"
          @click.capture="clickCapture"
          novalidate
        >
          <!--信息填写 1 start-->
          <div class="formBox">
            <!-- relatives -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_1"
              name="relation"
              rules="required|min:1"
              v-slot="{ failed }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed || fromBackEnd }"
              >
                <DropDownList
                  :id="'relativesBox'"
                  disabled
                  :dataList="relationArray"
                  label="Family member"
                  labelProperty="name"
                  @change="relationChange($event)"
                  v-model="formData.relation"
                  :isRequired="true"
                ></DropDownList>
              </div>
            </ValidationProvider>
            <!-- phone number -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_3"
              ref="relativesPhone"
              name="relativesPhone"
              rules="required|phoneTest|max:11"
              v-slot="{ failed }"
            >
              <palm-input
                :id="'relativePhoneBox'"
                :disabled="relativesDisabled"
                type="tel"
                label="Phone Number(e.g. 07012345678)"
                maxlength="11"
                v-model="formData.relativesPhone"
                :class="{ error_input_border: failed || parentsError }"
                @input="relativesPhoneInput"
                @blur="blurSpousePhone(formData.relativesPhone)"
                @keyup="relativesPhoneKeyup"
                :isRequired="true"
              />
              <div class="fill_phone_length">
                <em>{{ formData.relativesPhone.length }}</em
                >/11
              </div>
              <div class="choose" @click.prevent="chooseSystemContact('relatives')">Contact</div>
              <div v-if="relativesDisabled" @click.stop="chooseSystemContact('relatives')" class="overlay"></div>
            </ValidationProvider>
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_2"
              name="relativesName"
              rules="required|min:1"
              v-slot="{ failed }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed || parentsNameError }"
              >
                <palm-input
                  :id="'relativeNameBox'"
                  :disabled="relativesDisabled"
                  v-model="formData.relativesName"
                  label="Name"
                  maxlength="30"
                  @input="relativesNameInput"
                  @blur="blurRelativesName(formData.relativesName)"
                  @keyup="keyupRelativesName"
                  :isRequired="true"
                />
                <div v-if="relativesDisabled" @click.stop="chooseSystemContact('relatives')" class="overlay"></div>
              </div>
            </ValidationProvider>
            <!-- Colleague/Friend======================== -->
            <ValidationProvider
              tag="div"
              class="fill_item select_one clearfix"
              id="d_jump_4"
              name="other"
              rules="required|min:1"
              v-slot="{ failed }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed || fromBackEnd }"
              >
                <DropDownList
                  :id="'friendBox'"
                  label="Colleague/Friend"
                  :dataList="otherRelationArray"
                  labelProperty="name"
                  @change="otherChange($event)"
                  v-model="formData.other"
                  :isRequired="true"
                ></DropDownList>
              </div>
            </ValidationProvider>
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_6"
              ref="friendsPhone"
              name="friendsPhone"
              rules="required|phoneTest|max:11"
              v-slot="{ failed }"
            >
              <palm-input
                :id="'friendPhoneBox'"
                :disabled="friendsDisabled"
                type="tel"
                label="Phone Number(e.g. 07012345678)"
                @input="friendsphoneInput"
                v-model="formData.friendsPhone"
                maxlength="11"
                :class="{ error_input_border: failed || FriendsError }"
                @blur="blurFriendsPhone(formData.friendsPhone)"
                @keyup="friendsPhoneKeyup"
                :isRequired="true"
              />
              <div class="fill_phone_length">
                <em>{{ formData.friendsPhone.length }}</em
                >/11
              </div>
              <div class="choose" @click.prevent="chooseSystemContact('friends')">Contact</div>
              <div v-if="friendsDisabled" @click.stop="chooseSystemContact('friends')" class="overlay"></div>
            </ValidationProvider>
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_5"
              name="friendsName"
              rules="required|min:1"
              v-slot="{ failed }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed || FriendsNameError }"
                @click.prevent="chooseSystemContact('friends')"
              >
                <palm-input
                  :id="'friendNameBox'"
                  :disabled="friendsDisabled"
                  v-model="formData.friendsName"
                  label="Name"
                  maxlength="30"
                  @input="friendsNameInput"
                  @blur="blurFriendsName(formData.friendsName)"
                  @keyup="keyupFriendsName"
                  :isRequired="true"
                />
                <div v-if="friendsDisabled" @click.stop="chooseSystemContact('friends')" class="overlay"></div>
              </div>
            </ValidationProvider>
          </div>
          <div class="btn-box">
            <button class="c-button">Complete</button>
          </div>
          <PublicFooter></PublicFooter>
        </form>
      </ValidationObserver>
    </div>
  </div>
</template>

<script>
import publicMixns from '../updateEmergencyContactMixins.js'
export default {
    name: 'updateEmergencyContact',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>
<style lang="scss" scoped>
.emergencyContactInfo-container {
  height: 100%;
  padding-top: 56px;
  box-sizing: border-box;
}
.contact {
  position: absolute;
  right: 19px;
  top: 9px;
  text-align: center;
  img {
    display: inline-block;
    width: 24px;
    height: 24px;
  }
  p {
    color: #0068b2;
    font-family: Avenir;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
  }
}
.credit-index {
  height: 100%;
  padding-top: 12px;
  box-sizing: border-box;
  .emergencyContactForm {
    height: 100%;
    .c-button {
      width: calc(100% - 36px);
      height: 42px;
      line-height: 42px;
      border-radius: 7px;
      background: $themeColor;
      box-shadow: none;
      margin-left: 18px;
      margin-right: 18px;
      margin-top: 7px;
      color: #FFF;
      text-align: center;
      font-family: Avenir;
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      border: none;
    }
  }

  .formBox {
    height: calc(100% - 140px);
    overflow-y: auto;
    padding-bottom: 23px;
    box-sizing: border-box;
    & > div {
      margin-left: 18px;
      margin-right: 18px;
    }
  }
  .fill_phone_length {
    font-size: 10px;
    color: #999999;
    position: absolute;
    right: 12px;
    bottom: 20px;
  }

  .fill_phone_length>em {
    color: #fe5b6e;
  }

  .fill_phone_length.position {
    bottom: 18px;
  }
  .choose{
    position: absolute;
    right: 12px;
    top: 3px;
    color: $themeColor;
    text-align: right;
    font-family: Avenir;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    height: 20px;
    line-height: 20px;
  }
  .palm-input{
    &.is-disabled {
      ::v-deep .palm-input__inner{
        background-color: #FFF;
        border: 1px solid #C4CAD5;
      }
    }
  }
  .fill_item {
    margin-bottom: 12px;
    text-align: left;
    position: relative;
    .textarea_wrap{
      position: relative;
    }
    .overlay{
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 2;
      background: transparent;
    }
  }
  .select_one {
    margin-top: 0;
  }
  .select_wrap {
    position: relative;
  }
  /*输入框的样式修改*/
  /*设置输入框提示语的样式*/
  .fill_item input::placeholder {
    font-size: 16px;
    color: #919db3;
  }
  .fill_item textarea::placeholder {
    font-size: 16px;
    color: #919db3;
  }
  .emergency-family {
  }
  .emergency-friend {
  }
}
</style>
