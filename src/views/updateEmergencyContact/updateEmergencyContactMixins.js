import api from "@/api/interface";
import DropDownList from "@/components/dropDownList.vue";
import PalmInput from "@/components/palm-input.vue";
import PublicFooter from "@/components/public-footer.vue";
import {
  openSystemContact,
  getPhone
} from "@/assets/js/native";
import { jumpIndex } from "@/utils/anchorPoint.js";
import { phoneMap } from "@/utils/Map";
import { ValidationObserver, ValidationProvider } from "vee-validate";
import _ from "underscore";
import { mapState } from 'vuex';
import { decode } from 'js-base64';

export default {
  name: "emergencyContactInfo",
  components: {
    DropDownList,
    PalmInput,
    PublicFooter,
    ValidationProvider,
    ValidationObserver
  },
  computed: {
    ...mapState(['uid', 'channel', 'productSource', 'needReCreditliveDetection'])
  },
  data() {
    return {
      formData: {
        relation: "",
        other: "",
        relativesName: "",
        relativesPhone: "",
        relativesContactName: "",
        relativesContactMobile: "",
        friendsName: "",
        friendsPhone: "",
        friendsContactName: "",
        friendsContactMobile: "",
      },
      key: {}, // 用于提交后台的值
      //手机号相同，控制提示
      parentsError: false,
      FriendsError: false,
      //姓名相同，控制提示
      parentsNameError: false,
      FriendsNameError: false,

      //把每个表单中的验证取名的testName 组成一个数组
      testName: [
        { name: "relation", index: "1" },
        { name: "relativesName", index: "2" },
        { name: "relativesPhone", index: "3" },
        { name: "other", index: "4" },
        { name: "friendsName", index: "5" },
        { name: "friendsPhone", index: "6" }
      ],

      /*-------------------------------------------供选择的枚举值 start-----------------------------------------------------*/
      relationArray: [],
      otherRelationArray: [],
      /*------------------------------------------供选择的枚举值 end--------------------------------------------------------*/
      fromBackEnd: false, //由后台带出标志位
      relativesDisabled: true, // 初始化禁用
      friendsDisabled: true, // 初始化禁用
      userPhone: '' // 用户手机号
    };
  },
  beforeRouteEnter(to, from, next) {
    next()
  },
  async created() {
    this.queryDict();
    this.setInitData()
  },
  mounted() {
    this.report('update_contact_view');
    this.userPhone = getPhone();
    console.log('this.userPhone', this.userPhone);
  },
  destroyed() {
  },
  beforeDestroy() {
    this.handleLocalData()
  },
  methods: {
    /**
     * 处理缓存数据
     * 没有数据则缓存，有则赋值
     */
    setInitData() {
      const emergContactStr = localStorage.getItem(`emergContactObj${this.uid}`)
      if (emergContactStr) {
        const emergContactObj = JSON.parse(emergContactStr)
        console.log('缓存数据--紧急联系人', emergContactObj)
        this.formData = emergContactObj.formData
        this.key = emergContactObj.key
      }
    },
    handleLocalData() {
      localStorage.setItem("uid", this.uid)
      const localData = Object.assign({}, { formData: this.formData }, { key: this.key });
      window.localStorage.setItem(`emergContactObj${this.uid}`, JSON.stringify(localData));
    },
    queryDict() {
      this.$loading();
      return  new Promise((resolve, reject) => {
        this.$http(api.commonBlc.queryDict, {
          data: {
          }
        }).then(res => {
          this.$hideLoading();
          this.relationArray = res.familyRelation
          this.otherRelationArray = res.otherRelation
          resolve(res);
        }).catch(e => {
          reject(e);
        })
      })
    },
    backPrePage() {
    },

    //全局焦点事件捕获
    focusCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //全局点击事件捕获
    clickCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    relationChange(e) {
      this.formData.relation = e.value.value;
      this.key.relation = e.value.key;
    },
    otherChange(e) {
      this.formData.other = e.value.value;
      this.key.other = e.value.key;
    },
    /************* relatives **********/
    relativesNameInput(e) {
      this.parentsNameError = false
      this.formData.relativesName = e.replace(/^[\u4E00-\u9FA5]+$/, "")
    },
    blurRelativesName(e) {
      this.parentsNameError = false
      const nameregx = /^['\-abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\s']+$/
      if (!nameregx.test(e)) {
        this.parentsNameError = true
      }
      this.formData.relativesName=this.formData.relativesName.replace(/[\u4e00-\u9fa5]/g, '')
    },
    keyupRelativesName(e) {
      this.parentsNameError = false
      this.formData.relativesName = e.replace(/[\u4e00-\u9fa5]/g, '')
    },
    /************* Colleague/Friend **********/
    friendsNameInput(e) {
      console.log('friendsNameInput', e)
      this.FriendsNameError = false
      this.formData.friendsName = e.replace(/^[\u4E00-\u9FA5]+$/, "")
    },
    blurFriendsName(e) {
      this.FriendsNameError = false
      const nameregx = /^['\-abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\s']+$/
      if (!nameregx.test(e)) {
        this.FriendsNameError = true
      }
      this.formData.friendsName = this.formData.friendsName.replace(/[\u4e00-\u9fa5]/g, '')
    },
    keyupFriendsName(e) {
      this.FriendsNameError = false
      this.formData.friendsName = e.replace(/[\u4e00-\u9fa5]/g, '')
    },

    validSamePhone() {
      let isPass = false
      const a = this.formData.relativesPhone
      const b = this.formData.friendsPhone
      if (a && b && a.trim() === b.trim()) {
        this.parentsError = true
        this.FriendsError = true
        this.$newToast("Contact 1 and 2 cannot have the same cell phone No. please replace");
      } else {
        this.parentsError = false
        this.FriendsError = false
        isPass = true
      }
      return isPass
    },
    validUserPhone() {
      let isPass = false
      const a = this.formData.relativesPhone;
      const b = this.formData.friendsPhone;
      const userPhone = `0${this.userPhone}`;
      if (a && userPhone && a.trim() === userPhone.trim()) {
        this.parentsError = true
        this.$newToast("The contact number cannot be the same as the registered mobile number");
      } else if (b && userPhone && b.trim() === userPhone.trim()) {
        this.FriendsError = true
        this.$newToast("The contact number cannot be the same as the registered mobile number");
      } else {
        this.parentsError = false
        this.FriendsError = false
        isPass = true
      }
      return isPass
    },
    blurSpousePhone(e){
      phoneMap.add('Relatives Phone No.', e)
      this.validSamePhone(e);
      this.validUserPhone(e);
    },
    blurFriendsPhone(e){
      phoneMap.add('Friends Phone No.', e)
      this.validSamePhone(e);
      this.validUserPhone(e);
    },
    //控制relatives Phone只能输入数字
    relativesPhoneInput(e) {
      this.parentsError = false;
      this.FriendsError = false;
      // if (e.length >=6) {
      //   e = this.deleteFirstZero(e)
      // }
      this.formData.relativesPhone = e.replace(/[^\d]/g, "")
    },
    relativesPhoneKeyup() {
      this.parentsError = false;
      this.FriendsError = false;
    },
    //控制Friends Phone No.只能输入数字
    friendsphoneInput(e) {
      this.parentsError = false;
      this.FriendsError = false;
      // if (e.length >=6) {
      //   e = this.deleteFirstZero(e)
      // }
      this.formData.friendsPhone = e.replace(/[^\d]/g, "");
    },
    friendsPhoneKeyup() {
      this.parentsError = false;
      this.FriendsError = false;
    },
    /**
     * @param {string} jump 跳到错误标签的id
     * @param {string} msg 错误提示信息
     */
     jumpAndToastMsg(jump, msg, flag) {
      console.log(jump)
      if (flag === 'phone') {
        this.$newToast("Contact 1 and 2 cannot have the same cell phone No. please replace");
      } else {
        this.$newToast(`${msg}, please replace.`);
      }
      document.getElementById(jump).scrollIntoView();
    },

    findJumpIndex(repeatKey) {
      let jump = ''
      for (const key of repeatKey) {
        switch (key) {
          case 'Relatives Name':
            this.parentsNameError = true
            jump = "d_jump_2"
            break
          case 'Relatives Phone No.':
            this.parentsError = true
            jump = "d_jump_3"
            break
          case 'Friends Name':
            this.FriendsNameError = true
            jump = "d_jump_5"
            break
          case 'Friends Phone No.':
            this.FriendsError = true
            jump = "d_jump_6"
            break
          default:
        }
      }
      return jump
    },

    //前往下一步
    goNext() {
      const _this = this;
      _this.report('update_contact_submit_click');
      _this.$refs.form.validateWithInfo().then(({ isValid, errors }) => {
        //校验手机号是否相同
        const [repeatPhone, errPhone] = phoneMap.findAllRepeat()
        if (repeatPhone.length > 0) {
          const jump = _this.findJumpIndex(repeatPhone)
          // jump存在，说明这一步的电话号码于其他步骤的存在冲突
          if (jump) {
            _this.jumpAndToastMsg(jump, errPhone, 'phone')
            return false
          }
        }
        
        //如果表单验证通过
        if (isValid) {
          this.submit()
        } else {
          //如果表单验证不通过，需要锚点到第一个报错的位置
          //获取到第一个报错的testName
          const errorTestNames = Object.keys(errors)
          const errorObj = _.find(_this.testName, (item) => errorTestNames.indexOf(item.name) !== -1 && errors[item.name].length)
          console.log(errors)
          // //遍历数组，比较testName，取到报错的第一项的下标
          const errorIndex = !_.isEmpty(errorObj) ? errorObj.index : 1;
          const errorMsg = errors[errorObj.name][0]
          console.log(errorTestNames, errorObj, errorMsg)
          // //得到的index是字符串，将字符串转数值
          const errorNumberIndex = parseInt(errorIndex);
          //调用方法，锚点到第一个报错位置
          jumpIndex(errorNumberIndex);
          //弹出错误提示信息
          this.$newToast(errorMsg);
        }
      })
      .catch((e) => {
        //提示错误
        console.log(e);
      });
    },
    submit() {
      if (this.validSamePhone() && this.validUserPhone()) {
        this.handleSubmitInfo()
      }
    },
    handleSubmitInfo() {
      this.$loading();
      return  new Promise((resolve, reject) => {
        this.$http(api.profileInfo.saveContactList, {
          data: {
            contactList: JSON.stringify([
              {
                name: this.formData.relativesName,
                mobile: this.formData.relativesPhone,
                relation: this.key.relation,
                category: 'F',
                contactName: this.formData.relativesContactName,
                contactMobile: this.formData.relativesContactMobile
              },
              {
                name: this.formData.friendsName,
                mobile: this.formData.friendsPhone,
                relation: this.key.other,
                category: 'O',
                contactName: this.formData.friendsContactName,
                contactMobile: this.formData.friendsContactMobile
              }
            ])
          }
        }).then(res => {
          this.$hideLoading();
          // 还需要补充人脸信息则跳转到人脸。
          this.$store.commit('SET_NEEDRECREDITCONTRACTLIST', 'N'); // 更新紧急联系人信息提交状态
          if (this.needReCreditliveDetection === 'Y') {
            this.$router.push({
              path: '/faceVerification'
            });
          } else {
            this.$router.go(-1);
          }
          resolve(res);
        }).catch(e => {
          reject(e);
        })
      })
    },
    // 去掉首位0
    deleteFirstZero(value) {
      value = value.toString(); // 将数字转换为字符串
      if (value.charAt(0) === '0') {
        value = value.substr(1);
      }
      return value
    },
    chooseSystemContact(type) {
      window['openSystemContactCallBack'] = (res) => {
        const result = JSON.parse(decode(res));
        console.log('result', result)
        if (result.isEnable === true) {
          // 使用客户端选择的数据覆盖当前数据
          if (result.contactsName && result.contactsPhone) {
            const contactsName = this.checkName(result.contactsName);
            const contactsPhone = this.checkPhone(result.contactsPhone);
            if (contactsPhone && contactsName) {
              // 设置值
              this.formData[`${type}Phone`] = contactsPhone;
              this.formData[`${type}ContactMobile`] = contactsPhone;
              this.formData[`${type}Name`] = contactsName;
              this.formData[`${type}ContactName`] = contactsName;
              // 设置是否可以输入
              this[`${type}Disabled`] = false;
              // 清除错误
              if (type === 'relatives') {
                this.parentsError = false;
                this.parentsNameError = false;
              } else {
                this.FriendsError = false;
                this.FriendsNameError = false;
              }
            } else {
              // 设置值
              this.formData[`${type}Phone`] = '';
              this.formData[`${type}ContactMobile`] = '';
              this.formData[`${type}Name`] = '';
              this.formData[`${type}ContactName`] = '';
              // 设置是否可以输入
              this[`${type}Disabled`] = true;
              // 设置报错
              if (type === 'relatives') {
                this.parentsError = true;
                this.parentsNameError = true;
              } else {
                this.FriendsError = true;
                this.FriendsNameError = true;
              }
            }
          }
        }
      }
      openSystemContact('openSystemContactCallBack')
      console.log('chooseSystemContact')
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'updateEmergencyContact',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    // 名称检查
    checkName(name) {
      if (name) {
        // 替换字符
        let str = name.replace(/[^a-zA-Z0-9 ]/g, "").trim();
        return str;
      } else {
        return ''
      }
    },
    // 电话号码检查
    checkPhone(phone) {
      if (phone) {
        let newPhone = phone.replace(/\s+/g, '');
        if (newPhone.length >= 11) {
          // 这里是因为有国家码，所有从后往前取10位。
          newPhone = `0${newPhone.substring(newPhone.length-10)}`
        } else if (newPhone.length === 10) {
          newPhone = `0${newPhone}`;
        }
        if (/^(0[7-9][0-1])\d{8}$/.test(newPhone)) {
          return newPhone;
        } 
        return ''
      } else {
        return ''
      }
    }
  }
};