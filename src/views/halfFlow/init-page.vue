<template>
  <div class="init-page">
    <van-overlay :show="loading">
      <van-loading type="spinner" size="40px" />
    </van-overlay>
  </div>
</template>

<script>
// import { gotoHomeActivity, getLocation, isPhone, sdkAxios, showToast } from "@/assets/js/native";
import { gotoHomeActivity,  isPhone} from "@/assets/js/native";
// import api from "@/api/interface";
import { mapState } from 'vuex';
import api from "@/api/interface";
export default {
  computed: {
    ...mapState(['productSource', 'deviceType'])
  },
  data() {
    return {
      loading: true,
      timeout: false,
      timer: null,
      params: { }
    };
  },
  beforeCreate() {

  },
  created() {
    //这个页面作为过渡页面
    //只能手机号进入页面
    let vm = this;
    console.log('deviceType', vm.productSource, vm.deviceType);
    console.log(isPhone(), 'isPhone()')
    if (String(isPhone()) === 'false') {
      vm.$toast({
        message: '<div style="width: 200px">No mobile number, please bind try again</div>',
        type: 'html'
      });
      setTimeout(() => {
        gotoHomeActivity('initPage');
      }, 800)
    } else {
      console.log('打印');
      vm.login();
      vm.timer = setTimeout(() => {
        if (!vm.timeout) {
          console.log('30s后,接口依然无响应退出应用')
          clearTimeout(vm.timer)
          gotoHomeActivity('initPage');
        } else {
          console.log('30s后,接口响应')
          clearTimeout(vm.timer)
        }
      }, 30000)
    }
  },
  methods: {
    initPageReport(eventName, error) {
      let vm = this;
      vm.$store.dispatch('acReportEvent', {
        page: 'initPage',
        eventName: eventName,
        param: {
          arg1: error
        }
      });
    },
    login() {
      let vm = this;
      if (window.dopplerLib && window.dopplerLib.setAppId) {
        console.log('url地址：', location.href)
        window.dopplerLib.setAppId(vm.productSource); //传入产品来源
        window.loginCallBack = result => {
          console.log('调setAppId成功', result, JSON.stringify(result));
          const res = JSON.parse(result);
          if (res.code === 0) {
            // 这里需要清楚返回的计时器，否则会自动返回的。
            vm.timeout = true;
            clearTimeout(vm.timer);
            console.log(res, '调loginCallBack成功');
            // vm.initPageReport('nc_init_page_login_success');
            vm.queryStageInfo().then((res) => { // 从AC跳转过来的时候，我们只需要判断：是否有在途借据，是否有冻结状态，是否有授信。AC会拦截所有缺省资料未填写的用户。
              // vm.initPageReport('nc_init_page_query_stage_info_success');
              if (res.creditStage === '9') { //9 已授信
                // vm.initPageReport('nc_init_page_credit_stage_success');
                if (vm.productSource === 'palmcredit') {
                  vm.processForPc(res);
                } else {
                  vm.queryLoanList4nc().then(() => {
                    vm.loading = false;
                  }).catch(() => {
                    vm.loading = false;
                  }); // 需要判断是否有在途借据，有则跳转还款详情，无则跳转提现页
                }
              } else if (res.creditStage === '7') { //7 审核失败,不需要查询冻结
                vm.$router.push({path: '/rejectResult'});
              } else { // 剩余的其他状态
                vm.loading = false;
                vm.$router.push({path: '/creditResult'});
              }
            }).catch(() => {
              // vm.loading = false;
            });
          } else {
            //假设登录失败，重新调登录方法，实现重新登录
            vm.login();
            console.log(res, '调loginCallBack失败');
          }
        }
      } else {
        // vm.login();
        console.log('调用setAppId失败');
      }
    },
    // 获取pcx用户信息
    queryCustInfo(custId) {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http('/ims-service/cust/queryCustInfo', {
          showErrorToast: false,
          data: custId
        }).then(res => {
          console.log('pc用户信息', res);
          resolve(res.custId);
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          // vm.$toast({
          //   message: `${e.code}:${e.msg}`
          // });
          reject(e);
        });
      });
    },
    // 获取当前用户的总借款列表。
    queryLoanList() {
      let vm = this;
      return new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryLoanList4nc, {
          data:{
              "loanStatus":"a",
              "term": 0
          },
          page:{
              "isNext":false,
              "pageNum":0,
              "pageSize":100,
              "startIndex":0,
              "totalPage":0,
              "totalRecord":0
          }
        }).then(res => {
          let loan = {};
          let len = res.loanList.length;
          if (len > 0) {
            res.loanList.sort((a, b) => {
              let res = a.loanId - b.loanId;
              if (res > 0) {
                return -1
              } else if (res < 0) {
                return 1;
              } else {
                return 0;
              }
            });
            loan = res.loanList[0];
          }
          resolve(loan);
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    },
    // 查询PCX用户下的借据。
    queryPcxPlanListByCustId(custId) {
      let vm = this;
      return new Promise((resolve, reject) => {
        vm.$http('/cfk-service/nonfinancial/queryPlanListByCustId', {
          data: {
            custId: custId
          }
        }).then(res => {
          let loan = {};
          let len = res.length;
          if (len > 0) {
            loan = res[0];
          }
          resolve(loan);
        }).catch(e => {
          reject(e);
        });
      })
    },
    // PC流程（PC/NC合并）
    processForPc(res) {
      const vm = this;
      localStorage.setItem('xcorssHeader', JSON.stringify({
        'X-CID': res.custId // bvn
      }))
      // vm.initPageReport('init_page_credit_stage_success');
      vm.queryCustInfo(res.custId).then((custId) => {
        // vm.initPageReport('init_page_query_cust_info_success');
        Promise.all([
          vm.queryPcxPlanListByCustId(custId),
          vm.queryLoanList()
        ]).then((arr) => {
          console.log('allArr', arr);
          let [pcxLoan, pcLoan] = arr;
          console.log('pcxLoan, pcLoan', pcxLoan, pcLoan);
          if (pcxLoan.loanStatus === 'N' || pcxLoan.loanStatus === 'O' || pcxLoan.loanStatus === 'A' || pcxLoan.loanStatus === 'P' || pcxLoan.loanStatus === 'U')  { // 判断是否存在pcx在途借据, 存在则返回首页
            vm.$toast({
              message: `<div style="width: 240px">Already have a loan, please try another product.</div>`,
              type: 'html'
            });
            // vm.initPageReport('init_page_pcx_have_loan');
            vm.loading = false;
            setTimeout(function() {
              gotoHomeActivity('initPage');
            }, 2000);
          } else { // 否则走正常pc的逻辑。
            vm.loading = false;
            if (pcLoan.loanStatus === 'N' || pcLoan.loanStatus === 'O' || pcLoan.loanStatus === 'P') { // N 正常在途借据 O 逾期 P 待处理的借据
              // vm.initPageReport('init_page_pc_have_loan');
              vm.$router.push({ path: '/repaymentIndex', query: {
                loanId: pcLoan.loanId,
                loanType: 'loanSingle'
              }});
            } else if (pcLoan.loanStatus === 'A' || pcLoan.loanStatus === 'U') { // A 申请中借据 U // 放款中的借据
              vm.$router.push({ path: "/withdrawStatus", query: {
                data: JSON.stringify({
                        status: true
                      })
              }});
            } else if (pcxLoan.loanStatus || pcLoan.loanStatus) { // 之前至少存在一笔借据，跳转提现页
              // vm.initPageReport('init_page_into_withdraw');
              vm.$router.push({path: '/'});
            } else { // 新客，不存在借据时，退出。
              vm.$toast({
                message: `<div style="width: 240px">The system is wrong, please try another product.</div>`,
                type: 'html'
              });
              // vm.initPageReport('init_page_no_loan_user');
              vm.loading = false;
              setTimeout(function() {
                gotoHomeActivity('initPage');
              }, 2000);
            }
          }
        }).catch((e) => {
          vm.loading = false;
          console.log(e);
          // vm.initPageReport('init_page_loan_list_failed', JSON.stringify(e).substring(0,200));
        });
      }).catch((e) => {
        if (e.code == 1202001) { //pc用户不是pcx的用户, 会出现这种报错。
          vm.queryLoanList().then((pcLoan) => {
            vm.loading = false;
            if (pcLoan.loanStatus === 'N' || pcLoan.loanStatus === 'O' || pcLoan.loanStatus === 'P') { // N 正常在途借据 O 逾期 P 待处理的借据
              // vm.initPageReport('init_page_pc_have_loan');
              vm.$router.push({ path: '/repaymentIndex', query: {
                loanId: pcLoan.loanId,
                loanType: 'loanSingle'
              }});
            } else if (pcLoan.loanStatus === 'A') { // A 审批中借据
              vm.$router.push({ path: "/withdrawStatus", query: {
                data: JSON.stringify({
                        status: true
                      })
              }});
            } else if (pcLoan.loanStatus) { // 之前至少存在一笔借据，跳转提现页
              // vm.initPageReport('init_page_into_withdraw');
              vm.$router.push({path: '/'});
            } else { // 新客，不存在借据时，退出。
              vm.$toast({
                message: `<div style="width: 240px">The system is wrong, please try another product.</div>`,
                type: 'html'
              });
              // vm.initPageReport('init_page_no_loan_user');
              vm.loading = false;
              setTimeout(function() {
                gotoHomeActivity('initPage');
              }, 2000);
            }
          });
        } else {
          // vm.initPageReport('init_page_query_cust_info_failed', JSON.stringify(e).substring(0,200));
        }
        console.log(e);
      });
    },
    // 查询账户冻结状态
    queryCustFinancialStatus4nc() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryCustFinancialStatus4nc, {}).then(res => {
          resolve(res);
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          console.log(e);
          // vm.$toast({
          //   message: `${e.code}:${e.msg}`
          // });
          reject(e);
        });
      })
    },
    // 获取用户授权等信息
    queryStageInfo() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryStageInfo4nc, {
          showErrorToast: false
        }).then(res => {
          vm.$store.commit('SET_USERINFOR', res);
          vm.timeout = true;
          resolve(res);
        }).catch(e => {
          vm.timeout = false;
          reject(e);
        });
      })
    },
    // 获取当前用户的总借款列表。
    queryLoanList4nc() {
      let vm = this;
      return new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.queryLoanList4nc, {
          data:{
              "loanStatus":"a",
              "term":0
          },
          page:{
              "isNext":false,
              "pageNum":0,
              "pageSize":10000,
              "startIndex":0,
              "totalPage":0,
              "totalRecord":0
          }
        }).then(res => {
          let loan = {};
          let len = res.loanList.length;
          if (len > 0) {
            res.loanList.sort((a, b) => {
              let res = a.loanId - b.loanId;
              if (res > 0) {
                return -1
              } else if (res < 0) {
                return 1;
              } else {
                return 0;
              }
            });
            loan = res.loanList[0];
          }
          if (loan.loanStatus === 'N' || loan.loanStatus === 'O' || loan.loanStatus === 'P') { // N 正常在途借据 O 逾期 A 审批中借据 P 待处理的借据
            // vm.initPageReport('nc_init_page_have_loan');
            vm.$router.push({ path: '/loanDetail', query: {
              loanId: loan.loanId
            }});
          } else if (loan.loanStatus === 'A' || loan.loanStatus === 'U') { // A 审批中借据 U 放款中
            vm.$router.push({ path: "/withdrawStatus", query: {
              data: JSON.stringify({
                      status: true
                    })
            }});
          } else if (loan.loanStatus) { // 之前存在已完成的借据，可以进入提现页。
            // vm.initPageReport('nc_init_page_into_withdraw');
            vm.$router.push({path: '/'});
          } else { // 之前没有任何借据，不允许进入提现页。
            console.log('loan.loanStatus', loan.loanStatus);
            // vm.initPageReport('nc_init_page_no_loan_user');
            vm.$toast({
              message: `<div style="width: 240px">The system is wrong, please try another product.</div>`,
              type: 'html'
            });
            setTimeout(function() {
              gotoHomeActivity('initPage');
            }, 2000);
          }
          // console.log(res);
          // vm.detail = {
          //   ...res,
          //   ...res.loanList[0]
          // };
          // let loans = res.loanList.map(item => {
          //   return { 
          //     loanId: item.loanId,
          //     term: item.loanTerm
          //    }
          // })
          // vm.activeRepaymentCalc4nc({
          //   loans: loans
          // });
          // console.log(vm.detail);
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      let fromName = from.name;
      console.log(vm);
      // 处理返回
      if (fromName === 'rejectResult' || fromName === 'creditResult' || fromName === 'repaymentIndex' 
      || fromName === 'index'|| fromName === 'loanDetail') {
        gotoHomeActivity('initPage');
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.init-page {
  ::v-deep.van-overlay {
    z-index: 999;
    background-color: #fff;
  }
  ::v-deep .van-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
</style>
