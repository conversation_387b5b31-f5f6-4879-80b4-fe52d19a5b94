import { gotoHomeActivity } from "@/assets/js/native";

export default {
  components: {
  },
  data() {
    return {
      timer: null,
      count: "",
      freezeMsg: ''
    }
  },
  watch: {},
  computed: {},
  created() {
    this.freezeMsg = this.$route.query.freezeMsg
  },
  mounted() {
    const TIME_COUNT = 3
    this.count = TIME_COUNT
    this.timer = setInterval(() => {
      if (this.count > 0 && this.count <= TIME_COUNT) {
        this.count--;
      } else {
        clearInterval(this.timer);
        this.timer = null;
        gotoHomeActivity('rejectResult');
      }
    }, 1000);
  },
  methods: {
    goPage() {
      clearInterval(this.timer);
      gotoHomeActivity('rejectResult');
    }
  }
}