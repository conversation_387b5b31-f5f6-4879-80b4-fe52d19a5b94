import { gotoHomeActivity } from "@/assets/js/native";
import api from "@/api/interface";
export default {
  components: {
  },
  data() {
    return {
      crdStage: "",
      userInfo: "",
      bvnName: "",
      info: "",
      creditLmt: "",
      lmtAmt: false,
      timer: null,
      timer2: null,
      payMask: true,
      count: "",
      viewTime: null,
      uid: ''
    };
  },
  watch: {},
  computed: {},
  methods: {
    // 获取用户授权等信息
    queryStageInfo() {
      let self = this;
      return  new Promise((resolve, reject) => {
        self.$http(api.commonBlc.queryStageInfo4nc, {}).then(res => {
          self.$store.commit('SET_USERINFOR', res);
          if (res.creditStage === '9') { //9 已授信
            self.$router.push({path: '/'});
          } else if (res.creditStage === '7') { //7 审核失败
            self.queryCustFinancialStatus4nc().then((res) => {
              self.$router.push({path: '/rejectResult', query: {
                freezeMsg: res.freezeMsg
              }});
            });
          } else {
            // 若仍然没有最终授信结果，始终停留在当前页去轮询，除非用户自动退出。
            self.timer = setInterval(() => {
              clearInterval(self.timer);
              self.timer = '';
              self.queryStageInfo();
            }, 2000);
          }
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      })
    },
    setTime(val) {
      let self = this;
      const TIME_COUNT = val
      self.count = TIME_COUNT
      self.viewTime = setInterval(() => {
        if (self.count > 0 && self.count <= TIME_COUNT) {
          self.count--;
        } else {
          clearInterval(self.viewTime);
          self.viewTime = null;
          self.payMask = false
        }
      }, 1000);
    },
    goBack(index) {
      let self = this;
      if (index === 1) {
        if (!self.payMask) {
          clearInterval(self.viewTime);
          self.payMask = true
          self.setTime(20)
        }
      } else {
        clearInterval(self.viewTime);
        gotoHomeActivity('creditResult');
      }
    }
  },
  created() {
    this.queryStageInfo();
  },
  mounted() {
    this.setTime(20)
  },
  beforeDestroy() {
    let self = this;
    clearInterval(self.timer);
    clearInterval(self.timer2);
    clearInterval(self.viewTime);
  },
};