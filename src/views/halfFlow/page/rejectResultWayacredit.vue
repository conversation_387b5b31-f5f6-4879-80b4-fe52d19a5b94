<template>
  <div class="reject-result">
    <img src="@/assets/images/wayacredit/bg-fail.png" />
    <h1>Loan rejecture</h1>
    <p class="header-text" v-if="freezeMsg">{{ freezeMsg }}</p>
    <p class="header-text" v-else>Loan amount is full</p>
    <button class="c-button" @click="goPage">
      {{ `Back Home ( ${count} s)` }}
    </button>
  </div>
</template>

<script>
import publicMixns from "../rejectResultMixin.js";
export default {
  name: "rejectResultWayacredit",
  mixins: [publicMixns],
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.reject-result {
  height: 100%;
  background: #1e1e20;
  padding-top: 50px;
  box-sizing: border-box;
  img {
    width: 88px;
    margin: auto;
    margin-top: 26px;
  }

  h1 {
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    color: #fff;
    white-space: nowrap;
    margin: 15px 0 20px;
  }
  p {
    font-size: 28px;
    text-align: center;
    color: #919db3;
    margin-bottom: 100px;
  }
  .c-button {
    width: calc(100% - 44px);
    height: 45px;
    line-height: 45px;
    color: #282A30;
    text-align: center;
    font-family: Noto Sans;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    border-radius: 10px;
    background: $themeColor;
    border: none;
  }
  .bottom-2 {
    color: $themeColor;
    margin-top: 15px;
    font-size: 24px;
    color: $themeColor;
    text-align: center;
  }
  .header-text {
    font-size: 12px;
    text-align: center;
    color: rgba(255, 255, 255, 0.70);
    line-height: 18px;
    margin: 12px 0;
  }
}
</style>
