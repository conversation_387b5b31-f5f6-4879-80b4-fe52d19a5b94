<template>
  <div>
    <div class="credit-result c-page-24">
      <img src="@/assets/images/3.png">
      <h1>Loan rejecture</h1>
      <p class="header-text" v-if="freezeMsg">{{ freezeMsg }}</p>
      <p class="header-text" v-else>Loan amount is full</p>
      <button class="c-button" @click="goPage">{{ `Back Home ( ${ count } s)` }}</button>
    </div>
  </div>
</template>

<script>
import publicMixns from '../rejectResultMixin.js'
export default {
  name: 'rejectResultBase',
  mixins: [publicMixns],
  data() {
    return {

    }
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.credit-result {
  &.c-page-24 {
      margin: 0 24px;
      padding-bottom: 70px;
  }
   > img {
    width: 242px;
    margin: auto;
    margin-top: 56px;
      &.failed {
        width: 242px;
      }
   }

  > h1 {
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    color: #1b3155;
    white-space: nowrap;
    margin: 15px 0 20px;
  }
  > p {
    font-size: 28px;
    text-align: center;
    color: #919db3;
    margin-bottom: 100px;
  }
  .c-button{
    width: 80%;
    height: 45px;
    line-height: 45px;
    font-size: 18px !important;
    color: #ffffff !important;
    border-radius: 20px;
    background: $themeColor;
    border: none;
  }
  .bottom-2{
    color: $themeColor;
    margin-top: 15px;
    font-size: 24px;
    color: $themeColor;
    text-align: center;
  }
  .header-text {
    font-size: 12px;
    text-align: left;
    color: #919db3;
    line-height: 18px;
    margin: 12px 0;
  }
}
</style>
