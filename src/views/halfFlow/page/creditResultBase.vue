<template>
  <div>
    <div class="credit-result c-page-24">
      <img src="@/assets/images/4.png">
      <h1>Under Review</h1>
      <p class="header-text">Please wait patiently...</p>
      <button class="c-button" :class="{ 'disable': payMask }" @click="goBack(1)">
        {{
          `Refresh Result ${ payMask ? `( ${ count } s)` : '' }`
        }}</button>
      <h2 class="bottom-2" @click="goBack(2)">Back Home</h2>
    </div>
  </div>
</template>

<script>
import publicMixns from '../creditResultMixin.js'
export default {
  name: 'creditResultBase',
  mixins: [publicMixns],
  data() {
    return {

    }
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.credit-result {
  &.c-page-24 {
      margin: 0 24px;
      padding-bottom: 70px;
  }
  > img {
    width: 242px;
    margin: auto;
    margin-top: 56px;

    &.failed {
      width: 242px;
    }
  }

  > h1 {
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    color: #1b3155;
    white-space: nowrap;
    margin: 15px 0 20px;
  }

  > p {
    font-size: 28px;
    text-align: center;
    color: #919db3;
    margin-bottom: 100px;
  }

  > em {
    margin-top: 30px;
    text-align: center;
    color: $themeColor;
    font-size: 30px;
  }
  .c-button{
    width: 100%;
    height: 45px;
    line-height: 45px;
    font-size: 18px !important;
    color: #ffffff !important;
    border-radius: 20px;
    background: $themeColor;
    border: none;
    // box-shadow: 0 2px 4px 0 rgb(109 90 8 / 36%);
    .disable {
      background: $themeColor;
      cursor: not-allowed;
      pointer-events: none;
      box-shadow: none;
    }
  }
  .bottom-2{
    color: $themeColor;
    margin-top: 15px;
    font-size: 24px;
    color: $themeColor;
    text-align: center;
  }
  .header-text {
    font-size: 12px;
    text-align: left;
    color: #919db3;
    line-height: 18px;
    margin: 12px 0;
  }
}
.report-text {
  margin-top: 40px;
  font-size: 28px;
  text-align: center;
  color: $themeColor;
  cursor: pointer;
}
</style>
