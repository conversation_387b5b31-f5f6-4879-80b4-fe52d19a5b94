<template>
  <div class="credit-result">
    <img src="@/assets/images/wayacredit/bg-pending.png" />
    <h1>Under Review</h1>
    <p class="header-text">Please wait patiently...</p>
    <button class="c-button" :class="{ disable: payMask }" @click="goBack(1)">
      {{ `Refresh Result ${payMask ? `( ${count} s)` : ""}` }}
    </button>
    <h2 class="bottom-2" @click="goBack(2)">Back Home</h2>
  </div>
</template>

<script>
import publicMixns from "../creditResultMixin.js";
export default {
  name: "creditResultWayacredit",
  mixins: [publicMixns],
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.credit-result {
  height: 100%;
  background: #1e1e20;
  padding-top: 50px;
  box-sizing: border-box;
  img {
    width: 88px;
    margin: auto;
    margin-top: 26px;
  }

  h1 {
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    color: #fff;
    white-space: nowrap;
    margin: 15px 0 20px;
  }

  p {
    font-size: 28px;
    text-align: center;
    color: #919db3;
    margin-bottom: 100px;
  }

  em {
    margin-top: 30px;
    text-align: center;
    color: $themeColor;
    font-size: 30px;
  }
  .c-button {
    width: calc(100% - 44px);
    height: 45px;
    line-height: 45px;
    font-size: 18px !important;
    color: #282A30;
    border-radius: 10px;
    background: $themeColor;
    border: none;
    .disable {
      background: $themeColor;
      cursor: not-allowed;
      pointer-events: none;
      box-shadow: none;
    }
  }
  .bottom-2 {
    color: $themeColor;
    margin-top: 15px;
    font-size: 24px;
    color: $themeColor;
    text-align: center;
  }
  .header-text {
    font-size: 12px;
    text-align: center;
    color: rgba(255, 255, 255, 0.70);
    line-height: 18px;
    margin: 12px 0;
  }
}
.report-text {
  margin-top: 40px;
  font-size: 28px;
  text-align: center;
  color: $themeColor;
  cursor: pointer;
}
</style>
