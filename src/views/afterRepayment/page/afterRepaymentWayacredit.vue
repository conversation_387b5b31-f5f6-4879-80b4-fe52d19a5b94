<template>
  <div class="wrap">
    <CPage
      :className="['after-repayment-status', nowStatus]"
      v-if="statusObj[nowStatus]"
    >
      <div class="status">
        <img
          :src="
            nowStatus === 'SUCC'
              ? require(`@/assets/images/wayacredit/bg-success.png`)
              : nowStatus === 'FAIL'
              ? require('@/assets/images/wayacredit/bg-fail.png')
              : require('@/assets/images/wayacredit/bg-pending.png')
          "
          alt=""
        />
        <div v-text="statusObj[nowStatus].statusName"></div>
      </div>
      <div
        class="tip-content"
        :class="nowStatus"
        @click="contactUs($event)"
        v-html="statusObj[nowStatus].tipContent"
      ></div>
      <CButton
        @buttonClick="goNext()"
        className="button"
        :name="`${statusObj[nowStatus].mainButton} (${count}s)`"
      ></CButton>
      <div
        class="side-content"
        v-text="statusObj[nowStatus].sideButton"
        @click="backHomePage()"
      ></div>
      <van-dialog
        v-model="showDialog"
        show-cancel-button
        className="dialog-after-repayment"
        :showConfirmButton="false"
        :showCancelButton="false"
      >
        <div class="title">Note!</div>
        <div class="tips">
          You can apply for the next payment only after the loan is settled.
          Please pay attention to open the App after 30 minutes to check the
          repayment result.
        </div>
        <div class="dia-button">
          <div class="ok" @click="backHomePage()">OK</div>
          <div class="cancel" @click="cancelDia()">Cancel</div>
        </div>
      </van-dialog>
      <!-- 帮助中心入口 -->
      <Question @question="startQuestion"></Question>
    </CPage>
  </div>
</template>

<script>
import publicMixns from "../afterRepaymentMixins.js";
export default {
  name: "afterRepaymentWayacredit",
  mixins: [publicMixns],
  data() {
    return {};
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.wrap {
  background: #1E1E20;
  height: 100%;
  padding-top: 30px;
}
.after-repayment-status {
  &.c-page {
    width: calc(100% - 24px);
    background: transparent;
    margin-top: 0;
  }
  .status {
    img {
      width: 88px;
      height: 88px;
    }
    div {
      font-family: Avenir, Avenir-Medium;
      text-align: center;
      line-height: 33px;
      margin-top: 5px;
      font-size: 20px;
      color: #fff;
      font-weight: 800;
    }
  }
  .tip-content {
    margin-bottom: 60px;
    margin-right: 0;
    font-family: Avenir-Medium;
    font-size: 14px;
    color: #919db3;
    line-height: 20px;
    font-weight: 500;
    text-align: left;
    padding: 0 10px;
    &.PENDING {
      margin-bottom: 30px;
    }
  }
  .suc-button {
    margin-top: 50px;
  }
  .button {
    background: $themeColor;
    color: #1E1E20;
    width: calc(100% - 22px);
    left: 44px;
  }
  .side-content {
    font-size: 14px;
    font-family: Avenir, Avenir-Medium;
    font-weight: 500;
    text-align: center;
    color: $themeColor;
    line-height: 19px;
    margin-top: 18px;
  }

  .advertisement {
    width: 325px;
    height: 83px;
    position: fixed;
    bottom: 10px;
    left: 17px;
  }
  .dialog-after-repayment {
    color: #536887;
    padding: 12px 15px;
    width: 254px;
    .van-dialog__content {
      .title {
        height: 40px;
        line-height: 40px;
        text-align: left;
        margin-bottom: 5px;
        color: #536887;
        font-weight: 700;
      }
      .tips {
        line-height: 20px;
        text-align: left;
      }
      .dia-button {
        margin-top: 15px;
        .ok {
          height: 40px;
          line-height: 40px;
          background: $themeColor;
          color: #ffffff;
          border-radius: 5px;
        }
        .cancel {
          height: 40px;
          line-height: 40px;
          background: #ffffff;
          color: $themeColor;
          border-radius: 5px;
        }
      }
    }
    .van-dialog__content--isolated {
      min-height: 70px;
    }
    .van-dialog__message--left {
      padding: 15px 24px;
    }
  }
}
</style>
