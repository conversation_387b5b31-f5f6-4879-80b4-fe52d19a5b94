<template>
  <div class="wrap">
    <CPage
      :className="['after-repayment-status', nowStatus]"
      v-if="statusObj[nowStatus]"
    >
      <div class="status">
        <img
          :src="
            nowStatus === 'SUCC'
              ? require(`@/assets/images/${productSource}/bg-success.png`)
              : nowStatus === 'FAIL'
              ? require(`@/assets/images/${productSource}/bg-fail.png`)
              : require(`@/assets/images/${productSource}/bg-pending.png`)
          "
          alt=""
        />
        <div v-text="statusObj[nowStatus].statusName"></div>
      </div>
      <div
        class="tip-content"
        :class="nowStatus"
        @click="contactUs($event)"
        v-html="statusObj[nowStatus].tipContent"
      ></div>
      <CButton
        @buttonClick="goNext()"
        className="button"
        :name="`${statusObj[nowStatus].mainButton} (${count}s)`"
      ></CButton>
      <div
        class="side-content"
        v-text="statusObj[nowStatus].sideButton"
        @click="backHomePage()"
      ></div>
      <van-dialog
        v-model="showDialog"
        show-cancel-button
        className="dialog-after-repayment"
        :showConfirmButton="false"
        :showCancelButton="false"
      >
        <div class="title">Note!</div>
        <div class="tips">
          You can apply for the next payment only after the loan is settled.
          Please pay attention to open the App after 30 minutes to check the
          repayment result.
        </div>
        <div class="dia-button">
          <div class="ok" @click="backHomePage()">OK</div>
          <div class="cancel" @click="cancelDia()">Cancel</div>
        </div>
      </van-dialog>
    </CPage>
    <!-- 帮助中心入口 -->
    <Question @question="startQuestion"></Question>
  </div>
</template>

<script>
import publicMixns from "../afterRepaymentMixins.js";
export default {
  name: "afterRepaymentCommon1",
  mixins: [publicMixns],
  data() {
    return {};
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.wrap {
  background: $color;
  height: 100%;
  padding-top: 30px;
}
.after-repayment-status {
  &.c-page {
    width: calc(100% - 24px);
    background: transparent;
    margin-top: 0;
  }
  .status {
    img {
      width: 88px;
      height: 88px;
    }
    div {
      font-family: Avenir, Avenir-Medium;
      text-align: center;
      line-height: 33px;
      margin-top: 5px;
      font-size: 20px;
      color: #fff;
      font-weight: 800;
    }
  }
  .tip-content {
    margin-bottom: 60px;
    margin-right: 0;
    font-family: Avenir-Medium;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.70);
    line-height: 20px;
    font-weight: 500;
    text-align: left;
    padding: 0 10px;
    &.PENDING {
      margin-bottom: 30px;
    }
    &>span {
      color: rgba(255, 107, 0, 1) !important;
    }
  }
  .suc-button {
    margin-top: 50px;
  }
  .button {
    border-radius: 50px;
    background: #FFF;
    color: #1E1E20;
    width: calc(100% - 22px);
    left: 44px;
    color: #0F2388;
    font-family: HarmonyOS Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
  }
  .side-content {
    font-family: HarmonyOS Sans SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    text-align: center;
    color: #fff;
    line-height: 19px;
    margin-top: 18px;
  }

  .advertisement {
    width: 325px;
    height: 83px;
    position: fixed;
    bottom: 10px;
    left: 17px;
  }
  .dialog-after-repayment {
    color: #536887;
    padding: 12px 15px;
    width: 254px;
    .van-dialog__content {
      .title {
        height: 40px;
        line-height: 40px;
        text-align: left;
        margin-bottom: 5px;
        color: #536887;
        font-weight: 700;
      }
      .tips {
        line-height: 20px;
        text-align: left;
      }
      .dia-button {
        margin-top: 15px;
        .ok {
          height: 40px;
          line-height: 40px;
          background: $background;
          color: #ffffff;
          border-radius: 5px;
        }
        .cancel {
          height: 40px;
          line-height: 40px;
          background: #ffffff;
          color: $color;
          border-radius: 5px;
        }
      }
    }
    .van-dialog__content--isolated {
      min-height: 70px;
    }
    .van-dialog__message--left {
      padding: 15px 24px;
    }
  }
}
</style>
