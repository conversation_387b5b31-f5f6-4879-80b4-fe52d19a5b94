
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'afterRepayment',
  components: {
    baseAfterRepayment: () => import(/* webpackChunkName: "baseafR" */ './page/afterRepaymentBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "afterRepaymentWayacredit" */ './page/afterRepaymentWayacredit.vue'),
    afterRepaymentCommon1: () => import(/* webpackChunkName: "afterRepaymentCommon1" */ './page/afterRepaymentCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseAfterRepayment',
        to: '',
        from: '',
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.afterRepayment) {
      this.componentTag = globalConfig.pageStyleSetting.afterRepayment
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
