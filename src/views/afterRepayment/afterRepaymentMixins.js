import advertisement from '@/assets/images/advertisement.png';
import backImg from '@/assets/images/UBA.png';
import chat from '@/assets/images/chat.png';
import CButton from '@/components/c-button.vue';
import CPage from '@/components/c-page.vue';
import MembershipSystemDialog from './components/membership-system-dialog.vue';
import { gotoHomeActivity, gotoLoanList } from "@/assets/js/native";
import { mapState } from 'vuex';
import api from "@/api/interface";
import Question from "@/components/question.vue";

export default {
    name: 'afterRepayment',
    computed: {
        ...mapState(['productSource', 'nodeCode']),
    },
    components: {
        CPage,
        CButton,
        Question,
        MembershipSystemDialog
    },
    data() {
        return {
            backImg,
            advertisement,
            chat,
            nowStatus: '',
            statusObj: {
                SUCC: {
                    statusName: 'Successful Repayment',
                    tipContent: 'Thank you for repaying your loan on time, you have been granted a higher credit limit,Go ahead and use it.',
                    chat: false,
                    mainButton: 'Re-apply', // 回首页。
                    sideButton: 'Go Home'
                },
                FAIL: {
                    statusName: 'Repayment Failure',
                    tipContent: 'Oops, your repayment has failed! Please try again! </br> If you have any questions, please contact customer service: <span style="color: #099BFA;">Click to contact us</span>',
                    chat: true,
                    mainButton: 'Repayment', // 还款页
                    sideButton: 'Go Home'
                },
                PENDING: {
                    statusName: 'Repayment Pending',
                    tipContent: 'Your repayment order is being processed, please do not resubmit, the result will be available in 30 minutes at the latest. If you have any questions, please contact customer service: <span style="color: #099BFA;">Click to contact us</span>',
                    chat: false,
                    mainButton: 'Refresh Page', // OK回首页 cancle
                    sideButton: 'Go Home'
                }
            },
            showDialog: false,
            productType: '',
            count: 3,
            timer: null,
            showMembershipSystemDialog: false
        }
    },
    methods: {
        // PCX查询还款状态
        pcxActiveQueryPlutusPaymentResult() {
            let vm = this;
            vm.$loading();
            return vm.$http('/cfk-service/inner/v1/activeQueryPlutusPaymentResult', {
                data:{
                    "reference": vm.$route.query.reference
                }
            }).then(res => {
                vm.nowStatus = res.payStatus;
                vm.$hideLoading();
                if (vm.nowStatus === 'PENDING') {
                    vm.count = 15;
                }
            });
        },
        // 查询还款状态
        activeQueryPlutusPaymentResult() {
            let vm = this;
            vm.$loading();
            return vm.$http(api.repayment.activeQueryPlutusPaymentResult, {
                data:{
                    "reference": vm.$route.query.reference
                }
            }).then(res => {
                vm.nowStatus = res.payStatus;
                vm.$hideLoading();
                // 提交成功，设置日历。
                // setCalendar(vm.nodeCode.CALENDAR_NODE_RP, '', '');
                // console.log(res);
                // vm.nowStatus = 'FAIL';
                if (vm.nowStatus === 'PENDING') {
                    vm.count = 15;
                }
                vm.report(`afterRepayment_result_${vm.nowStatus}`)
            }).catch(() => {
                vm.report(`afterRepayment_result_failed`)
            })
        },
        // 查询用户关联信息（获取是否有在途）
        hasOnlineLoan() {
            let vm = this;
            return new Promise((resolve) => {
                vm.$http(api.repayment.hasOnlineLoan, {
                    data:{},
                    showErrorToast: false
                }).then(res => {
                    resolve(res);
                }).catch(() => {
                })
            })
        },
        action(type) {
            let vm = this;
            if (type === 'OK') {
                gotoHomeActivity('afterRepayment');
            } else if (type === 'Try Again') {
                let loanId = localStorage.getItem('repaymentLoanId');
                if (vm.productType === 'PCX') {
                    vm.$router.push({ path: '/repaymentIndex', replace: true, query: {
                        loanId: loanId,
                        loanType: 'loanInstallment'
                    }});
                } else {
                    vm.$router.push({ path: '/repaymentIndex', replace: true, query: {
                        loanId: loanId
                    }});
                }
            } else if (type === 'Loan Again') {
                vm.showDialog = true;
            }
        },
        // 返回首页
        backHomePage() {
            gotoHomeActivity('afterRepayment');
        },
        myBackFun() {
            gotoHomeActivity('afterRepayment');
        },
        // 跳转到借款列表
        gotoLoanList() {
            gotoLoanList();
        },
        cancelDia() {
            this.showDialog = false;
        },
        sideAction(type) {
            if (type === 'Back Homepage') {
                this.backHomePage();
            }
        },
        goNext() {
            let vm = this;
            if (this.nowStatus === 'SUCC') {
                clearInterval(vm.timer);
                // 间隔2s发送请求: 后台异步执行任务需要延迟一点时间。
                console.log('SUCC')
                setTimeout(function () {
                    vm.hasOnlineLoan().then(res => {
                        // 无在途借据，跳转结清复贷页。
                        const ncInfofromKc = localStorage.getItem('ncInfofromKc');
                        if (res && res.hasOnlineLoan === false && !ncInfofromKc) {
                            vm.$router.push({ path: '/afterRepaymentLead'});
                        } else {
                            vm.backHomePage();
                        }
                    }).catch(() => {
                        vm.backHomePage();
                    })
                }, 2000)
            } else if (this.nowStatus === 'FAIL') {
                clearInterval(vm.timer);
                const loanId = localStorage.getItem('repaymentLoanId');
                if (vm.productType === 'PCX') {
                    vm.$router.push({ path: '/repaymentIndex', replace: true, query: {
                        loanId: loanId,
                        loanType: 'loanInstallment'
                    }});
                } else {
                    vm.$router.push({ path: '/repaymentIndex', replace: true, query: {
                        loanId: loanId
                    }});
                }
            } else {
                clearInterval(vm.timer);
                vm.count = 15;
                vm.loadData();
            }
        },
        loadData() {
            this.productType = localStorage.getItem('productType');
            if (this.productType === 'PCX') { // PCX还款状态查询
                this.pcxActiveQueryPlutusPaymentResult().then(() => {
                    this.setInterval();
                })
            } else { // NC还款状态查询
                this.activeQueryPlutusPaymentResult().then(() => {
                    this.setInterval();
                });
            }
        },
        setInterval() {
            this.timer = setInterval(() => {
                this.count = this.count - 1;
                if (this.count == 0) {
                    clearInterval(this.timer);
                    if (!['palmcredit', 'newcredit'].includes(this.productSource)) { // 只有这两个包不需要自动跳转
                        this.goNext();
                    }
                }
            }, 1000);
        },
        contactUs(e) {
            if (e.target.localName === 'span') {
                clearInterval(this.timer);
                this.$router.push({path: '/loadUrlPage', query: {
                    url: process.env.VUE_APP_CONTACT_US_URL, 
                    title: 'Contact Us'
                }});
            }
        },
        // 埋点
        report(eventName, eventType) {
            let self = this;
            self.$store.dispatch('reportEvent', {
                page: 'afterRepayment',
                eventName: eventName,
                eventData: {
                    event_time: Date.now(),
                    event_type: eventType
                }
            });
        },
        closeMembershipSystemDialog() {
            this.showMembershipSystemDialog = false;
        },
        hideMembershipSystemDialog() {
            // 暂时回到首页
            this.showMembershipSystemDialog = false;
            this.backHomePage();
        },
        startQuestion() {
            this.report('withdraw_result_get_help_click');
            this.$router.push({
                path: '/helpCenter'
            })
        }
    },
    mounted() {
        // 页面调试链接：https://newcredit-cashloan-h5.dexintec.cn/#/afterRepayment?result=pending&reference=NC581439966489350144
        this.loadData();
        window.scrollTo(0, 0);
        this.report('afterRepayment_page_view');
        if (['palmcredit', 'newcredit'].includes(this.productSource)) { // 只有这两个包支持会员功能
            this.showMembershipSystemDialog = true;
        }
    }
};