<template>
  <div class="dialog" :class="className">
    <van-dialog v-model="showDialog" className="dialog" :closeOnClickOverlay="false" :showConfirmButton="false"
      :showCancelButton="false">
      <div class="control">
        <img class="vip" src="@/assets/images/membershipSystem/vip.png" alt="" srcset="">
        <img class="bg1" src="@/assets/images/membershipSystem/bg1.png" alt="" srcset="">
        <div class="top">
          <div class="line1">Up to <span> ₦ 4,000</span></div>
          <div class="line2">Interest Reduction Bonus</div>
        </div>
        <div class="wrap">
          <img class="bg2" src="@/assets/images/membershipSystem/bg2.png" alt="" srcset="">
          <div class="text">
            <div>{{ channel }} VIP is coming</div>
            <div v-if="mainButton" class="confirm" @click="mainAction()" v-text="mainButton"></div>
          </div>
        </div>
      </div>
      <img v-if="showClose" class="close" src="@/assets/images/inviteFriends/close.png" @click="close" alt="">
    </van-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex';

export default {
  name: 'CDialog',
  computed: {
    ...mapState(['channel', 'productSource']),
  },
  props: {
    className: [String, Array],
    mainButton: {
      type: String,
      default: ''
    },
    show: Boolean,
    showClose: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      showDialog: false
    };
  },

  methods: {
    mainAction() {
      this.$emit('mainAction');
    },
    close() {
      this.$emit('close');
    }
  },
  watch: {
    show: function(value) {
      this.showDialog = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog {
  background: none;
  .control{
    position: relative;
    z-index: 1;
    padding-top: 20px;
    width: 250px;
    height: 185px;
    margin: auto;
    .bg1{
      width: 226px;
      height: 95px;
      border-radius: 15px;
      margin: auto;
    }
    .vip{
      width: 81px;
      height: 45px;
      margin: auto;
      position: absolute;
      right: 8px;
      top: 10px;
    }
    .top{
      position: absolute;
      left: 36px;
      top: 37px;
      .line1{
        color: #FCDEA0;
        font-family: Montserrat;
        font-size: 12px;
        font-style: italic;
        font-weight: 600;
        line-height: 130%;
        text-align: left;
        span{
          color: #FCDEA0;
          text-shadow: 0px 3px 4px rgba(0, 0, 0, 0.40);
          font-family: Montserrat;
          font-size: 16px;
          font-weight: 800;
          line-height: 130%;
        }
      }
      .line2{
        color: #FCDEA0;
        font-family: Montserrat;
        font-size: 12px;
        font-style: italic;
        font-weight: 600;
        line-height: 130%;
        text-align: left;
      }
    }
  }
  .wrap{
    border-radius: 16px;
    height: 112px;
    width: 250px;
    box-sizing: border-box;
    margin: auto;
    position: absolute;
    top: 87px;
    .bg2{
      width: 250px;
      height: 111px;
    }
    .text{
      position: absolute;
      top: 21px;
      left: 0px;
      color: #22242C;
      text-align: center;
      font-family: Montserrat;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 130%;
      width: 250px;
      text-align: center;
    }
    .confirm{
        background: #22242C !important;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
        line-height: 42px;
        margin: 0 auto;
        margin-top: 23px;
        width: 220px;
        border-radius: 42px !important;
    }
  }
  .close{
    width: 16px;
    height: 16px;
    margin-top: 30px;
  }
}
</style>