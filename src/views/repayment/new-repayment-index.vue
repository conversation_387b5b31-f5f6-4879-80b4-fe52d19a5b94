
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'withdrawLoan',
  components: {
    baseRepayment: () => import(/* webpackChunkName: "baserp" */ './page/repaymentIndexBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "wayacreditRepayment" */ './page/repaymentIndexWayacredit.vue'),
    ponykash: () => import(/* webpackChunkName: "ponykashRepayment" */ './page/repaymentIndexPonykash.vue'),
    repaymentIndexCommon1: () => import(/* webpackChunkName: "repaymentIndexCommon1" */ './page/repaymentIndexCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseRepayment',
        to: '',
        from: '',
      }
  },
  created() {
    const productSource = localStorage.getItem('productSource')
    if (productSource === 'ponykash') {
      this.componentTag = 'ponykash'
    }
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.repaymentIndexBase) {
      this.componentTag = globalConfig.pageStyleSetting.repaymentIndexBase
    }
    console.log('还款页面--中转组件', this.componentTag)
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
