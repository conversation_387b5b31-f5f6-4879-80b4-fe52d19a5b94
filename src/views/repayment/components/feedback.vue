<template>
  <div class="feedback">
    <!-- 产品列表 -->
    <van-popup v-model="showfeedBack" class="feedback-pop" :close-on-click-overlay="false" position="bottom">
      <div class="close" @click="close()">
        <img src="@/assets/images/cancel.png" alt="" />
      </div>
      <div class="detail">
        <div class="title">Feedbacks on repayment issues</div>
        <div class="content">
          <div class="item required">Issue Type</div>
          <div class="select" @click="selectIssue">
            <div class="value" v-text="feedBackObj.issueType"></div>
            <img src="@/assets/images/choosetype.png" alt="">
          </div>
          <div class="item">Description</div>
          <div class="input">
            <textarea v-model.trim="feedBackObj.description" maxlength="1000" name="" id="" @input="handleInput" cols="30" rows="10"></textarea>
            <div class="num" v-text="`${feedBackObj.description.length}/1000`"></div>
          </div>
          <div class="item">Attachment</div>
          <div class="attachment" :class="{ 'have-data': fileList.length > 0 }">
            <!-- <ul class="photo-list">
              <li class="not-data">
                <img src="@/assets/images/attachment.png" alt="">
                <div>Up to 5 sheets</div>
              </li>
            </ul> -->
            <Uploader v-model="fileList" capture="camera" accept="image/*" preview-size="60" :after-read="afterRead" :max-count="5">
              <div class="choose-img">
                <img src="@/assets/images/attachment.png" alt="">
                <div>Up to 5 sheets</div>
              </div>
            </Uploader>
          </div>
        </div>
        <c-button name="Submit" @buttonClick="submitData"></c-button>
      </div>
    </van-popup>
    <CSelect title="Cancel" :list="issueTypeList" :value="feedBackObj.issueType" :show="showCSelect" @close="closeSelect" />
  </div>
</template>

<script>
import { Uploader } from 'vant';
import CSelect from '@/components/c-select.vue';
import api from "@/api/interface";
export default {
  name: 'loanTerms',
  components: {
    Uploader,
    CSelect
  },
  props: {
    showFeedback: {
      type: Boolean,
      default: false
    },
  },
  computed: {
  },
  data() {
    return {
      showfeedBack: false,
      feedBackObj: {
        description: '',
        issueType: ''
      },
      fileList: [],
      showCSelect: false,
      issueTypeList: [{
        key: 'Unable to repay',
        value: 'Unable to repay'
      }, {
        key: 'Repayment not reflected',
        value: 'Repayment not reflected'
      }, {
        key: 'Repeated debited',
        value: 'Repeated debited'
      }, {
        key: 'Others',
        value: 'Others'
      }]
    };
  },

  mounted() {
    this.report('feedback_tanchuang_activity');
  },

  watch: {
    'showFeedback': function(value) {
      this.showfeedBack = value;
    },
   },

  methods: {
    // 数据上报
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'repaymentIndex',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    // 关闭产品选择
    close() {
      this.showfeedBack = false;
      this.$emit('close');
      this.report('feedback_cancel_click');
    },
    // 提交数据
    submitData() {
      this.$loading()
      if (!this.feedBackObj.issueType) {
        this.$toast('Please select Issue Type');
        this.$hideLoading();
        return;
      }
      const attachmentList = [];
      if (this.fileList.length > 0) {
        this.fileList.forEach(item => {
          console.log('item.content', item.content.length);
          attachmentList.push(item.content);
        })
      }
      this.report('feedback_submit_click');
      return new Promise((resolve, reject) => {
        this.$http(api.repayment.submitRepayRequestion, {
          data: {
            requestType: 'Repayment',
            issueType: this.feedBackObj.issueType,
            description: this.feedBackObj.description,
            attachmentList: attachmentList
          }
        }).then(res => {
          this.$hideLoading();
          // 重置数据
          this.fileList = [];
          this.feedBackObj = {
            description: '',
            issueType: ''
          };
          this.$toast('Thanks for your feedback, we will deal with you as soon as possible.');
          this.$emit('close');
          resolve(res)
        }).catch(e => {
          reject(e);
        });
      })
    },
    selectIssue() {
      this.showCSelect = true;
    },
    // 限制输入
    handleInput(e) {
      this.feedBackObj.description = e.target.value.replace(/[\r\n]/g, '')
    },
    // 压缩图片
    afterRead(file) {
      // 大于800kb的jpeg和png图片都缩小像素上传
      if(/\/(?:jpeg|png)/i.test(file.file.type)&&file.file.size>800000) {
        // 创建Canvas对象(画布)
        let canvas =  document.createElement('canvas')
        // 获取对应的CanvasRenderingContext2D对象(画笔)
        let context = canvas.getContext('2d') 
        // 创建新的图片对象 
        let img = new Image()
        // 指定图片的DataURL(图片的base64编码数据)
        img.src = file.content
        console.log('压缩前长度', file.content.length)
        // 监听浏览器加载图片完成，然后进行进行绘制
        img.onload = () => {
          // 指定canvas画布大小，该大小为最后生成图片的大小
          canvas.width = 400
          canvas.height = 300
          /* drawImage画布绘制的方法。(0,0)表示以Canvas画布左上角为起点，400，300是将图片按给定的像素进行缩小。
          如果不指定缩小的像素图片将以图片原始大小进行绘制，图片像素如果大于画布将会从左上角开始按画布大小部分绘制图片，最后的图片就是张局部图。*/ 
          context.drawImage(img, 0, 0, 400, 300)
          // 将绘制完成的图片重新转化为base64编码，file.file.type为图片类型，0.92为默认压缩质量
          file.content = canvas.toDataURL(file.file.type, 0.2)
          // 最后将base64编码的图片保存到数组中，留待上传。
          this.fileList[this.fileList.length - 1] = file;
          console.log('压缩后长度', file.content.length)
        }                       
      }
    },
    closeSelect(obj) {
      this.showCSelect = false;
      if (obj) {
        this.feedBackObj.issueType = obj.value;
      }
    },
  },
};
</script>

<style lang="scss" scoped>

.feedback{

  .feedback-pop{
    background-color: inherit;
    overflow: hidden;
  }
  .close{
    text-align: left;
    margin-bottom: 10px;
    img{
      width: 38px;
      height: 38px;
      margin-left: 16px;
      margin-bottom: -8px;
    }
  }
  .detail{
    padding: 25px 0 0 0px;
    background: #ffffff;
    height: 480px;
    padding: 0px 0 0 0px;
    .title{
      height: 45px;
      font-size: 18px;
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      color: #1b3155;
      background: #F5F5F5;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .content{
      padding: 20px 27px;
      text-align: left;
      height: 342px;
      overflow-y: scroll;
      overflow-x: hidden;
      .item {
        font-size: 14px;
        font-family: Avenir, Avenir-Medium;
        font-weight: 500;
        text-align: left;
        color: #1b3155;
        line-height: 19px;
        &.required:before {
          content: '* ';
          color: red;
        }
      }
      .select{
        width: 304px;
        height: 40px;
        border: 1px solid #c4cad5;
        margin-top: 9px;
        margin-bottom: 14px;
        position: relative;
        .value{
          height: 40px;
          line-height: 40px;
          padding-left: 5px;
          font-weight: 500;
          text-align: left;
          color: #97a2b7;
          font-size: 12px;
        }
        img{
          width: 11px;
          height: 6px;
          position: absolute;
          right: 9px;
          top: 18px;
        }
      }
      .input{
        position: relative;
        margin-top: 9px;
        margin-bottom: 14px;
        textarea{
          resize: none;
          padding: 9px 12px;
          width: 292px;
          height: 80px;
          border: 1px solid #c4cad5;
          font-size: 12px;
          color: #97a2b7;
        }
        .num{
          position: absolute;
          right: 7px;
          font-size: 12px;
          color: #c4cad5;
          bottom: 11px;
        }
      }
      .attachment{
        width: 285px;
        min-height: 60px;
        border: 1px dashed #c4cad5;
        text-align: center;
        margin-top: 9px;
        padding: 10px;
        .choose-img{
          margin-top: 10px;
          img {
            width: 24px;
            height: 24px;
          }
          div{
            font-size: 12px;
            font-family: Avenir, Avenir-Medium;
            font-weight: 500;
            text-align: center;
            color: #c4cad5;
            transform: scale(.75);
          }
        }
        &.have-data{
          text-align: left;
          .choose-img{
            margin-top: 0px;
            height: 60px;
            width: 60px;
            text-align: center;
            border: 1px dashed #c4cad5;
            img {
              margin-top: 8px;
            }
            div{
              margin-top: -9px;
            }
          }
        }
      }
    }
  }
}
</style>