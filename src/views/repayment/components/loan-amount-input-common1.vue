<template>
  <div class="loan-input">
    <div class="input-amount">
      <div class="currency">₦</div>
      <input @click="report" type="tel" placeholder="" @blur="startLoanCalculate()" :ref="`${type}inputLoanAmt`"
      @keydown="keyDown($event)" @keyup="loanKeyUp()" @input="loanInput()" v-model="amount">
      <div class="edit" @click="editAmount()">
        <img class="edit-input" :src="require(`@/assets/images/${productSource}/edit.png`)" />
      </div>
    </div>
    <div class="range" v-text="rangeTips"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'inputAmountCommon1',
  data() {
    return {
      amount: '',
      editStatus: false
    };
  },
  computed: {
    ...mapState(['productSource']),
    rangeTips() {
      let tips = '';
      if (this.productInfo.chooseTypeData) {
        tips = `Cash withdrawal range: ₦${this.common.singleToThousands(this.productInfo.chooseTypeData.minAmount)}-₦${this.common.singleToThousands(this.productInfo.chooseTypeData.maxAmount)}`
      }
      return tips;
    }
  },
  props: {
    inputAmount: {
      type: [String, Number],
      default: ''
    },
    type: { // 用户的产品类型
      type: String,
      default: ''
    },
    productInfo: { // 产品相关信息
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  watch: {
    'inputAmount': function(value) {
      this.amount = value;
    }
  },
  mounted() {
    // 组件初次加载也要重新赋值一下，否则无法获取值。
    this.amount = this.inputAmount;
  },

  methods: {
    report(value) {
      const vm = this;
      vm.$emit('report', {
        type: vm.type,
        value: value
      })
    },
    startLoanCalculate() {
      const vm = this;
      vm.editStatus = false;
      vm.$emit('startLoanCalculate', {
        type: vm.type
      })
    },
    keyDown(e) {
      const vm = this;
      vm.$emit('keyDown', {
        type: vm.type,
        value: e
      })
    },
    loanKeyUp() {
      const vm = this;
      vm.$emit('loanKeyUp', {
        amount: vm.amount,
        type: vm.type
      })
    },
    loanInput() {
      const vm = this;
      vm.$emit('loanInput', {
        amount: vm.amount,
        type: vm.type
      })
    },
    // 编辑金额
    editAmount() {
      let vm = this;
      vm.editStatus = !vm.editStatus;
      if (vm.editStatus) {
        vm.$nextTick(function() {
          vm.$refs[`${vm.type}inputLoanAmt`].focus();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
 .input-amount{
  height: 50px;
  display: flex;
  align-items: flex-end;
  position: relative;
  .currency{
    margin-top: -19px;
    color: #1B40FF;
    font-family: DIN;
    font-size: 26px;
    font-style: normal;
  }
  input{
    border: none;
    height: 35px;
    line-height: 35px;
    margin-left: 5px;
    width: calc(100% - 65px);
    outline: none;
    position: relative;
    background: transparent;
    color: #1B40FF;
    font-family: DIN;
    font-size: 26px;
    font-style: normal;
    &::placeholder{
      font-size: 12px;
      font-weight: 600;
      color: rgb(177, 176, 176);
      position: absolute;
      top: 22px;
    }
  }
  img{
    width: 26px;
    height: 26px;
    position: absolute;
    right: 23px;
    top: 20px;
  }
}
 .range{
    font-family: HarmonyOS Sans SC;
    font-size: 12px;
    color: #0F2388;
    letter-spacing: 0;
    font-weight: 500;
    transform: scale(.84);
    margin-left: -29px;
    margin-top: 8px;
  }
</style>