<template>
  <div class="only-repay-popup">
    <van-popup class="popup" v-model="show">
      <div class="content">
        <img class="close" @click="close" src="@/assets/images/common/close06.png" alt="">
        <div class="tips">
         Only repay will lose your <span>{{ amt }} credit increase.</span> Are you sure to give up?
        </div>
        <img class="money" src="@/assets/images/repayment/money.png" alt="">
      </div>
      <div class="reloan" @click="reloanBtnClick">Repay and Get A New Loan</div>
      <div class="only-repay" @click="onlyRepayBtnClick">Only Repay</div>
    </van-popup>
  </div>
</template>

<script>

export default {
  name: 'onlyRepayPopup',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    amt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false
    };
  },

  mounted() {
  },
  watch: {
    value(newValue) {
      this.show = newValue;
      if (newValue) {
        const vm = this;
        console.log('nativeBackCallback');
        window.nativeBackCallback = function() {
          vm.$emit('onlyRepayBtnClick');
          vm.report('reloan_popup_back_click')
          // 1 原生不处理返回键  0 原生处理返回键 
          return 1
        }
      }
    }
  },
  methods: {
    close() {
      this.$emit('input', false);
      this.$emit('onlyRepayBtnClick');
      this.report('reloan_popup_close_click')
    },
    reloanBtnClick() {
      this.$emit('reloanBtnClick');
      this.report('reloan_popup_reloan_click')
    },
    onlyRepayBtnClick() {
      this.$emit('onlyRepayBtnClick');
      this.report('reloan_popup_only_repay_click')
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'repayment',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.popup{
    height: 701px;
    width: 100%;
    box-sizing: border-box;
    border-radius: 12px;
    border-radius: 23px;
    background: rgba(0, 0, 0, 0.5);
    padding-top: 125px;
    .content{
      border-radius: 18px;
      background: linear-gradient(180deg, #FFF 42.6%, #FFFCAB 100%);
      width: 250px;
      min-height: 233px;
      height: auto;
      box-sizing: border-box;
      position: relative;
      margin: auto;
      .close{
        width: 12px;
        height: 12px;
        position: absolute;
        right: 0;
        top: -30px;
      }
      .tips{
        text-align: left;
        color: #000;
        font-family: Avenir;
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        padding: 15px 20px 0 20px;
        span{
          color: #FF4D00;
          font-weight: 600;
        }
      }
      .money{
        width: 202px;
        height: 125px;
        margin: 0 auto;
      }
    }
    .reloan{
      width: 250px;
      height: 45px;
      line-height: 45px;
      border-radius: 25px;
      background: linear-gradient(151deg, #FFC700 -14.73%, #FF3D00 116.62%);
      box-shadow: 0px 2px 2px 0px rgba(255, 255, 255, 0.25) inset;
      color: #FFF;
      text-align: center;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      margin: 16px auto 0 auto;
    }
    .only-repay{
      color: rgba(255, 255, 255, 1);
      opacity: 0.7033;
      text-align: center;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      margin: 23px auto 0 auto;
    }
  }
</style>