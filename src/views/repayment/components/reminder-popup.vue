<template>
  <div class="reminder">
    <van-popup class="popup" v-model="show">
      <img class="jingle" src="@/assets/images/repayment/tips.png" alt="">
      <div class="content">
        <div class="title">Reminder</div>
        <div class="tips">
          Dear user, your loan will be due this week on <span v-text="day"></span>, in order to avoid overdue payment, please recharge the amount to your repayment account in advance and make repayment with peace of mind.
        </div>
        <c-button name="OK" @buttonClick="close('confirm')"></c-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'reminder',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    day: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false
    };
  },

  mounted() {
    this.show = this.value;
  },
  watch: {
    value(newValue) {
      this.show = newValue;
    }
  },
  methods: {
    close() {
      this.$emit('input', false)
    }
  },
};
</script>

<style lang="scss" scoped>
.popup{
    height: 521px;
    width: 300px;
    box-sizing: border-box;
    border-radius: 12px;
    border-radius: 24px;
    background-color: transparent;
    padding-top: 100px;
    .jingle{
      width: 77px;
      height: 84px;
      position: absolute;
      top: 70px;
      right: 107px;
      z-index: 5000;
    }
    .content{
        background: linear-gradient(180deg, #FFDEDE 0%, #FFF 30.5%, #FFF 100%);
        box-shadow: 0px 2px 20px 0px rgba(0, 54, 101, 0.06);
        border-radius: 24px;
        height: 300px;
        padding: 50px 20px 20px 20px;
        box-sizing: border-box;
        position: relative;
        .title{
          color: #000;
          font-family: Roboto;
          font-size: 17px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          text-align: left;
        }
        .faq{
          color: #314CFE;
          font-family: Avenir;
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          text-decoration-line: underline;
          text-align: left;
          margin-top: 10px;
        }
        .tips{
          color: #08234E;
          font-family: Avenir;
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 21px;
          text-align: left;
          margin-top: 8px;
          span{
            color: #000;
            text-transform: capitalize;
          }
        }
        .show-img{
          .one{
            width: 181px;
            height: 163px;
          }
        }
        .c-button{
          width: calc(100% - 32px);
          height: 42px;
          border-radius: 12px;
          position: absolute;
          bottom: 15px;
          left: 16px;
        }
    }
  }
</style>