<template>
  <div class="repayment-index" @click="blurStatus($event)">
    <c-header titleName="Repayment" :backFun="myBackFun">
      <template #right>
        <img class="contact-us" @click="goFeedBack()" :src="require(`@/assets/images/feedback.png`)" alt="">
      </template>
    </c-header>
    <div class="repayment">
      <!-- 增信banner: 命中客群且当前存在复借或者处于预约几款状态 -->
      <div class="increase-limit-upload" @click="showAddPersonalInformationBanner" v-if="showIncreaseCredit === 'Y' && haveReloan && androidVersion > 28">
        <div class="tip-content">
          <span>Add additional information can increase your </span>
          <span class="sub"> credit limit by up to 50%</span>
          <span class="add">Add now</span>
        </div>
        <img src="@/assets/images/increaseLimit/money.png" />
      </div>
      <!-- 提示广告条 -->
      <div class="repayment-tips" v-else-if="showBanner">
        <div class="text">The loan is due on <span v-text="dueRemindDay"></span>, please arrange in advance</div>
        <img src="@/assets/images/repayment/repayment-tips.png" alt="">
      </div>
      <div v-else-if="showVerdictAnnouncement" class="overdue-tips" @click="goToVerdictAnnouncement">
        <img src="@/assets/images/repayment/warning.png" alt="">
        <div class="marquee">
          <div class="marquee-text">Your loan is overdue, please repay it in time.
            If it is still not handled in time, our legal department will initiate prosecution and other processing.You can refer to the example
          </div>
        </div>
        <div class="arrow">>>></div>
      </div>
      <template v-else>
        <div class="goldman-tips" v-if="showGoldmanTip === 'Y' && supportedFeatures.goldbank && transferNow !== '1'">
          <div class="tips">Your loan has been disbursed to your goldman account!</div>
          <div class="transfer-now" @click="goToBanlance">Transfer Now</div>
        </div>
      </template>
      <!-- 还款 -->
      <div class="repayment-amount" :class="{'goldman': showGoldmanTip === 'Y' && supportedFeatures.goldbank && transferNow !== '1'}">
        <div class="title">
          <div class="left">Repayment Amount</div>
          <div class="right" @click="showPaymentList()">View Details</div>
        </div>
        <div class="amount">
          <div class="currency">₦</div>
          <div class="value">
            <div @click="editAmount()" :style="{'visibility': editStatus ? 'hidden' : 'initial'}" :class="{'overdue': repaymentDetail.loanStatus === 'O'}" class="static" v-text="repaymentDetail.totalAmt"></div>
            <input :style="{'visibility': editStatus ? 'initial' : 'hidden'}" ref="inputAmt"
            class="input" type="tel" :class="{'overdue': repaymentDetail.loanStatus === 'O'}" @keydown="keyDown($event)" @keyup="keyUp($event)" v-model="repaymentDetail.totalAmt">
          </div>
          <div class="edit" @click="editAmount()" v-if="canChangeRepayAmt">
            <img class="edit-input" :src="editStatus ? editOn : editOff" />
          </div>
        </div>
        <div class="charge-fee">
          Including <span class="money" v-text="editStatus ? '--' : `₦${repaymentDetail.passAmount}`"></span> fees charged by payment channels
        </div>
        <div class="next-repayment" v-if="nextRepayment.dueDate">
          <div>Next Repayment Amount: <span class="currency">₦</span><span v-text="nextRepayment.amount"></span></div>
          <div>Next Due Date: <span v-text="nextRepayment.dueDate"></span></div>
        </div>
        <!-- 优惠券 -->
        <div class="choose-coupon" v-if="haveReloan">
          <div class="name">
            <span>Coupon</span>
          </div>
          <div class="select-coupon" v-if="defaultCouponId" @click="chooseCoupons()">
            <div  class="negtive" v-if="discountAmt > 0" v-text="`-${common.singleToThousandsWithoutCarryBit(discountAmt)}`"></div>
            <div  class="negtive" v-else v-text="couponList.length"></div>
            <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
          </div>
          <div v-else class="select-coupon not" @click="showDialog = true">Not available</div>
        </div>
      </div>
      <!-- 复借 -->
      <div class="re-loan" v-if="haveReloan">
        <div class="title">
          <img :src="require(`@/assets/images/${productSource}/ic-star.png`)" alt="">
          <div class="name" v-text="loanTypeInfor.title"></div>
        </div>
        <div class="tips" v-if="loanTypeInfor.tips">
          <div class="content" v-text="loanTypeInfor.tips">
          </div>
        </div>
        <!-- 用户首选产品 -->
        <div class="loan-amount">
          <div class="title">
            Loan Amount
            <!-- <img v-if="loanType === 'BOOK_LOAN'" src="@/assets/images/common/book-loan-tips.png" alt=""> -->
            <div class="amount-add" v-if="preferChoiceProduct.tempAddLimit > 0">
              <div class="wrap">
                <div class="num" v-text="`Including ${common.singleToThousands(preferChoiceProduct.tempAddLimit)} limit increase`"></div>
                <div class="arrow"></div>
              </div>
            </div>
          </div>
          <loanAmountInput
            key="prefer"
            :inputAmount="preferChoiceProduct.amount"
            :productInfo="preferChoiceProduct"
            type="prefer"
            @report="repaymentReport('new_loan_amount_click')"
            @startLoanCalculate="startLoanCalculate"
            @loanKeyUp="loanKeyUp"
            @loanInput="loanInput"
            @keyDown="keyDown"
          ></loanAmountInput>
        </div>
        <loanTerms
        v-model="preferChoiceProduct.productActive"
        @chooseType="chooseLoanTermType"
        :productSource="productSource"
        customType='prefer'
        :productList="loanDetail.productList"></loanTerms>
        <newLoanDetail @checkInsurance="checkInsurance" :loanDetail="preferChoiceProduct.loanApplyObj"></newLoanDetail>
        <!-- 用户备选产品 -->
        <div class="alternative-loan" v-show="alternativeChoiceProduct.canShow">
          <div class="head" @click="changeAlternativeChoice">
            <img :src="alternativeChoiceProduct.isChoosen ? require('@/assets/images/choose.png') : require('@/assets/images/notchoose.png')" alt="">
            <div>Alternative-loan</div>
          </div>
          <div class="alternative-tips" :class="{'not-choose': !alternativeChoiceProduct.isChoosen}">
            Tips: <span>Only one loan disbursement</span> When you do not meet the borrowing requirements of the New Loan, we will lend with the alternative-loan to meet your financial needs.
          </div>
        </div>
        <div class="loan-amount" v-show="alternativeChoiceProduct.canShow && alternativeChoiceProduct.isChoosen">
          <div class="title">
            Loan Amount
            <!-- <img v-if="loanType === 'BOOK_LOAN'" src="@/assets/images/common/book-loan-tips.png" alt=""> -->
            <div class="amount-add" v-if="alternativeChoiceProduct.tempAddLimit > 0">
              <div class="wrap">
                <div class="num" v-text="`Including ${common.singleToThousands(alternativeChoiceProduct.tempAddLimit)} limit increase`"></div>
                <div class="arrow"></div>
              </div>
            </div>
          </div>
          <loanAmountInput
            key="alternative"
            :inputAmount="alternativeChoiceProduct.amount"
            :productInfo="alternativeChoiceProduct"
            type="alternative"
            @report="repaymentReport('alternative_loan_amount_click')"
            @startLoanCalculate="startLoanCalculate"
            @loanKeyUp="loanKeyUp"
            @loanInput="loanInput"
            @keyDown="keyDown"
          ></loanAmountInput>
        </div>
        <loanTerms
        v-show="alternativeChoiceProduct.canShow && alternativeChoiceProduct.isChoosen"
        v-model="alternativeChoiceProduct.productActive"
        @chooseType="chooseLoanTermType"
        :productSource="productSource"
        customType='alternative'
        :productList="loanDetail.productList"></loanTerms>
        <newLoanDetail v-show="alternativeChoiceProduct.canShow && alternativeChoiceProduct.isChoosen" @checkInsurance="checkInsurance" :loanDetail="alternativeChoiceProduct.loanApplyObj"></newLoanDetail>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="repay">
      <!-- 单期按钮控制逻辑 -->
      <template v-if="productType === 'single'">
        <div class="repay-re-loan" v-preventReClick="3000" @click="repayAndReLoan()">
          <div class="left">
            <div class="title">Repayment Amount</div>
            <div class="amount">
              <div class="after">
                <CMoney :currencyNum="common.singleToThousands(repaymentAmount)"></CMoney>
              </div>
              <div class="before" v-if="discountAmt" v-text="`${getRepayAmount}`"></div>
            </div>
          </div>
          <div class="right" v-text="loanTypeInfor.reButton"></div>
          <div class="amount-de" v-if="discountAmt">
            <div class="wrap">
              <div class="num" v-text="`-₦${(common.singleToThousandsWithoutCarryBit(discountAmt))}`"></div>
              <div class="arrow"></div>
            </div>
          </div>
        </div>
        <div class="only-repay">
          <div class="button" v-preventReClick="3000" @click="judgeOnlyRepay()">Only Repay</div>
        </div>
      </template>
      <!-- 多期控制逻辑 -->
      <template v-else>
        <div class="repay-re-loan" :class="{'inactive': getRepayAmount == 0}" v-if="discountAmt" v-preventReClick="3000" @click="haveReloan ? repayAndReLoan() : repayOnly()">
          <div class="left">
            <div class="title">Repayment Amount</div>
            <div class="amount">
              <div class="after">
                <CMoney :currencyNum="common.singleToThousands(repaymentAmount)"></CMoney>
              </div>
              <div class="before" v-text="`₦${getRepayAmount}`"></div>
            </div>
          </div>
          <div class="right" :class="{'reloan': haveReloan}" v-text="haveReloan ? loanTypeInfor.reButton : 'Repay'"></div>
          <div class="amount-de">
            <div class="wrap">
              <div class="num" v-text="`-₦${(common.singleToThousandsWithoutCarryBit(discountAmt))}`"></div>
              <div class="arrow"></div>
            </div>
          </div>
        </div>
        <div class="singel-button" v-if="!discountAmt">
          <CButton @buttonClick="haveReloan ? repayAndReLoan() : repayOnly()" :className="`confirm ${haveReloan ? 'reloan' : ''} ${getRepayAmount == 0 ? 'inactive' : ''}`"
          :name="haveReloan ? loanTypeInfor.reButton : 'Repay'"></CButton>
        </div>
        <div class="only-repay">
          <div class="button" :class="{'inactive': getRepayAmount == 0}" v-if="haveReloan" v-preventReClick="3000" @click="judgeOnlyRepay()">Only Repay</div>
        </div>
      </template>
    </div>
    <!-- 优惠券提示 -->
    <van-dialog v-model="showDialog" show-cancel-button className="dialog-after-repayment" :showConfirmButton="false" :showCancelButton="false">
        <div class="title">Note!</div>
        <div class="tips">There are currently no coupons available</div>
        <div class="dia-button">
          <div class="ok" @click="hideDialog()">OK</div>
          <div class="cancel" @click="hideDialog()">Cancel</div>
        </div>
    </van-dialog>
    <feedback :showFeedback="showFeedback" @close="closeFeedback()" />
    <reminderPopup v-model="showReminder" :day="dueRemindDay"></reminderPopup>
    <OnlyRepayPopup v-model="showOnlyRepayPopup" :amt="common.singleToThousands(maxTempAddLimit)" @reloanBtnClick="repayAndReLoan" @onlyRepayBtnClick="repayOnly"></OnlyRepayPopup>
    <addPersonalInformation v-model="showAddPersonalInformation" @hidePopup="hideAddPersonalInformation"></addPersonalInformation>
  </div>
</template>

<script>
import publicMixns from '../repaymentIndexMixins.js'
export default {
    name: 'indexBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.repayment-index{
  background: #F2F3F8;
  min-height: 100%;
  .c-header{
    background: #F2F3F8;
  }
  .contact-us{
    width: 53px;
    height: 40px;
    margin-right: 18px;
  }
  .repayment{
    margin-top: 56px;
    padding-bottom: 120px;
    .goldman-tips{
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #FEFDDD;
      padding: 7px 12px;
      align-items: center;
      margin: 0 12px 0 12px;
      box-sizing: content-box;
      .tips{
        width: 200px;
        color: #FF7438;
        font-family: Avenir;
        font-size: 11px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
        text-align: left;
      }
      .transfer-now{
        width: 90px;
        height: 27px;
        line-height: 27px;
        border-radius: 4px;
        background: #FF7438;
        color: #FFF;
        text-align: center;
        font-family: Avenir;
        font-size: 12px;
        font-style: normal;
      }
    }
    .increase-limit-upload{
      border-radius: 12px;
      height: 53px;
      background: $themeColor;
      box-sizing: border-box;
      margin: 0 12px 14px;
      position: relative;
      display: flex;
      align-items: center;
      z-index: 5;
      img{
        width: 62px;
        height: 54px;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
      }
      .tip-content{
        width: 275px;
        color: #FFF;
        font-family: Avenir;
        font-size: 12px;
        font-style: normal;
        font-weight: 900;
        line-height: normal;
        text-align: left;
        margin-left: 10px;
        position: relative;
        z-index: 10;
        .sub{
          color: #FFF600;
          font-family: Avenir;
          font-size: 15px;
          font-style: normal;
          font-weight: 900;
          line-height: normal;
        }
        .add{
          width: 77px;
          height: 20px;
          border-radius: 4px;
          background: #FFF600;
          display: inline-block;
          text-align: center;
          line-height: 20px;
          color: #FF454F;
          font-family: Avenir;
          font-size: 12px;
          font-style: normal;
          font-weight: 900;
          margin-left: 6px;
        }
      }
    }
    .repayment-tips{
      margin: 0 12px 10px 12px;
      background: #FFDEDE;
      height: 45px;
      position: relative;
      img{
        position: absolute;
        width: 105px;
        height: 45px;
        bottom: 0;
        right: 0;
      }
      .text{
        width: 230px;
        padding-top: 5px;
        margin-left: 12px;
        text-align: left;
        color: #E80000;
        font-family: Avenir;
        font-size: 12px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
        span{
          text-decoration-line: underline;
          font-size: 14px;
          text-transform: capitalize;
        }
      }
    }
    .overdue-tips{
      margin: 0 12px 5px 12px;
      display: flex;
      padding: 0 8px;
      height: 32px;
      box-sizing: border-box;
      justify-content: space-between;
      align-items: center;
      background: #FF786D;
      overflow: hidden;
      white-space: nowrap;
      position: relative;
      img{
        width: 19px;
        height: 18px;
        margin-right: 5px;
      }
      .marquee {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        box-sizing: border-box;
        height: 100%;
        line-height: 100%;
        position: relative;
      }
      .marquee-text {
        display: inline-block;
        animation: marquee 20s linear infinite;
        color: #FFF;
        font-family: Avenir;
        font-size: 12px;
        padding-left: 100%;
        height: 100%;
        line-height: 100%;
        position: absolute;
        top: 10px
      }

      @keyframes marquee {
        0% { transform: translateX(0); }
        100% { transform: translateX(-100%); }
      }
      .arrow{
        color: #FFF;
        font-family: Avenir;
        font-style: normal;
        margin-right: 8px;
        margin-left: 8px;
        font-size: 12px;
        height: 32px;
        line-height: 32px;
      }
    }
    .repayment-amount{
      background: #FFFFFF;
      box-shadow: 0 1px 10px 0 rgb(0 54 101 / 6%);
      border-radius: 16px;
      border-radius: 8px;
      margin: 7px 12px 12px 12px;
      padding: 0 18px;
      min-height: 120px;
      padding-bottom: 12px;
      &.goldman{
        margin-top: 0px;
        border-top-left-radius: 0px;
        border-top-right-radius: 0px;
      }
      .title{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 15px;
        .left {
          font-family: Avenir-Medium;
          font-size: 12px;
          color: #C4CAD5;
        }
        .right {
          font-family: Avenir-Medium;
          font-size: 12px;
          color: $themeColor;
          text-align: right;
        }
      }
      .amount{
        display: flex;
        justify-content: flex-start;
        align-items: baseline;
        margin-top: 15px;
        .currency{
          margin-top: -19px;
          font-family: Avenir-Medium;
          font-size: 20px;
          color: #1B3155;
          margin-right: 5px;
        }
        .value{
          position: relative;
          .static{
            line-height: 48px;
            display: inline-block;
            width: 100%;
            height: 100%;
            font-family: DINAlternate-Bold;
            font-size: 48px;
            color: $themeColor;
            font-weight: 600;
            &.overdue{
              color: red;
            }
          }
          .input{
            font-family: DINAlternate-Bold;
            font-size: 50px;
            line-height: 50px;
            color: $themeColor;
            border: none;
            width: 100%;
            height: 100%;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0;
            text-align: center;

            &.overdue{
              color: red;
            }
          }
          .edit{
            width: 14px;
            height: 14px;
            position: absolute;
            right: 10px;
            top: 21px;
          }
        }
        .edit{
          margin-left: 6px;
          height: 37px;
          width: 15px;
          position: relative;
          img {
            width: 15px;
            height: 15px;
            position: absolute;
          }
        }
      }
      .charge-fee{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #919DB3;
        transform: scale(0.9);
        margin-left: -24px;
        margin-bottom: 8px;
        margin-top: 5px;
        .money{
          color: red;
        }
      }
      .next-repayment{
        text-align: left;
        font-size: 12px;
        color: #1B3155;
        font-weight: 500;
        line-height: 18px;
        border-top: 1px solid #C4CAD5;
        padding-top: 4px;
        span{
          font-size: 14px;
          color: #1B3155;
          font-weight: 700;
        }
        .currency{
          font-size: 12px;
        }
      }
      .choose-coupon{
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 25px;
        line-height: 25px;
        border-radius: 6px;
        margin-top: 8px;
        .name{
          font-size: 14px;
          color: #536887;
          display: flex;
          align-items: center;
        }
        .select-coupon{
          display: flex;
          align-items: center;
          &.not{
            color: #919DB3;
            font-size: 14px;
          }
          .negtive{
            margin-right: 10px;
            color: $themeColor;
            font-size: 12px;
          }
        }
      }
    }

    .re-loan{
      background: #FFFFFF;
      box-shadow: 0 1px 10px 0 rgb(0 54 101 / 6%);
      border-radius: 16px;
      border-radius: 8px;
      margin: 7px 12px 12px 12px;
      padding: 0 18px;
      .title{
        display: flex;
        align-items: center;
        padding-top: 12px;
        img{
          width: 19px;
          height: 19px;
        }
        .name{
          font-family: Avenir-Medium;
          font-size: 14px;
          color: #1B3155;
          margin-left: 5px;
          font-weight: 800;
        }
      }

      .tips{
        background: $lightColor;
        border-radius: 4px;
        height: 26px;
        padding: 5px;
        margin-top: 7px;
        .content{
          font-size: 12px;
          color: #919DB3;
          transform: scale(.9);
          text-align: left;
          width: 105%;
          margin-left: -10px;

          .discount{
            color: $themeColor;
          }
        }
      }
      .loan-amount {
        text-align: left;
        padding: 0 4px;
        padding-top: 8px;
        position: relative;
        .title{
          text-align: left;
          line-height: 12px;
          font-family: Avenir-Medium;
          font-size: 12px;
          color: #C4CAD5;
          padding-top: 0px;
          img{
            width: 135px;
            height: 15px;
            margin-left: 5px;
          }
        }
        .input-amount{
          height: 50px;
          display: flex;
          align-items: flex-end;
          position: relative;
          .currency{
            margin-top: -19px;
            font-family: Avenir-Medium;
            font-size: 20px;
            color: #1B3155;
          }
          input{
            border: none;
            height: 35px;
            color: $themeColor;
            line-height: 35px;
            margin-left: 5px;
            width: calc(100% - 65px);
            outline: none;
            position: relative;
            font-family: DINAlternate-Bold;
            font-size: 35px;
            &::placeholder{
              font-size: 12px;
              font-weight: 600;
              color: rgb(177, 176, 176);
              position: absolute;
              top: 22px;
            }
          }
          img{
            width: 15px;
            height: 15px;
            position: absolute;
            right: 23px;
            top: 25px;
          }
        }
        .amount-add{
          position: absolute;
          left: 76px;
          top: 7px;
          transform: scale(.85);
          .wrap{
            position: relative;
            .num{
              background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
              color: #ffffff;
              width: auto;
              white-space: nowrap;
              padding: 2px 4px;
              font-size: 12px;
            }
            .arrow{
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 3.5px 5px 3.5px 0;
              border-color: transparent #C78900 transparent transparent;
              position: absolute;
              left: -5px;
              top: 5px;
            }
          }
        }
      }
      .alternative-loan{
        border-top: 1px dashed rgba(196,202,213,1);
        padding-top: 15px;
        .head{
          display: flex;
          align-items: center;
          font-family: Avenir-Heavy;
          font-size: 14px;
          color: #1B3155;
          font-weight: 800;
          img {
            width: 15px;
            height: 15px;
            margin-right: 10px;
          }
        }
        .alternative-tips{
          font-family: Avenir-Medium;
          font-size: 12px;
          color: #6E7D94;
          font-weight: 500;
          text-align: left;
          transform: scale(.94);
          margin-left: -10px;
          margin-top: 5px;
          &.not-choose {
            padding-bottom: 15px;
          }
          span{
            color: #FF454F;
            font-weight: 800;
          }
        }
      }
      .select-loan-tenor{
        position: relative;
        margin-top: 20px;
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #536887;
        &::before {
          position: absolute;
          box-sizing: border-box;
          content: " ";
          pointer-events: none;
          right: -21px;
          top: -3px;
          left: 3px;
          border-bottom: 2px solid #ebedf0;
          transform: scaleY(0.5);
        }
      }
      .loan-terms{
        border: 1px solid #E3E5E9;
        margin-top: 10px;
        ::v-deep .select-loan-terms{
          border-radius: 0px;
          height: 76px;
          .left{
            .title {
              font-size: 12px;
              margin-bottom: 4px;
              font-weight: 500;
            }
            .days{
              font-weight: 600;
              margin-bottom: 2px;
            }
          }
          .right{
            border-radius: 0px;
          }
        }
      }
    }
  }
  .loan-detail{

    &.loan{
      margin-top: 10px;
    }

    &.select{
      .right{
            display: flex;
            align-items: center;
            height: 40px;
            .no-selected{
              font-size: 14px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              color: #c4cad5;
            }
            .available{
              color: #fa7809;
            }
            .selected{
              color: #1b3155;
            }
            .checked{
              color: $themeColor;
            }
            .no-data{
              color: #c4cad5;
            }
          }
    }

    .li{
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 55px;
      position: relative;
      &.border-line::before {
          position: absolute;
          box-sizing: border-box;
          content: " ";
          pointer-events: none;
          right: -21px;
          top: -3px;
          left: 3px;
          border-bottom: 2px solid #ebedf0;
          transform: scaleY(0.5);
        }
      .item{
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #536887;
      }
      .check-acc{
          display: flex;
          align-items: center;
          img{
            width: 18px;
            height: 18px;
            margin-left: 5px;
          }
        }
      .content{

        font-family: DINAlternate-Bold;
        font-size: 14px;
        color: #1B3155;
        text-align: right;

        &.right{
          display: flex;
          align-items: center;
          .checked{
            color: $themeColor;
          }
        }
        &.outstanding{
          color: #02b17b;
        }
        &.overdue{
          color: #E13334;
        }
        &.pending{
          color: $themeColor;
        }
        &.under-review{
          color: $themeColor;
        }
        &.distursing{
          color: $themeColor;
        }
        ::v-deep .c-money{
          display: flex;
          align-items: flex-end;
          .monetary-unit{
            font-family: DINAlternate-Bold;
            font-size: 12px;
            color: #1B3155;
            font-weight: 600;
          }
          .currency-num{
            font-family: DINAlternate-Bold;
            font-size: 14px;
            color: #1B3155;
            font-weight: 600;
          }
        }
      }
      .cal{
        font-size: 14px;
        color: #c4cad5;
        font-weight: 500;
      }
    }
  }
  .repay{
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0px;
    background: #F2F3F8;
    .repay-re-loan{
      background: $themeColor;
      height: 49px;
      margin: 0 18px;
      border-radius: 8px;
      color: #FFFFFF;
      padding: 6px 12px 7px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &.inactive{
        // background: #caefff;
      }
      .left{
        text-align: left;
        .title{
          font-family: Avenir-Medium;
          font-size: 12px;
          margin-top: -11px;
          margin-bottom: 4px;
        }
        .amount{
          display: flex;
          align-items: flex-end;
          ::v-deep .c-money{
            display: flex;
            align-items: flex-end;
            .monetary-unit{
              font-size: 14px;
              color: #ffffff;
              font-weight: 700;
            }
            .currency-num{
              font-family: DINAlternate-Bold;
              font-size: 14px;
              color: #ffffff;
              margin-left: 2px;
              font-weight: 700;
            }
          }
          .before{
            font-size: 12px;
            margin-left: 3px;
            text-decoration: line-through;
            color: #DDFFF4;
          }
        }
      }
      .right{
        font-family: Avenir-Medium;
        font-size: 18px;
        color: #FFFFFF;
        text-align: right;
        &.reloan{
          font-size: 16px;
        }
      }
      .amount-de{
        position: absolute;
        right: 26px;
        top: -13px;
        transform: scale(0.85);
        .wrap{
          position: relative;
          .num{
            background-image: linear-gradient(180deg, #FFD487 5%, #FFA316 100%);
            color: #ffffff;
            width: auto;
            white-space: nowrap;
            padding: 4px 9px;
            border-radius: 4px;
            font-size: 12px;
          }
          .arrow{
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 5px 0 5px 7px;
            border-color: transparent transparent transparent #FFA316;
            position: absolute;
            left: 0px;
            top: 16px;
          }
        }
      }
    }

    .singel-button{
      .confirm {
        width: 90%;
        margin-bottom: 30px;
        background: $themeColor;
        &.reloan{
          margin-bottom: 0px;
        }
      }
      .inactive{
        // background: #caefff;
      }
    }
    .only-repay{
      display: flex;
      justify-content: center;
      background: #F2F3F8;
      height: 55px;
      .button{
        font-family: Avenir-Heavy;
        font-size: 16px;
        color: $themeColor;
        text-align: center;
        margin-top: 14px;
        border-bottom: 1px solid $themeColor;
        padding-bottom: 3px;
        height: 20px;
        // &.inactive{
        //   color: #caefff;
        //   border-bottom: 1px solid #caefff;
        // }
      }
    }
  }
  .van-popup{
    background: rgba(120,120,120,0);
    .close{
      text-align: left;
      img{
        width: 38px;
        height: 38px;
        margin-left: 16px;
        margin-bottom: 3px;
      }
    }
    .seal-overdue{
      width: 174px;
      height: 166px;
      position: absolute;
      left: 27%;
      top: 194px;
    }
    &.plan-list{
      max-height: 90%;
      overflow-y: hidden;
      .warp-list{
        height: 500px;
        overflow-y: auto;
      }
    }
  }
  .dialog-after-repayment{
    color: #536887;
    padding: 12px 15px;
    width: 254px;
    .van-dialog__content{
      .title{
          height: 40px;
          line-height: 40px;
          text-align: left;
          margin-bottom: 5px;
          color: #536887;
          font-weight: 700;
      }
      .tips{
          line-height: 20px;
          text-align: left;
      }
      .dia-button{
          margin-top: 15px;
          .ok{
              height: 40px;
              line-height: 40px;
              background: $themeColor;
              color: #ffffff;
              border-radius: 5px;
          }
          .cancel{
              height: 40px;
              line-height: 40px;
              background: #ffffff;
              color:  $themeColor;
              border-radius: 5px;
          }
      }
    }
    .van-dialog__content--isolated{
      min-height: 70px;
    }
    .van-dialog__message--left {
        padding: 15px 24px;
    }
  }
}
</style>
