<template>
  <div class="repayment-index" @click="blurStatus($event)">
    <c-header titleName="Repayment" :backFun="myBackFun" :styType="'b'">
      <template #right>
        <img class="contact-us" @click="goFeedBack()" :src="require(`@/assets/images/wayacredit/feed-back.png`)" alt="">
      </template>
    </c-header>
    <div class="repayment">
      <div class="next-repayment" v-if="nextRepayment.dueDate">
        <div class="next-content">
          <img class="next-term" src="@/assets/images/wayacredit/next-term.png" alt="">
          <div class="content">
            <div>Next Repayment Amount: ₦ {{ nextRepayment.amount }}</div>
            <div>Next Due Date: {{ nextRepayment.dueDate }}</div>
          </div>
        </div>
        <div class="arrow">>>></div>
      </div>
      <template v-else>
        <div class="goldman-tips" v-if="showGoldmanTip === 'Y' && supportedFeatures.goldbank && transferNow !== '1'">
          <div class="tips">Your loan has been disbursed to your goldman account!</div>
          <div class="transfer-now" @click="goToBanlance">Transfer Now</div>
        </div>
      </template>
      <!-- 还款 -->
      <div class="repayment-amount">
        <div class="title">
          <div class="left">Repayment Amount</div>
          <div class="right" @click="showPaymentList()">View Details</div>
        </div>
        <div class="amount">
          <div class="currency">₦</div>
          <div class="value">
            <div @click="editAmount()" :style="{'visibility': editStatus ? 'hidden' : 'initial'}" :class="{'overdue': repaymentDetail.loanStatus === 'O'}" class="static" v-text="repaymentDetail.totalAmt"></div>
            <input :style="{'visibility': editStatus ? 'initial' : 'hidden'}" ref="inputAmt"
            class="input" type="tel" :class="{'overdue': repaymentDetail.loanStatus === 'O'}" @keydown="keyDown($event)" @keyup="keyUp($event)" v-model="repaymentDetail.totalAmt">
          </div>
          <div class="edit" @click="editAmount()" v-if="!(isPCX && !canChangeRepayAmt)">
            <img class="edit-input" :src="editStatus ? editOnWaya : editOnWaya" />
          </div>
        </div>
        <div class="charge-fee">
          Including <span class="money" v-text="editStatus ? '--' : `₦${repaymentDetail.passAmount}`"></span> fees charged by payment channels
        </div>
        <div class="next-repayment" v-if="nextRepayment.dueDate">
          <div>Next Repayment Amount: <span class="currency">₦</span><span v-text="nextRepayment.amount"></span></div>
          <div>Next Due Date: <span v-text="nextRepayment.dueDate"></span></div>
        </div>
        <!-- 优惠券 -->
        <div class="choose-coupon" v-if="haveReloan">
          <div class="name">
            <span>Coupons</span>
          </div>
          <div class="select-coupon" v-if="defaultCouponId" @click="chooseCoupons()">
            <div  class="negtive" v-if="discountAmt" v-text="`-${common.singleToThousandsWithoutCarryBit(discountAmt)}`"></div>
            <div  class="negtive" v-else v-text="couponList.length"></div>
            <i class="van-icon van-icon-arrow van-cell__right-icon"></i>
          </div>
          <div v-else class="select-coupon not" @click="showDialog = true">Not available</div>
        </div>
      </div>
      <!-- 复借 -->
      <div class="re-loan" v-if="haveReloan">
        <div class="title">
          <div class="name">Re-loan</div>
        </div>
        <div class="reloan-tips">
          repayment discount for re-loan
        </div>
        <!-- 用户首选产品 -->
        <div class="loan-amount">
          <div class="title">
            Loan Amount
            <!-- <img v-if="loanType === 'BOOK_LOAN'" src="@/assets/images/common/book-loan-tips.png" alt=""> -->
            <div class="amount-add" v-if="preferChoiceProduct.tempAddLimit > 0">
              <div class="wrap">
                <div class="num" v-text="`Including ${common.singleToThousands(preferChoiceProduct.tempAddLimit)} limit increase`"></div>
                <div class="arrow"></div>
              </div>
            </div>
          </div>
          <loanAmountInputWayacredit
            key="prefer"
            :inputAmount="preferChoiceProduct.amount"
            :productInfo="preferChoiceProduct"
            type="prefer"
            @report="repaymentReport('new_loan_amount_click')"
            @startLoanCalculate="startLoanCalculate"
            @loanKeyUp="loanKeyUp"
            @loanInput="loanInput"
            @keyDown="keyDown"
          ></loanAmountInputWayacredit>
        </div>
        <loanTermsWayacredit
        v-model="preferChoiceProduct.productActive"
        @chooseType="chooseLoanTermType"
        :productSource="productSource"
        customType='prefer'
        :productList="loanDetail.productList"></loanTermsWayacredit>
        <newLoanDetailWayacredit @checkInsurance="checkInsurance" :loanDetail="preferChoiceProduct.loanApplyObj"></newLoanDetailWayacredit>
        <!-- 用户备选产品 -->
        <div class="alternative-loan" v-show="alternativeChoiceProduct.canShow">
          <div class="head" @click="changeAlternativeChoice">
            <img :src="alternativeChoiceProduct.isChoosen ? require('@/assets/images/wayacredit/choose_loan.png') : require('@/assets/images/wayacredit/unchoose_loan.png')" alt="">
            <div>Alternative-loan</div>
          </div>
          <div class="alternative-tips" :class="{'not-choose': !alternativeChoiceProduct.isChoosen}">
            Tips: <span>Only one loan disbursement</span> When you do not meet the borrowing requirements of the New Loan, we will lend with the alternative-loan to meet your financial needs.
          </div>
        </div>
        <div class="loan-amount" v-show="alternativeChoiceProduct.canShow && alternativeChoiceProduct.isChoosen">
          <div class="title">
            Loan Amount
            <!-- <img v-if="loanType === 'BOOK_LOAN'" src="@/assets/images/common/book-loan-tips.png" alt=""> -->
            <div class="amount-add" v-if="alternativeChoiceProduct.tempAddLimit > 0">
              <div class="wrap">
                <div class="num" v-text="`Including ${common.singleToThousands(alternativeChoiceProduct.tempAddLimit)} limit increase`"></div>
                <div class="arrow"></div>
              </div>
            </div>
          </div>
          <loanAmountInputWayacredit
            key="alternative"
            :inputAmount="alternativeChoiceProduct.amount"
            :productInfo="alternativeChoiceProduct"
            type="alternative"
            @report="repaymentReport('alternative_loan_amount_click')"
            @startLoanCalculate="startLoanCalculate"
            @loanKeyUp="loanKeyUp"
            @loanInput="loanInput"
            @keyDown="keyDown"
          ></loanAmountInputWayacredit>
        </div>
        <loanTermsWayacredit
        v-show="alternativeChoiceProduct.canShow && alternativeChoiceProduct.isChoosen"
        v-model="alternativeChoiceProduct.productActive"
        @chooseType="chooseLoanTermType"
        :productSource="productSource"
        customType='alternative'
        :productList="loanDetail.productList"></loanTermsWayacredit>
        <newLoanDetailWayacredit v-show="alternativeChoiceProduct.canShow && alternativeChoiceProduct.isChoosen" @checkInsurance="checkInsurance" :loanDetail="alternativeChoiceProduct.loanApplyObj"></newLoanDetailWayacredit>
      </div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="repay">
      <!-- 单期按钮控制逻辑 -->
      <template v-if="productType === 'single'">
        <div class="repay-re-loan" v-preventReClick="3000" @click="repayAndReLoan()">
          <div class="left">
            <div class="title">Repayment Amount</div>
            <div class="amount">
              <div class="after">
                <CMoney :currencyNum="common.singleToThousands(repaymentAmount)"></CMoney>
              </div>
              <div class="before" v-if="discountAmt" v-text="`${getRepayAmount}`"></div>
            </div>
          </div>
          <div class="right">Repay and Get another loan</div>
          <div class="amount-de" v-if="discountAmt">
            <div class="wrap">
              <div class="num" v-text="`-₦${(common.singleToThousandsWithoutCarryBit(discountAmt))}`"></div>
              <div class="arrow"></div>
            </div>
          </div>
        </div>
        <div class="only-repay">
          <div class="button" v-preventReClick="3000" @click="repayOnly()">Only Repay</div>
        </div>
      </template>
      <!-- 多期控制逻辑 -->
      <template v-else>
        <div class="repay-re-loan" v-preventReClick="3000" :class="{'inactive': getRepayAmount == 0}" v-if="repayCoupon.discountAmt" @click="haveReloan ? repayAndReLoan() : repayOnly()">
          <div class="left">
            <div class="title">Repayment Amount</div>
            <div class="amount">
              <div class="after">
                <CMoney :currencyNum="common.singleToThousands(getRepayAmount - (discountAmt ? discountAmt : 0))"></CMoney>
              </div>
              <div class="before" v-text="`₦${getRepayAmount}`"></div>
            </div>
          </div>
          <div class="right" :class="{'reloan': haveReloan}" v-text="haveReloan ? 'Repay and Get another loan' : 'Repay'"></div>
          <div class="amount-de">
            <div class="wrap">
              <div class="num" v-text="`-₦${(common.singleToThousandsWithoutCarryBit(discountAmt))}`"></div>
              <div class="arrow"></div>
            </div>
          </div>
        </div>
        <div class="singel-button" v-if="!discountAmt">
          <CButton @buttonClick="haveReloan ? repayAndReLoan() : repayOnly()" :className="`confirm ${haveReloan ? 'reloan' : ''} ${getRepayAmount == 0 ? 'inactive' : ''}`"
          :name="haveReloan ? 'Repay and Get another loan' : 'Repay'"></CButton>
        </div>
        <div class="only-repay">
          <div class="button" v-preventReClick="3000" :class="{'inactive': getRepayAmount == 0}" v-if="haveReloan" @click="repayOnly()">Only Repay</div>
        </div>
      </template>
    </div>
    <!-- 优惠券提示 -->
    <van-dialog v-model="showDialog" show-cancel-button className="dialog-after-repayment" :showConfirmButton="false" :showCancelButton="false">
        <div class="title">Note!</div>
        <div class="tips">There are currently no coupons available</div>
        <div class="dia-button">
          <div class="ok" @click="hideDialog()">OK</div>
          <div class="cancel" @click="hideDialog()">Cancel</div>
        </div>
    </van-dialog>
    <feedback :showFeedback="showFeedback" @close="closeFeedback()" />
    <reminderPopup v-model="showReminder" :day="dueRemindDay"></reminderPopup>
    <OnlyRepayPopup v-model="showOnlyRepayPopup" :amt="common.singleToThousands(maxTempAddLimit)" @reloanBtnClick="repayAndReLoan" @onlyRepayBtnClick="repayOnly"></OnlyRepayPopup>
  </div>
</template>

<script>
import publicMixns from '../repaymentIndexMixins.js'
export default {
    name: 'wayacreditRepayment',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.repayment-index{
  background: #1E1E20;
  min-height: 100%;
  .contact-us{
    width: 53px;
    height: 40px;
    margin-right: 18px;
  }
  .repayment{
    margin-top: 56px;
    padding-bottom: 120px;
    .next-repayment{
      background: #26282D;
      padding: 4px 24px;
      .next-content {
        border-radius: 30px;
        background: #1A2022;
        display: flex;
        align-items: center;
        padding: 0 16px;
      }
      .next-term {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
      .content {
        color: #00FFAD;
        font-family: Avenir;
        font-size: 10px;
        font-style: normal;
        font-weight: 800;
        text-align: left;
        padding: 4px 0;
      }
    }
    .repayment-amount{
      border-radius: 18px;
      border: 0.5px solid #FFF;
      background: rgba(40, 42, 48, 0.80);
      margin: 7px 12px 12px 12px;
      padding: 0 18px;
      min-height: 120px;
      padding-bottom: 12px;
      &.goldman{
        margin-top: 0px;
        border-top-left-radius: 0px;
        border-top-right-radius: 0px;
      }
      .title{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 15px;
        .left {
          font-family: Avenir-Medium;
          font-size: 12px;
          color: #fff;
        }
        .right {
          font-family: Avenir-Medium;
          font-size: 12px;
          color: $themeColor;
          text-align: right;
        }
      }
      .amount{
        display: flex;
        justify-content: flex-start;
        align-items: baseline;
        margin-top: 15px;
        .currency{
          margin-top: -19px;
          color: #00FFAD;
          font-family: Impact;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          margin-right: 5px;
        }
        .value{
          position: relative;
          .static{
            line-height: 48px;
            display: inline-block;
            width: 100%;
            height: 100%;
            font-family: DINAlternate-Bold;
            font-size: 24px;
            color: $themeColor;
            font-weight: 600;
            &.overdue{
              color: red;
            }
          }
          .input{
            font-family: DINAlternate-Bold;
            font-size: 24px;
            font-weight: 600;
            color: $themeColor;
            border: none;
            width: 100%;
            height: 100%;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 0;
            text-align: center;
            background: transparent;
            &.overdue{
              color: red;
            }
          }
          .edit{
            width: 14px;
            height: 14px;
            position: absolute;
            right: 10px;
            top: 21px;
          }
        }
        .edit{
          margin-left: 6px;
          height: 37px;
          width: 15px;
          position: relative;
          img {
            width: 15px;
            height: 15px;
            position: absolute;
          }
        }
      }
      .charge-fee{
        font-family: Avenir-Medium;
        font-size: 12px;
        color: #919DB3;
        transform: scale(0.9);
        margin-left: -12px;
        margin-bottom: 8px;
        margin-top: 5px;
        text-align: left;
        .money{
          color: #00FFAD;
        }
      }
      .choose-coupon{
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 25px;
        line-height: 25px;
        border-radius: 6px;
        margin-top: 8px;
        .name{
          color: rgba(255, 255, 255, 0.50);
          text-align: center;
          font-family: HarmonyOS Sans SC;
          font-size: 14px;
          font-style: normal;
          display: flex;
          align-items: center;
        }
        .select-coupon{
          display: flex;
          align-items: center;
          &.not{
            color: #919DB3;
            font-size: 14px;
          }
          .negtive{
            margin-right: 10px;
            color: $themeColor;
            font-size: 12px;
          }
        }
      }
    }

    .re-loan{
      border-radius: 18px;
      border: 0.5px solid #FFF;
      background: rgba(40, 42, 48, 0.80);
      margin: 7px 12px 12px 12px;
      padding: 0 18px;
      .title{
        display: flex;
        align-items: center;
        padding-top: 12px;
        margin-bottom: 14px;
        img{
          width: 19px;
          height: 19px;
        }
        .name{
          font-family: Avenir-Medium;
          font-size: 14px;
          color: #fff;
          margin-left: 5px;
          font-weight: 800;
        }
      }

      .reloan-tips{
        display: flex;
        text-align: left;
        border-radius: 15px;
        background: #1E1E20;
        color: rgba(255, 255, 255, 0.70);
        font-family: Avenir;
        font-size: 11px;
        font-style: normal;
        font-weight: 500;
        padding: 4px 8px;
        width: 76%;
      }
      .loan-amount {
        text-align: left;
        padding: 0 4px;
        padding-top: 8px;
        .title{
          text-align: left;
          line-height: 12px;
          font-family: Avenir-Medium;
          font-size: 12px;
          color: #fff;
          margin-bottom: 0;
          img{
            width: 135px;
            height: 15px;
            margin-left: 5px;
          }
        }
        .input-amount{
          height: 50px;
          display: flex;
          align-items: flex-end;
          position: relative;
          .currency{
            margin-top: -19px;
            font-family: Avenir-Medium;
            font-size: 20px;
            color: #fff;
          }
          input{
            border: none;
            height: 35px;
            color: $themeColor;
            line-height: 35px;
            margin-left: 5px;
            width: calc(100% - 65px);
            outline: none;
            position: relative;
            font-family: DINAlternate-Bold;
            font-size: 35px;
            &::placeholder{
              font-size: 12px;
              font-weight: 600;
              color: rgb(177, 176, 176);
              position: absolute;
              top: 22px;
            }
          }
          img{
            width: 15px;
            height: 15px;
            position: absolute;
            right: 23px;
            top: 25px;
          }
        }
        .amount-add{
          position: absolute;
          left: 76px;
          top: 7px;
          transform: scale(.85);
          .wrap{
            position: relative;
            .num{
              background-image: linear-gradient(90deg, #C78900 0%, #F0C400 100%);
              color: #ffffff;
              width: auto;
              white-space: nowrap;
              padding: 2px 4px;
              font-size: 12px;
            }
            .arrow{
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 3.5px 5px 3.5px 0;
              border-color: transparent #C78900 transparent transparent;
              position: absolute;
              left: -5px;
              top: 5px;
            }
          }
        }
      }
      .alternative-loan{
        border-top: 1px dashed rgba(196,202,213,1);
        padding-top: 15px;
        .head{
          display: flex;
          align-items: center;
          font-family: Avenir-Heavy;
          font-size: 14px;
          color: #fff;
          font-weight: 800;
          img {
            width: 15px;
            height: 15px;
            margin-right: 10px;
          }
        }
        .alternative-tips{
          font-family: Avenir-Medium;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.60);
          font-weight: 500;
          text-align: left;
          transform: scale(.94);
          margin-left: -10px;
          margin-top: 5px;
          &.not-choose {
            padding-bottom: 15px;
          }
          span{
            color: rgba(255, 38, 38, 0.60);
            font-weight: 800;
          }
        }
      }
      .select-loan-tenor{
        position: relative;
        margin-top: 20px;
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #536887;
        &::before {
          position: absolute;
          box-sizing: border-box;
          content: " ";
          pointer-events: none;
          right: -21px;
          top: -3px;
          left: 3px;
          border-bottom: 2px solid #ebedf0;
          transform: scaleY(0.5);
        }
      }
      .loan-terms{
        margin-top: 10px;
        ::v-deep .select-loan-terms{
          height: 76px;
          .left{
            .title {
              font-size: 12px;
              margin-bottom: 4px;
              font-weight: 500;
            }
            .days{
              font-weight: 600;
              margin-bottom: 2px;
            }
          }
        }
      }
    }
  }
  .loan-detail{
    &.loan{
      margin-top: 10px;
    }

    &.select{
      .right{
            display: flex;
            align-items: center;
            height: 40px;
            .no-selected{
              font-size: 14px;
              font-family: Avenir, Avenir-Medium;
              font-weight: 500;
              color: #c4cad5;
            }
            .available{
              color: #fa7809;
            }
            .selected{
              color: #00FFAD;
            }
            .checked{
              color: $themeColor;
            }
            .no-data{
              color: #c4cad5;
            }
          }
    }

    .li{
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 55px;
      position: relative;
      &.border-line::before {
          position: absolute;
          box-sizing: border-box;
          content: " ";
          pointer-events: none;
          right: -21px;
          top: -3px;
          left: 3px;
          border-bottom: 2px solid #ebedf0;
          transform: scaleY(0.5);
        }
      .item{
        font-family: Avenir-Medium;
        font-size: 14px;
        color: #536887;
      }
      .check-acc{
          display: flex;
          align-items: center;
          img{
            width: 18px;
            height: 18px;
            margin-left: 5px;
          }
        }
      .content{
        font-family: DINAlternate-Bold;
        font-size: 14px;
        color: #00FFAD;
        text-align: right;

        &.right{
          display: flex;
          align-items: center;
          .checked{
            color: $themeColor;
          }
        }
        &.outstanding{
          color: #02b17b;
        }
        &.overdue{
          color: #E13334;
        }
        &.pending{
          color: $themeColor;
        }
        &.under-review{
          color: $themeColor;
        }
        &.distursing{
          color: $themeColor;
        }
        ::v-deep .c-money{
          display: flex;
          align-items: flex-end;
          .monetary-unit{
            font-family: DINAlternate-Bold;
            font-size: 12px;
            color: #fff;
            font-weight: 600;
          }
          .currency-num{
            font-family: DINAlternate-Bold;
            font-size: 14px;
            color: #fff;
            font-weight: 600;
          }
        }
      }
      .cal{
        font-size: 14px;
        color: #c4cad5;
        font-weight: 500;
      }
    }
  }
  .repay{
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0px;
    background: linear-gradient(180deg, rgba(30, 30, 32, 0.00) 0%, #1E1E20 100%);
    .repay-re-loan{
      background: $themeColor;
      height: 49px;
      margin: 0 18px;
      border-radius: 8px;
      color: #1E1E20;
      padding: 6px 12px 7px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &.inactive{
        // background: #caefff;
      }
      .left{
        text-align: left;
        .title{
          font-family: Avenir-Medium;
          font-size: 12px;
          margin-top: -11px;
          margin-bottom: 4px;
        }
        .amount{
          display: flex;
          align-items: flex-end;
          ::v-deep .c-money{
            display: flex;
            align-items: flex-end;
            .monetary-unit{
              font-size: 14px;
              color: #1E1E20;
              font-weight: 700;
            }
            .currency-num{
              font-family: DINAlternate-Bold;
              font-size: 14px;
              color: #1E1E20;
              margin-left: 2px;
              font-weight: 700;
            }
          }
          .before{
            font-size: 12px;
            margin-left: 3px;
            text-decoration: line-through;
            color: #1E1E20;
          }
        }
      }
      .right{
        font-family: Avenir-Medium;
        font-size: 18px;
        color: #1E1E20;
        text-align: right;
        &.reloan{
          font-size: 16px;
        }
      }
      .amount-de{
        position: absolute;
        right: 26px;
        top: -13px;
        transform: scale(0.85);
        .wrap{
          position: relative;
          .num{
            background-image: linear-gradient(180deg, #FFD487 5%, #FFA316 100%);
            color: #1E1E20;
            width: auto;
            white-space: nowrap;
            padding: 4px 9px;
            border-radius: 4px;
            font-size: 12px;
          }
          .arrow{
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 5px 0 5px 7px;
            border-color: transparent transparent transparent #FFA316;
            position: absolute;
            left: 0px;
            top: 16px;
          }
        }
      }
    }

    .singel-button{
      .confirm {
        width: 90%;
        margin-bottom: 30px;
        background: $themeColor;
        color: #1E1E20;
        &.reloan{
          margin-bottom: 0px;
        }
      }
      .inactive{
        // background: #caefff;
      }
    }
    .only-repay{
      display: flex;
      justify-content: center;
      background: linear-gradient(180deg, rgba(30, 30, 32, 0.00) 0%, #1E1E20 100%);
      height: 55px;
      .button{
        font-family: Avenir-Heavy;
        font-size: 16px;
        color: $themeColor;
        text-align: center;
        margin-top: 14px;
        border-bottom: 1px solid $themeColor;
        padding-bottom: 3px;
        height: 20px;
        // &.inactive{
        //   color: #caefff;
        //   border-bottom: 1px solid #caefff;
        // }
      }
    }
  }
  .van-popup{
    background: rgba(120,120,120,0);
    .close{
      text-align: left;
      img{
        width: 38px;
        height: 38px;
        margin-left: 16px;
        margin-bottom: 3px;
      }
    }
    .seal-overdue{
      width: 174px;
      height: 166px;
      position: absolute;
      left: 27%;
      top: 194px;
    }
    &.plan-list{
      max-height: 90%;
      overflow-y: hidden;
      .warp-list{
        height: 500px;
        overflow-y: auto;
      }
    }
  }
  .dialog-after-repayment{
    color: #536887;
    padding: 12px 15px;
    width: 254px;
    .van-dialog__content{
      .title{
          height: 40px;
          line-height: 40px;
          text-align: left;
          margin-bottom: 5px;
          color: #536887;
          font-weight: 700;
      }
      .tips{
          line-height: 20px;
          text-align: left;
      }
      .dia-button{
          margin-top: 15px;
          .ok{
              height: 40px;
              line-height: 40px;
              background: $themeColor;
              color: #ffffff;
              border-radius: 5px;
          }
          .cancel{
              height: 40px;
              line-height: 40px;
              background: #ffffff;
              color:  $themeColor;
              border-radius: 5px;
          }
      }
    }
    .van-dialog__content--isolated{
      min-height: 70px;
    }
    .van-dialog__message--left {
        padding: 15px 24px;
    }
  }
}
</style>
