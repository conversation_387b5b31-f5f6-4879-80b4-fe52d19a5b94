<template>
  <div class="load-url-page">
    <c-header :titleName="title"></c-header>
    <iframe width="100%" :src="url" height="auto" id="iframe" frameborder="0"></iframe>
  </div>
</template>

<script>
import { h5Loading, h5HideLoading } from "@/assets/js/native";
export default {
  name: 'loadUrlPage',
  data() {
    return {
      url: '',
      title: ''
    };
  },
  created() {
    h5Loading()
    this.url = this.$route.query.url
    this.title = this.$route.query.title
    window.scrollTo(0, 0)
    setTimeout(() => {
      h5HideLoading()
    }, 800)
  },
  mounted() {
    
  },

  methods: {
    
  },
};
</script>

<style lang="scss" scoped>
  .load-url-page{
    height: 100%;
  }
  #iframe{
    height: calc(100% - 56px);
    padding-top: 56px;
  }
</style>