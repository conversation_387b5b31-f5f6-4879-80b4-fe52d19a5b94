import {
  getGpsInfo, getWifiList, getDeviceId, gotoHomeActivity, activateTradeBigdata, getLocation, getBatchNo, activateTradeBigdataAC,
  getCustId, getCurrentAppVersion, getCurrentAppVersionName, gotoBalanceTab, getOsVersionCode } from "@/assets/js/native";
import editOff from '@/assets/images/edit-off.png';
import editOn from '@/assets/images/edit-on.png';
import editOnWaya from '@/assets/images/wayacredit/edit.png';
import closeImg from '@/assets/images/cancel.png';
import unChecked from '@/assets/images/un-paid.png';
import pastChecked from '@/assets/images/past-paid.png';
import arrowDown from '@/assets/images/arrow-down.png';
import pick from '@/assets/images/pick.png';
import CMoney from '@/components/c-money.vue';
import loanTerms from '@/components/loan-terms.vue';
import loanTermsWayacredit from '@/components/loan-terms-wayacredit.vue';
import addPersonalInformation from '@/components/addPersonalInformation.vue';
import newLoanDetail from '@/components/new-loan-detail.vue';
import newLoanDetailWayacredit from './components/new-loan-detail-wayacredit.vue';
import CButton from '@/components/c-button.vue';
import loanAmountInput from '@/components/loan-amount-input.vue';
import loanAmountInputWayacredit from './components/loan-amount-input-wayacredit.vue';
import reminderPopup from './components/reminder-popup.vue';
import feedback from './components/feedback.vue';
import OnlyRepayPopup from './components/only-repay-popup.vue';
import { mapState } from 'vuex';
import { guid, dateFormat, inTimeAreaCal } from '@/assets/js/common';
import api from "@/api/interface";
export default {
  name: 'repaymentIndex',
  components: {
    CMoney,
    CButton,
    loanTerms,
    loanTermsWayacredit,
    newLoanDetail,
    newLoanDetailWayacredit,
    loanAmountInput,
    loanAmountInputWayacredit,
    feedback,
    reminderPopup,
    OnlyRepayPopup,
    addPersonalInformation
  },
  computed: {
    ...mapState(['userInfor', 'repayCoupon', 'statusObj', 'uid', 'deviceType', 'addHeader', 'productSource', 'repaySchduleList', 'supportedFeatures', 'uploadDocuments']),
    // 还款金额
    getRepayAmount: function() {
      return +(this.repaymentDetail.totalAmt ? this.repaymentDetail.totalAmt.replace(/,/g, '') : 0);
    },
    repaymentAmount: function() {
      let vm = this;
      let value = +vm.common.floatMinus(vm.getRepayAmount, (vm.discountAmt ? vm.discountAmt : 0));
      // 用户使用还款优惠券时，判断用户还款金额-优惠券优惠金额＜最低还款金额时，不支持使用优惠券
      if (value < vm.minRepayAmount && vm.discountAmt) {
        vm.isLowerAmount = true;
      } else {
        vm.isLowerAmount = false;
      }
      return value > 0 ? value : 0;
    },
    loanTypeInfor: function() {
      return this.showInfor[this.loanType];
    },
    discountAmt() { // 减免的利息
      let interestRelief = 0;
      let toBePaidInterest = 0; // 待还利息
      // let paidInterest = 0; // 已还利息
      // let totalInterest = 0; // 原应还利息
      // 应还利息 interest; 已还利息 paidInterest; 这两个字段,  这两个加起来就是原始应还.
      // 目前是只在最后一期使用优惠券，所以取第一期即可
      if(this.repaySchduleList.length > 0) {
        toBePaidInterest = this.repaySchduleList[0].interest;
        // paidInterest = this.repaySchduleList[0].paidInterest;
        // totalInterest = toBePaidInterest + paidInterest;
      }

      if (this.repayCoupon.couponMethod === 'A') {
        interestRelief = this.repayCoupon.denominations;
      } else if (this.repayCoupon.couponMethod === 'R') {
        interestRelief = this.repayCoupon.denominations * toBePaidInterest;
      }
      console.log('interestRelief', interestRelief, this.repayCoupon.maxAmt);
      if (interestRelief > this.repayCoupon.maxAmt) { // 若大于最大减免金额，使用最大减免金额
        interestRelief = this.repayCoupon.maxAmt;
      }

      // 减免利息金额不能大于待还利息金额，取两者最小值
      if (interestRelief > toBePaidInterest) {
        interestRelief = toBePaidInterest
      }
      console.log('interestRelief', interestRelief);
      return interestRelief;
    }
  },
  data() {
    return {
      editOff,
      editOn,
      editOnWaya,
      closeImg,
      arrowDown,
      unChecked,
      pastChecked,
      pick,
      editStatus: false,
      showDialog: false,
      productType: '', // 还款产品类型 single/multiple
      repaymentDetail: { // 还款详情
        totalAmt: '', // 还款金额
        totalAmtBackUp: '', // 金额备份
        passAmount: '', // 通道费
        loanStatus: '' // 借款状态
      },
      defaultCouponId: '', // 后台默认的优惠券
      loanDetail:{ // 借款基础信息
        amount: 0,
        usableLmtAmt: 0,
        productList: [] // 产品列表
      },
      userInfo: {
        custId: '',
        xcrossCustId: ''
      },
      deviceId: '',
      wifiList: '',
      showProductList: false,
      hasChangedAmt: false, // 记录金额的修改
      currDate: '', // 服务器时间
      couponList: [], // 优惠券列表
      haveReloan: false, // 是否存在复借
      temHaveReloan: false, // 是否存在复借(临时缓存)
      reloanControl: true, // 是否显示复借模块（在truemonyios中不允许复借）
      defaultLoanAccount: {}, // 默认银行账户
      currentAppVersionName: '',
      appVersion: '',
      userRepaymentObj: {
        nc: '',
        ncStatus: '',
      }, // 用户存在的借款对象 可能存在nc(pc也包含在其中)
      nextRepayment: {
        amount: '',
        dueDate: ''
      },
      canChangeRepayAmt: false, // 可编辑金额
      preferChoiceProduct: { // 用户首选产品
        canShow: true, // 是否展示
        isChoosen: false, // 是否被勾选
        amount: '', // 额度
        oldAmount: '', // 缓存的输入额度
        chooseTypeData: { // 选择的产品对应的最大值和最小值。
          minAmount: 0,
          maxAmount: 0
        },
        loanApplyObj: { // 复借计划计算详情
          type: 'prefer',
          showDetail: false, // 是否展示详情
          caculateStatus: false, // 试算状态
          checkShow: '' // 是否默认勾选保险费
        },
        productActive: -1, // 产品勾选索引
      },
      alternativeChoiceProduct: { // 用户备选产品
        canShow: false, // 是否展示
        isChoosen: false, // 是否被勾选
        amount: '', // 额度
        oldAmount: '', // 缓存的输入额度
        chooseTypeData: { // 选择的产品对应的最大值和最小值。
          minAmount: 0,
          maxAmount: 0
        },
        loanApplyObj: { // 复借计划计算详情
          type: 'alternative',
          showDetail: false, // 是否展示详情
          caculateStatus: false, // 试算状态
          checkShow: '' // 是否默认勾选保险费
        },
        productActive: -1 // 产品勾选索引
      },
      showFeedback: false,
      isUsedCoupon: {}, // 是否可用的优惠券
      minRepayAmount: 50, // 最小还款金额
      isLowerAmount: false, // 是否触发优惠券优惠金额低于最小还款金额
      loanType: 'AGAIN_LOAN', // 借款类型：BOOK_LOAN表示预约借款，AGAIN_LOAN表示还款再借
      showInfor: { // 展示的信息
        BOOK_LOAN: { // 预约借款
          title: 'Book Next Loan to Increase Your Limit',
          tips: 'The order will be effective after your current loan is settled and verified by your OTP.',
          reButton: 'Repay and Book next loan'
        },
        AGAIN_LOAN: { // 复借
          title: 'Get A New Loan',
          // tips: 'repayment discount for re-loan',
          reButton: 'Repay and Get A New Loan'
        }
      },
      havePreRepayment: false, // 是否有符合预约借款的前置借款逻辑
      bookSwitchIsOpen: false, // 预约借款开关
      isRevolvingLoan: false, // 是否是循环贷用户
      loanSpan: 0, // 当前借款的周期
      showGoldmanTip: '', // 是否显示提示
      transferNow: '', // 缓存的本地标识
      showBanner: false, // 显示广告条
      showReminder: false, // 显示弹窗
      dueRemindFlag: 'N', // 提醒开关
      dueRemindDay: '', // 提醒日期
      showVerdictAnnouncement: false, // 是否显示判决书
      showOnlyRepayPopup: false,
      maxTempAddLimit: '', // 主副产品临时增量额度金额
      showAddPersonalInformation: false, // 是否显示补件弹窗
      showIncreaseCredit: '', // 是否显示补件banner
      androidVersion: 0, // 安卓版本
      fromName: '' // 页面来源
    };
  },
  watch: {
    'editStatus': function(newValue, oldValue) {
      let vm = this;
      if (oldValue && vm.hasChangedAmt) { // 修改完成后，触发计算通道费逻辑。
        vm.queryPassAmount(vm.getRepayAmount);
        if (vm.isLowerAmount) {
          vm.$store.commit('SET_REPAYCOUPON', {});
        }
        if (parseFloat(vm.repaymentDetail.totalAmt.replace(/,/g, '')) < parseFloat(vm.repaymentDetail.totalAmtBackUp.replace(/,/g, ''))) {
          // 输入金额小于初始金额，清空优惠券
          vm.$store.commit('SET_REPAYCOUPON', {});
        }
      }
    },
    repayCoupon: function(newValue) {
      // 选择了优惠券
      if (newValue.couponId) {
        // 重置金额为初始金额
        this.repaymentDetail.totalAmt = this.repaymentDetail.totalAmtBackUp;
      }
    },
    'alternativeChoiceProduct.amount': function(newValue, oldValue) {
      this.alternativeChoiceProduct.oldAmount = oldValue;
    },
    'preferChoiceProduct.amount': function(newValue, oldValue) {
      console.log('preferChoiceProduct.amount oldValue', oldValue)
      this.preferChoiceProduct.oldAmount = oldValue;
    },
    showReminder: function(newValue) {
      // console.log('showReminder', newValue);
      if (!newValue) {
        this.repaymentReport('Repayment-Popup_click');
      }
    },
    showAddPersonalInformation: function(newValue) {
      if (newValue) {
        this.repaymentReport('repay_reloan_addinfo_popup__view');
      }
    }
  },
  methods: {
    // 提现金额获取
    getAmount(userProductType) {
      const vm = this;
      const amount = vm[`${userProductType}ChoiceProduct`].amount;
      return +(amount ? amount.replace(/,/g, '') : 0);
    },
    myBackFun() {
      gotoHomeActivity('repaymentIndex');
    },
    hideDialog() {
      this.showDialog = false;
    },
    // 编辑金额
    editAmount() {
      let vm = this;
      vm.repaymentReport('edit_repayment_amount_click');
      vm.editStatus = !vm.editStatus;
      if (vm.editStatus) {
        vm.$nextTick(function() {
          vm.$refs.inputAmt.focus();
        });
      }
    },
    blurStatus(e) {
      let vm = this;
      if (e.srcElement._prevClass !== 'input' && vm.editStatus && e.srcElement._prevClass !== 'static' && e.srcElement._prevClass !== 'edit' && e.srcElement._prevClass !== 'edit-input') {
        vm.editStatus = !vm.editStatus;
      }
    },
    // 输入控制
    keyDown(e) {
      let keyCode = e.keyCode;
      if ((keyCode < 48 || keyCode > 57) && keyCode != 8) {
        e.preventDefault();
      }
    },
    // 还款金额输入监听
    keyUp(e) {
      console.log(e);
      let vm = this;
      let value = vm.getRepayAmount;
      vm.hasChangedAmt = true;
      if (value) {
        if (value > vm.repaymentDetail.totalAmtBackUp.replace(/,/g, '')) {
          vm.repaymentDetail.totalAmt = vm.repaymentDetail.totalAmtBackUp;
        } else {
          vm.repaymentDetail.totalAmt = vm.common.singleToThousands(value);
        }
      } else {
        vm.repaymentDetail.totalAmt = 0;
      }
    },
    // 提现金额监听
    loanKeyUp(obj) {
      let vm = this;
      console.log('obj', obj);
      let value = +(obj.amount ? obj.amount.replace(/,/g, '') : 0);
      const choiceProduct = vm[`${obj.type}ChoiceProduct`];
      if (value) {
        if (value > choiceProduct.chooseTypeData.maxAmount) {
          vm.$toast({
            message: `<div style="width: 200px">At most ₦${vm.common.singleToThousands(choiceProduct.chooseTypeData.maxAmount)}</div>`,
            type: 'html'
          });
          console.log('choiceProduct.oldAmount', choiceProduct.oldAmount);
          choiceProduct.amount = choiceProduct.oldAmount;
        } else {
          choiceProduct.amount = vm.common.singleToThousands(value);
        }
      } else {
        choiceProduct.amount = 0;
      }
    },
    loanInput(obj) {
       let vm = this;
      const choiceProduct = vm[`${obj.type}ChoiceProduct`];
      choiceProduct.amount = obj.amount;
    },
    // 数据上报
    repaymentReport(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'repaymentIndex',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    showPaymentList() {
      const vm = this;
      vm.$router.push({path: '/termDetail'});
    },
    startNcHaveReloan() {
      let vm = this;
      // 需要确保先获取额度再进行产品的勾选。
      return vm.queryCustRepayInfo().then(() => {
        // 多期时，最后一期是当前期才会出现复借的逻辑。同时，用户还需要匹配到产品。
        if (vm.loanDetail.productList.length === 0) {
          vm.temHaveReloan = false;
        }
        vm.haveReloan = vm.temHaveReloan && vm.reloanControl;
        if (vm.haveReloan) {
          vm.repaymentReport('new_loan_activity');
        }
        vm.chooseType(vm.preferChoiceProduct.productActive, 0, 0, 'prefer');
        if (vm.alternativeChoiceProduct.canShow) {
          vm.chooseType(vm.alternativeChoiceProduct.productActive, 0, 0, 'alternative');
          vm.repaymentReport('alternative_loan_activity');
        }
        let coupon = {}
        vm.couponList.forEach(item => {
          if (item.couponId === vm.isUsedCoupon.couponId) {
            vm.isUsedCoupon.useScene = item.useScene;
            coupon = item;
          }
        })
        if (vm.isUsedCoupon.couponId) {
          if (vm.haveReloan) {
            vm.defaultCouponId = vm.isUsedCoupon.couponId;
            vm.$store.commit('SET_REPAYCOUPON', {
              ...vm.isUsedCoupon,
              ...coupon
            });
          }
          // 用户使用还款优惠券时，判断用户还款金额-优惠券优惠金额＜最低还款金额时，不支持使用优惠券
          if (vm.repaymentAmount < vm.minRepayAmount) {
            vm.$store.commit('SET_REPAYCOUPON', {});
            vm.defaultCouponId = ''
          }
        }
        // 使用优惠券后优惠券金额等于0，则清空优惠券
        if (this.repayCoupon.couponId && this.discountAmt === 0) {
          this.$store.commit('SET_REPAYCOUPON', {});
        }
      }).catch(() => {
        vm.chooseType(vm.preferChoiceProduct.productActive, 0, 0, 'prefer');
      })
    },
    // 处理NC系列的数据
    dealNcPlanList(plans) {
      const vm = this;
      let totalAmt = 0;
      let passAmount = 0;
      let haveChecked = false;
      let haveReloan = false;
      let productType = '';
      let interest = 0; // 利息
      let obj = {
        totalAmt: '',
        totalAmtBackUp: '',
        passAmount: '',
        interest: '',
        repaySchduleList: '',
        haveReloan: '',
        productType: '',
        nextRepayment: {
          amount: '',
          dueDate: ''
        },
        canChangeRepayAmt: false
      }
      if (plans.length > 1) { // 分期逻辑
        vm.repaymentReport('repayonly_button_view');
        productType = 'multiple';
        // 多期产品仅当借据当前期为最后一期（还款中或已逾期状态），还款页面才展示复借模块
        if (plans[plans.length - 2].termStatus === 'F') {
          vm.repaymentReport('repay_and_reloan_button_view');
          haveReloan = true;
        }
        // 当前未还账单为倒数第二期账单且该笔借据为N状态的时候，新增提供预约下一笔借款功能。最终的判断，还要结合循环贷状态以及后台开关判断。
        if (plans.length === 2 && plans[0].totalTerm >= 2 && plans[0].termStatus === 'N') {
          vm.repaymentReport('repay_book_button_view');
          vm.havePreRepayment = true;
          vm.loanSpan = plans[0].loanSpan;
        }
      } else {
        productType = 'single';
        vm.repaymentReport('repay_and_reloan_button_view');
        haveReloan = true;
      }
      console.log('plans.forEach', plans);
      plans.forEach((item) => {
        let status = item.termStatus;
        if (item.showGoldmanTip === 'Y') {
          this.showGoldmanTip = 'Y'
        }
        if (item.promiseRepaydate) { // 存在减免时间，则需要把减免金额加回来展示
          item.reduceBeforRepaymentAmount = vm.common.floatAdd(item.repaymentAmount, item.reductAmt)
        }
        item.status = item.termStatus;
        if (status === 'O' || status === 'N' || status === 'P') { // 只有这三种状态可被勾选
          item.canCheck = true;
          // 默认勾选当前期
          if (item.dueDate <= item.businessDate) {
            obj.canChangeRepayAmt = true;
            item.checked = true;
            haveChecked = true;
            totalAmt = vm.common.floatAdd(totalAmt, item.repaymentAmount);
            passAmount = vm.common.floatAdd(item.passAmount, passAmount);
            interest = vm.common.floatAdd(item.interest, interest);
            console.log('passAmount', item.passAmount);
          } else {
            item.checked = false;
          }
        } else {
          item.canCheck = false;
        }
      });
      if (haveChecked === false) { // 若用户提现还款，则默认勾选下一期未还的。
        let dueDate = '';
        plans.forEach(item => {
          let status = item.termStatus;
          if ((dueDate === '' || dueDate === item.dueDate) && (status === 'O' || status === 'N' || status === 'P')) {
            if (dueDate === '') {
              dueDate = item.dueDate;
            }
            haveChecked = true;
            item.checked = true;
            totalAmt = vm.common.floatAdd(totalAmt, item.repaymentAmount);
            passAmount = vm.common.floatAdd(item.passAmount, passAmount);
            interest = vm.common.floatAdd(item.interest, interest);
          }
        })
      }
      // 下一期显示: 单期和仅剩一期不展示
      if (productType === 'multiple' && plans[plans.length - 2].termStatus != 'F') {
        let dueDate = '';
        let amount = 0;
        plans.forEach(item => {
          item.dueDate = dateFormat(new Date(item.dueDate.replace(/-/g, '/')), 'MM/dd/yyyy');
          if ((dueDate === '' || dueDate === item.dueDate) && item.checked === false) {
            if (dueDate === '') {
              dueDate = item.dueDate;
            }
            amount = vm.common.floatAdd(amount, item.repaymentAmount);
          }
        });
        obj.nextRepayment.dueDate = dueDate;
        obj.nextRepayment.amount = amount;
      }
      obj.totalAmt = obj.totalAmtBackUp = vm.common.thousandsToFixed(totalAmt);
      obj.passAmount = vm.common.thousandsToFixed(passAmount);
      obj.interest = interest; // 只用来计算优惠券的值，不需要格式化
      obj.repaySchduleList = plans;
      obj.productType = productType;
      obj.haveReloan = haveReloan;
      console.log('ncobj', obj);
      return obj;
    },
    // 获取通道费
    queryPassAmount() {
    let vm = this;
      return new Promise((resolve, reject) => {
        vm.$http(api.repayment.queryPassAmount, {
            "data":{
                "amount": vm.getRepayAmount,
                "loanId": vm.repaySchduleList[0].loanId
            }
        }).then(res => {
          vm.repaymentDetail.passAmount = vm.common.thousandsToFixed(res.passAmount);
          // 该处不知道什么更新了数据后不刷新页面数据，后面排查，先强制更新。
          vm.$forceUpdate();
          resolve();
        }).catch(e => {
          console.log(e);
          reject(e);
        });
      })
    },
    // 选择分期产品
    chooseLoanTermType(param) {
      const vm = this;
      if (param.customType === 'prefer' && param.index === vm.alternativeChoiceProduct.productActive && vm.alternativeChoiceProduct.isChoosen) {
        vm.$toast({
          message: 'The Alternative-loan can not be the same with the New loan.'
        });
        return
      }
      if (param.customType === 'alternative' && param.index === vm.preferChoiceProduct.productActive && vm.alternativeChoiceProduct.isChoosen) {
        vm.$toast({
          message: 'The Alternative-loan can not be the same with the New loan.'
        });
        return
      }
      vm.chooseType(param.index, param.type, '', param.customType);
    },
    // 选择产品类型
    /**
     * @param index 索引值
     * @param chooseType 选择的类型
     * @param showToast 是否展示额度不符合区间时的提示
     * @param userProductType 用户产品类型
    */
    chooseType(index, chooseType, showToast, userProductType) {
      let vm = this;
      if (index < 0) return;
      let detail = vm.loanDetail;
      let product = detail.productList[index];
      let productFee = product.productFee[0];
      let amount = vm.getAmount(userProductType);
      let maxAmount = 0;
      let minAmount = 0;
      const choiceProduct = vm[`${userProductType}ChoiceProduct`];
      choiceProduct.productActive = index;
      choiceProduct.loanApplyObj.caculateStatus = false;
      if (productFee.tempAddLimit > 0) {
        choiceProduct.tempAddLimit = productFee.tempAddLimit;
        vm.repaymentReport('reloan_credit_increase_label_view');
      }
      vm.showProductList = false;
      // 输入的值大小控制。
      maxAmount = productFee.maxAmt;
      minAmount = productFee.minAmt;
      if (!showToast) { // 切换产品时，使用产品的最大值。
        choiceProduct.amount = vm.thousands(maxAmount);
      } else {
        if ((maxAmount < amount || minAmount > amount)) { // 正常输入时，只有不在产品区间才设置为最大值。
          choiceProduct.amount = vm.thousands(maxAmount);
          if (maxAmount < amount) {
            vm.$toast({
              message: `<div style="width: 240px">Withdraw amount is ₦${minAmount}-₦${maxAmount}</div>`,
              type: 'html'
            });
          } else {
            vm.$toast({
              message: `<div style="width: 240px">At least ₦${vm.common.singleToThousands(minAmount)}</div>`,
              type: 'html'
            });
          }
        }
      }
      choiceProduct.chooseTypeData.minAmount = minAmount;
      choiceProduct.chooseTypeData.maxAmount = maxAmount;
      if ((productFee.insuranceRate && productFee.insuranceRate.feeRate > 0) && choiceProduct.loanApplyObj.checkShow === '') {
        choiceProduct.loanApplyObj.checkShow = true;
      }
      let enableInsurance = (productFee.insuranceRate && productFee.insuranceRate.feeRate > 0) ? choiceProduct.loanApplyObj.checkShow : '';
      if (index > -1) {
        vm.loanApplyCalc({
          loanAmt: vm.getAmount(userProductType),
          loanSpan: product.borrowCount,
          loanTerm: productFee.term,
          loanType: product.loanType,
          needEncryptContract: true, // 需要获取base64的合同
          productFeeId: productFee.productFeeId,
          txnScene: product.txnScene,
          enableInsurance: enableInsurance
        }).then(res => {
          console.log('还款试算--loanCalculate4nc-res', res)
          let insuranceFee = 0;
          if (enableInsurance) {
            res.loanCalcVOList.forEach(item => {
              if (item.key === 'Credit Protection Service') { // 若字段改变，后端也要变更这个字段
                insuranceFee = item.value;
              }
            })
          }
          if (res.plans.length > 0) {
            res.plans.forEach(item => {
              item.termAmount = vm.common.thousandsToFixed(item.termAmount);
              if (enableInsurance && item.termFeeList.length > 0) {
                item.insuranceAmountOutstd = item.termFeeList && item.termFeeList[0].INSURANCE_FEE;
              }
              item.dueDate = dateFormat(new Date(item.dueDate.replace(/-/g, '/')), 'MM/dd/yyyy');
            });
            res.loanCalcVOList.forEach(item => {
              if (item.key === 'Received') { // 若字段改变，后端也要变更这个字段
                choiceProduct.loanApplyObj.received = vm.common.thousandsToFixed(item.value);
              }
            })
            choiceProduct.loanApplyObj.pltfFeeAmt = res.pltfFeeAmt > 0 ? vm.common.thousandsToFixed(res.pltfFeeAmt) : '';
            choiceProduct.loanApplyObj.plans = res.plans;
            choiceProduct.loanApplyObj.showInsurance = productFee.insuranceRate && productFee.insuranceRate.feeRate > 0;
            choiceProduct.loanApplyObj.insuranceFee = vm.common.thousandsToFixed(insuranceFee);
          }
          choiceProduct.loanApplyObj.caculateStatus = true;
        }).catch(e => {
          console.log(e);
        })
      }
      if (chooseType) {
        if (productFee.term === 1) {
          vm.repaymentReport(`${userProductType === 'prefer' ? 'new_loan_single' : 'alternative_loan'}_${product.type.replace(/\s/g, '_')}_days_click`);
        } else {
          vm.repaymentReport(`${userProductType === 'prefer' ? 'new_loan_multi' : 'alternative_loan'}_${product.type.replace(/\s/g, '_')}_days_click`);
        }
      }
    },
    changeNum(num) {
      if (num.match(/.00/)) {
        return num.replace(/.00/, '');
      } else {
        return num;
      }
    },
    // 后台计算分期产品还款计划
    loanApplyCalc(data) {
      let vm = this;
      console.log('loanApplyCalc', data);
      return  new Promise((resolve, reject) => {
        vm.$http(api.commonBlc.loanCalculate4nc, {
          data:{
            ...data
          }
        }).then(res => {
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    thousands(num) {
      return this.common.singleToThousands(num);
    },
    goActivateTradeBigdata() {
      let vm = this;
      getDeviceId().then(deviceId => {
        vm.deviceId = deviceId;
      });
      getWifiList().then(wifiList => {
        vm.wifiList = wifiList || '';
      })
      if (vm.deviceType !== 'AC') {
        window.getGpsInfoCb = function (res) {
          vm.gpsInfo = JSON.parse(vm.$decode(res));
        }
        getGpsInfo('getGpsInfoCb'); // 回调
        // 需要调用这个接口之后，才可以正常提现。
        let uid = guid();
        getCustId().then(custId => {
          console.log(uid, custId, 'activateTradeBigdataCb');
          if (custId) {
            activateTradeBigdata(uid, custId, 'activateTradeBigdataCb');
            console.log('activateTradeBigdataBvn', uid, custId, 'activateTradeBigdataCb');
          } else {
            const customerId = localStorage.getItem('customerId');
            activateTradeBigdata(uid, customerId, 'activateTradeBigdataCb');
            console.log('activateTradeBigdataNin', uid, customerId, 'activateTradeBigdataCb');
          }
        });
        window.activateTradeBigdataCb = function(res) {
            let result = JSON.parse(vm.$decode(res));
            console.log('activateTradeBigdataCb', result, result.activateState === 'success');
            if (result.activateState === 'success') {
              vm.activateState = true;
            } else {
              vm.activateState = false;
              vm.$toast({
                message: result.errMsg
              })
            }
          }
      } else {
        if (window.dopplerLib && window.dopplerLib.getGpsInfo) { // 新的GPS原生能力。
          window.getGpsInfoCb = function (res) {
            console.log('getGpsInfoCb', res);
            vm.gpsInfo = JSON.parse(vm.$decode(res));
            console.log('gpsInfo', vm.gpsInfo);
          }
          getLocation('getGpsInfoCb'); // 回调
        } else {
          vm.gpsInfo = JSON.parse(getLocation());
        }
        let batchNo = getBatchNo();
        let custId = vm.userInfor.custId;
        console.log(batchNo, custId);
        activateTradeBigdataAC(batchNo, custId);
        window.setActivateTradeResult = (res) => {
            console.log('activateTradeBigdataRes', res, JSON.stringify(res));
            if (res === 'true') {
              console.log('test');
            } else {
              vm.$toast({
                message: res.errMsg
              })
            }
        }
      }
    },
    // 借款前判断
    canLoan(userProductType) {
      let vm = this;
      const chooseTypeData = vm[`${userProductType}ChoiceProduct`].chooseTypeData;
      let amount = vm.getAmount(userProductType);
      if (vm.longActive === -1) {
        vm.$toast({
          message: 'Please select loan tenor'
        });
        return false;
      }
      if (amount < chooseTypeData.minAmount || amount > chooseTypeData.maxAmount) {
        vm.$toast({
          message: `<div style="width: 240px">Withdraw amount is ₦${chooseTypeData.minAmount}-₦${chooseTypeData.maxAmount}</div>`,
          type: 'html'
        });
        return false;
      } else {
        return true;
      }
    },
    // 再借的预借
    loanAgainGenOrder() {
      let vm = this;
      if (vm.canLoan('prefer')) {
        let preferProduct = vm.loanDetail.productList[vm.preferChoiceProduct.productActive];
        let preferProductFee = preferProduct.productFee[0];
        let loanAgainGenList = [];
        let tempPreferProduct = {
          acctType: preferProduct.acctType,
          deviceId: vm.deviceId,
          loanAmt: vm.getAmount('prefer'),
          productFeeId: preferProductFee.productFeeId,
          productId: preferProduct.productId,
          term: preferProduct.term,
          txnScene: preferProduct.txnScene,
          wifi: vm.wifiList,
          priorityLevel: 1, // 1为首选产品
          enableInsurance: preferProductFee.insuranceRate && preferProductFee.insuranceRate.feeRate > 0 ? vm.preferChoiceProduct.loanApplyObj.checkShow : '', // 是否勾选保险费
          channelIsPCX: false // 是否是来自pcx的复借。
        }
        if (vm.loanType === 'BOOK_LOAN') { // 仅在这种情况需要以下两种参数
          tempPreferProduct.onwayLoanId = vm.repaySchduleList[0].loanId; //当前在途借据loanId
          tempPreferProduct.loanSpan = vm.loanSpan; //还款周期跨数
        }
        // 用户首选产品
        loanAgainGenList.push(tempPreferProduct);
        // 用户若同时勾选备选产品, 只有还款复借（AGAIN_LOAN）的类型需要传副产品
        if (vm.alternativeChoiceProduct.isChoosen && vm.loanType === 'AGAIN_LOAN') {
          if (vm.alternativeChoiceProduct.productActive === vm.preferChoiceProduct.productActive && vm.alternativeChoiceProduct.isChoosen) {
            vm.$toast({
              message: 'The Alternative-loan can not be the same with the New loan.'
            });
            return
          }
          if (vm.canLoan('alternative')) {
            let alternativeProduct = vm.loanDetail.productList[vm.alternativeChoiceProduct.productActive];
            let alternativeProductFee = alternativeProduct.productFee[0];
            loanAgainGenList.push({
              acctType: alternativeProduct.acctType,
              deviceId: vm.deviceId,
              loanAmt: vm.getAmount('alternative'),
              productFeeId: alternativeProductFee.productFeeId,
              productId: alternativeProduct.productId,
              term: alternativeProduct.term,
              txnScene: alternativeProduct.txnScene,
              wifi: vm.wifiList,
              priorityLevel: 0, // 0为首选产品
              enableInsurance: alternativeProductFee.insuranceRate && alternativeProductFee.insuranceRate.feeRate > 0 ? vm.alternativeChoiceProduct.loanApplyObj.checkShow : '', // 是否勾选保险费
              channelIsPCX: false // 是否是来自pcx的复借。
            });
          } else {
            return;
          }
        }
        vm.$loading();
        vm.$http(api.repayment.loanAgainGenMultiOrder, {
            addHeader: vm.deviceType === 'AC' ? vm.addHeader : {},
            data: {
              loanType: vm.loanType, // 借款类型：BOOK_LOAN表示预约借款，AGAIN_LOAN表示还款再借
              loanAgainGenList: loanAgainGenList
            }
        }).then(res => {
          vm.$hideLoading();
          vm.generatePlutusWebOrder4nc('repay-re-loan');
          console.log(res);
        }).catch(e => {
          console.log(e);
        });
      }
    },
    // 取消订单
    cancelLoanPreStatus() {
      let vm = this;
      vm.$http(api.repayment.cancelLoanPreStatus, {
        data: {
          loanType: this.loanType
        }
      }).then(res => {
        console.log(res);
      }).catch(e => {
        console.log(e);
      });
    },
    // 还款复借
    repayAndReLoan() {
      let vm = this;
      if (vm.loanType === 'AGAIN_LOAN') {
        vm.repaymentReport('repay_and_reloan_button_click');
      } else if (vm.loanType === 'BOOK_LOAN') {
        vm.repaymentReport('repay_book_button_click');
      }
      if (vm.getRepayAmount <= 0) {
        return;
      }
      vm.loanAgainGenOrder();
    },
    judgeOnlyRepay() {
      // 取主副产品两者中最大的临时增量额度金额作为弹窗的展示金额
      const preTempAddLimit = this.preferChoiceProduct.tempAddLimit || 0;
      const altTempAddLimit = this.alternativeChoiceProduct.tempAddLimit || 0;
      if (preTempAddLimit > altTempAddLimit) {
        this.maxTempAddLimit = preTempAddLimit;
      } else {
        this.maxTempAddLimit = altTempAddLimit;
      }
      console.log('judgeOnlyRepay', this.maxTempAddLimit)
      // 存在临时增量额度>0时，点击才弹出弹窗
      if (this.maxTempAddLimit > 0) {
        this.startOnlyRepay();
      } else { // 不存在复借的时候的点击。
        this.repayOnly();
      }
    },
    startOnlyRepay() {
      this.showOnlyRepayPopup = true;
      this.repaymentReport('reloan_popup_view');
    },
    repayOnly() {
      let vm = this;
      vm.repaymentReport('repayonly_button_click');
      if (vm.getRepayAmount <= 0) {
        return;
      }
      vm.generatePlutusWebOrder4nc('repay-only');
    },
    // 还款（调用H5收银台）
    generatePlutusWebOrder4nc(type) {
      let vm = this;
      localStorage.setItem('repaymentLoanId', vm.repaySchduleList[0].loanId);
      // 还款不能低于200。
      if (vm.getSubmitData(type)) {
        if (vm.submitData.data.repayAmount < vm.minRepayAmount) {
          vm.$hideLoading();
          vm.$toast({
            message: `Single repayment amount shall not be less than ₦${vm.minRepayAmount}.`
          });
          return;
        }
        vm.$http(api.repayment.generatePlutusWebOrder4nc, vm.submitData).then(res => {
          // 跳转收银台
          vm.$hideLoading();
          vm.repaymentReport('generate_plutus_web_order4nc_success');
          localStorage.setItem('productType', 'NC');
          console.log(res);
          location.href = res.url;
        }).catch(() => {
          vm.repaymentReport('generate_plutus_web_order4nc_failed');
        });
      } else {
        vm.$toast({
          message: 'Please select the repayment schedule'
        });
      }
    },
    getSubmitData(type) {
      let vm = this;
      let term = 0;
      let couponId = '';
      let unpayLength = 0;
      let loans = [];
      let repayAmount = vm.getRepayAmount;
      // 初始进入的优惠券，需要获取对应的useScene
      if (vm.repayCoupon.couponId && !vm.repayCoupon.useScene) {
        vm.couponList.forEach(item => {
          if (vm.repayCoupon.couponId === item.couponId) {
            let coupon = {
              ...vm.repayCoupon,
              useScene: item.useScene,
            }
            vm.$store.commit('SET_REPAYCOUPON', coupon);
            console.log('vm.repayCoupon', vm.repayCoupon);
          }
        })
      }
      if (type === 'repay-only') {
        vm.cancelLoanPreStatus();
        vm.$loading();
        if (vm.repayCoupon.useScene === '2' || vm.repayCoupon.useScene === '3') { // 还款时，只能用还款卷
          couponId = vm.repayCoupon.couponId;
        }
      } else {
        couponId = vm.repayCoupon.couponId || '';
      }
      vm.repaySchduleList.forEach((item) => {
        if (item.termStatus !== 'F') {
          unpayLength++;
          loans = [{
            loanId: item.loanId,
            term: item.term
          }]
        }
        if (item.checked && item.termStatus !== 'F') {
          term++;
        }
      });
      // 只有最后一期的时候，需要提供loans用于优惠券的计算。非最后一期不需要
      if (unpayLength != 1) {
        loans = [];
      }
      if (term) {
        // let partialRepay = false;
        // 在逾期和账单日：
        if (vm.canChangeRepayAmt) {
          if (vm.repaymentDetail.totalAmtBackUp != vm.repaymentDetail.totalAmt) { // 数额有变更。
            // partialRepay = true;
          }
        }
        // 存在优惠券时，都要减去金额再传金额给后端。
        if (couponId) {
          repayAmount = vm.common.floatMinus(repayAmount, (vm.discountAmt ? vm.discountAmt : 0));
        }
        vm.submitData = {
          data: {
              isRepayToLoanFlag: type === 'repay-re-loan' ? true : false, // 还款复借标识。
              coolingOffINTFreeFlag: false, // 写死，固定
              couponId: couponId,
              loans: loans,
              partialRepay: true, // 部分还款
              repayAmount: repayAmount
          }
        };
        return true;
      } else {
        return false;
      }
    },
    // 选择优惠券
    chooseCoupons() {
      let vm = this;
      vm.repaymentReport('repayment_page_coupon_button_click');
      if (vm.productType === 'single') {
        vm.$router.push({ path: "/couponsList", query: {
          // mLoanSpan: vm.repaymentDetail.loanList[0].loanDay,
          // mLoanAmt: vm.getRepayAmount,
          productType: vm.productType,
          term: 1, // 目前写死为1
          from: 'repaymentIndex'
        }});
      } else {
        vm.$router.push({ path: "/couponsList", query: {
          productType: vm.productType,
          couponList: vm.couponList,
          from: 'repaymentIndex'
        }});
      }
    },
    // bulr后再执行一次计算。
    startLoanCalculate(obj) {
      let vm = this;
      vm.chooseType(vm[`${obj.type}ChoiceProduct`].productActive, 0, 1, obj.type);
    },
    // 访问联系我们
    goContactUs() {
      this.$router.push({path: '/loadUrlPage', query: {
        url: process.env.VUE_APP_CONTACT_US_URL,
        title: 'Contact Us'
      }});
    },
    goFeedBack() {
      this.showFeedback = true;
      this.repaymentReport('feedback_click');
    },
    closeFeedback() {
      this.showFeedback = false;
    },
    // 获取nc用户借据
    queryNcRepayPlanDetailList() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.repayment.queryRepayPlanDetailList, {
          data: {}
        }).then(res => {
          console.log('nc用户借据，查询所有待还期数', res)
          resolve(res);
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          reject(e);
        });
      });
    },
    checkInsurance(obj) {
      let vm = this;
      const choiceProduct = vm[`${obj.type}ChoiceProduct`];
      choiceProduct.loanApplyObj.checkShow = !choiceProduct.loanApplyObj.checkShow;
      vm.startLoanCalculate(obj);
    },
    getNcRepayment() {
      const vm = this;
      setTimeout(() => {
        vm.$loading();
      }, 300);
      vm.queryNcRepayPlanDetailList().then(res => {
        vm.userRepaymentObj.ncStatus = true;
        if (res.length > 0) {
          vm.userRepaymentObj.nc = vm.dealNcPlanList(res);
        }
        vm.finalDeal();
        setTimeout(() => {
          vm.$hideLoading();
        }, 500);
      }).catch(() => {
        setTimeout(() => {
          vm.$hideLoading();
        }, 2000);
        vm.userRepaymentObj.ncStatus = true;
        vm.finalDeal();
      });
    },
    getPopAPPConfig() {
      return  new Promise((resolve, reject) => {
        this.$http(api.repayment.popAPPConfig, {
          data: {
            // 不传参数，使用旧逻辑查询，传参数，传什么查什么
            showIncreaseCredit: true,
            dueRemindFlag: true,
            showDueRemind: true,
            showVerdictAnnouncement: true
          }
        }).then(res => {
          console.log('popAPPConfig==配置===res', res)
          this.dueRemindFlag = res.dueRemindFlag;
          this.dueRemindDay = res.dueRemindDay;
          // 开关和提示日期同时存在，则显示
          if (this.dueRemindFlag === 'Y' && this.dueRemindDay) {
            this.showBanner = true;
            this.repaymentReport('Repayment-banner_view');
            // 24小时只显示一次
            if (!inTimeAreaCal('showReminder', 24)) {
              this.showReminder = true;
              localStorage.setItem('showReminder', new Date().getTime());
              this.repaymentReport('Repayment-Popup_view');
            }
          }
          if (res.showVerdictAnnouncement === 'Y') {
            this.showVerdictAnnouncement = true;
          }
          this.showIncreaseCredit = res.showIncreaseCredit;
          if (this.showIncreaseCredit === 'Y') {
            this.repaymentReport('repay_reloan_addinfo_banner_view');
          }
          resolve(res);
        }).catch(e => {
          // 后续这里报错，需要退出，返回到首页。
          reject(e);
        });
      });
    },
    // nc(pc), 各自查询借据后的处理。
    finalDeal() {
      const vm = this;
      if (vm.userRepaymentObj.ncStatus && vm.userRepaymentObj.nc) {
        vm.setNcData();
      }
    },
    closeRepaymentList(checkedObj) {
      const vm = this;
      vm.userRepaymentObj.ncChecked = checkedObj.ncChecked;
      if (vm.userRepaymentObj.ncChecked) {
        vm.setNcData();
      }
    },
    setNcData() {
      const vm = this;
      vm.repaymentDetail.totalAmt = vm.repaymentDetail.totalAmtBackUp = vm.userRepaymentObj.nc.totalAmt;
      vm.repaymentDetail.passAmount = vm.userRepaymentObj.nc.passAmount;
      vm.repaymentDetail.interest = vm.userRepaymentObj.nc.interest;
      vm.nextRepayment.dueDate = vm.userRepaymentObj.nc.nextRepayment.dueDate;
      vm.nextRepayment.amount = vm.userRepaymentObj.nc.nextRepayment.amount;
      vm.canChangeRepayAmt = vm.userRepaymentObj.nc.canChangeRepayAmt;
      vm.$store.commit('SET_REPAYSCHDULELIST', vm.userRepaymentObj.nc.repaySchduleList);
      if (vm.userRepaymentObj.nc.haveReloan || vm.havePreRepayment) {
        if (vm.userRepaymentObj.nc.haveReloan) {
          vm.temHaveReloan = vm.userRepaymentObj.nc.haveReloan;
        }
        vm.startNcHaveReloan();
      }
    },
    // 合并客户还款信息接口
    queryCustRepayInfo() {
      let vm = this;
      return  new Promise((resolve, reject) => {
        vm.$http(api.repayment.queryCustRepayInfo, {
          data:{
            blcQueryIsUsedCouponReq: {
              derateAmtList:[{
                amt: vm.getRepayAmount,
                loanId: vm.repaySchduleList[0].loanId
              }],
              loanList: [vm.repaySchduleList[0].loanId],
              // custId: vm.userInfor.custId || vm.userInfo.custId,
              useScene: '2,4',
              status: 'UNUSED'
            },
            blcQueryCouponListReq: {
              status: 'UNUSED',
              useScene: '2,4',
              isNext: false,
              pageNum: 0,
              pageSize: 100,
              startIndex: 0,
              totalPage: 0,
              totalRecord: 0
            },
            queryProductWithSerV5Req: {
              isReloanApply:true
            }
          },
          showErrorToast: false
        }).then(res => {
          console.log('还款合并接口-queryCustRepayInfo===res', res)
          // 复借额度处理
          vm.loanDetail.usableLmtAmt = res.loanAgainAmtResp.loanAmt;
          vm.loanDetail.amount = vm.thousands(res.loanAgainAmtResp.loanAmt);
          vm.alternativeChoiceProduct.amount = vm.loanDetail.amount;
          vm.preferChoiceProduct.amount = vm.loanDetail.amount;
          // 优惠券处理
          vm.couponList = res.queryCouponListResp.couponList;
          vm.isUsedCoupon = res.queryLoanIsUsedCouponResp;
          // 针对产品处理
          vm.dealProductInfo(res.querySimpleProductResp);
          if (vm.havePreRepayment && res.bookLoanFactor && res.bookLoanFactor.bookSwitchIsOpen) {
            vm.bookSwitchIsOpen = true;
            vm.queryLoanApplyAuthority().then(res => {
              console.log('queryLoanApplyAuthority', res);
              // 循环贷的资格 res.isCanloanApply && res.isWhite 都为true，而且res.custLmt >=2000.
              // 这里只使用!(res.isCanloanApply && res.isWhite) 判断即可
              vm.isRevolvingLoan = res.isCanloanApply && res.isWhite;
              // 预约借款条件
              // 1.针对指定周期: 目前是7天(loanSpan === 4)
              // 2.多期产品客户（无借循环贷资格的） (isRevolvingLoan === false)
              // 3.当前未还账单为倒数第二期账单且该笔借据为N状态的(havePreRepayment === true)
              // 4.预约借款开关打开（bookSwitchIsOpen === true）
              if (!vm.isRevolvingLoan && vm.loanSpan === 7) {
                vm.haveReloan = true;
                vm.loanType = 'BOOK_LOAN';
              }
            })
          }
          resolve(res);
        }).catch(e => {
          // 无产品需要提示弹窗。
          if (e.code !== 1104016) {
            vm.$toast(e.msg);
          }
          reject(e);
        });
      });
    },
    // 处理产品信息
    dealProductInfo(querySimpleProductResp) {
      const vm = this;
      let allProductList = querySimpleProductResp && querySimpleProductResp.productList;
      if (allProductList && allProductList.length > 0) {
        allProductList.forEach((item) => {
          let productFee = item.productFee[0];
          item.days = productFee.totalBorrowCount;
          item.type = `${item.days} days`;
          item.term = productFee.term;
          item.rate = productFee.dayRate * 100;
          if (item.productFee[0].term > 1) {
            item.tips = `Every ${ item.borrowCount } days in ${item.productFee[0].term} installment`;
          }
          vm.repaymentReport(`${item.type}_repayment_tenor_button_view`);
          return item;
        });
        // 分离单期和多期产品，分开展示以及排序
        let pcSingleProductList = [];
        let pcMuiltiProductList = allProductList.filter(item => {
          if (item.productFee[0].term === 1) {
              pcSingleProductList.push(item);
          }
          return item.productFee[0].term > 1;
        });
        // 单期排序
        pcSingleProductList.sort((a, b) => {
          let res = a.days - b.days;
          if (res > 0) {
            return -1
          } else if (res < 0) {
            return 1;
          } else {
            if (a.rate > b.rate) {
              return 1
            } else if (a.rate < b.rate) {
              return -1;
            } else {
              return 0;
            }
          }
        });
        // 多期排序
        pcMuiltiProductList.sort((a, b) => {
          let res = a.days - b.days;
          if (res > 0) {
            return -1
          } else if (res < 0) {
            return 1;
          } else {
            if (a.rate > b.rate) {
              return 1
            } else if (a.rate < b.rate) {
              return -1;
            } else {
              return 0;
            }
          }
        });
        allProductList = [...pcMuiltiProductList, ...pcSingleProductList];
        // 若用户仅支持可见单期时，下方增加固定展示7*4和14*4多期产品
        if (pcMuiltiProductList.length === 0 && pcSingleProductList.length > 0) {
          const lockMuiltiProductList = [{
            tips: 'Every 7 days in 4 installment',
            type: '28 days',
            islock: true,
            productId: '999999999'
          }, {
            tips: 'Every 14 days in 4 installment',
            type: '56 days',
            islock: true,
            productId: '999999998'
          }];
          allProductList = [...allProductList, ...lockMuiltiProductList];
        }
        vm.loanDetail.productList = allProductList;
        vm.preferChoiceProduct.productActive = 0;
        // 只有多期和单期同时存在时，才显示备选产品选项
        if (pcMuiltiProductList.length > 0 && pcSingleProductList.length > 0) {
          vm.alternativeChoiceProduct.productActive = pcMuiltiProductList.length;
          vm.alternativeChoiceProduct.canShow = true;
          vm.alternativeChoiceProduct.isChoosen = true;
        } else {
          // 只有主产品，默认展开详情
          vm.preferChoiceProduct.loanApplyObj.showDetail = true;
        }
    }
    },
    // 勾选期数回来的计算
    startRepaymentCalc() {
      const vm = this;
      let totalAmt = 0;
      let passAmount = 0;
      let interest = 0;
      vm.repaySchduleList.forEach(item => {
        if (item.checked) {
          totalAmt = vm.common.floatAdd(totalAmt, item.repaymentAmount);
          passAmount = vm.common.floatAdd(item.passAmount, passAmount);
          interest = vm.common.floatAdd(item.interest, interest);
        }
      });
      vm.repaymentDetail.totalAmt = vm.common.thousandsToFixed(totalAmt);
      vm.repaymentDetail.totalAmtBackUp = vm.common.thousandsToFixed(totalAmt);
      vm.repaymentDetail.passAmount = vm.common.thousandsToFixed(passAmount);
      vm.repaymentDetail.interest = interest; // 不需要格式化，只用来计算优惠券减免的金额
    },
    // 修改选择
    changeAlternativeChoice() {
      const vm = this;
      vm.alternativeChoiceProduct.isChoosen = !vm.alternativeChoiceProduct.isChoosen;
      if (vm.alternativeChoiceProduct.productActive === vm.preferChoiceProduct.productActive && vm.alternativeChoiceProduct.isChoosen) {
        vm.$toast({
          message: 'The Alternative-loan can not be the same with the New loan.'
        });
      }
      vm.repaymentReport('alternative_loan_choose_click');
    },
    beforeRouteEnter (to, from, flag) {
      const vm = this;
      if (from.name === 'showVerdictAnnouncement') return
      if (from.name === 'couponsList' && vm.isLowerAmount) {
        vm.$newToast(`The minimum repayment amount after using the coupon should not be less than ₦${vm.minRepayAmount}`);
        vm.$store.commit('SET_REPAYCOUPON', {});
      }
      vm.fromName = from.name;
      if (vm.fromName === 'couponsList' && vm.discountAmt === 0 && vm.repayCoupon.couponId) {
        vm.$store.commit('SET_REPAYCOUPON', {});
        setTimeout(() => {
          this.$newToast(`Your interest has been repaid so the coupon cannot be used.`);
        }, 1500);
      }
      if (vm.$route.query.from === 'loanHistory' && vm.productSource !== 'palmcredit') {
        vm.$router.push({ path: '/loanDetail', query: {
          loanId: vm.$route.query.loanId
        }});
      } else if (from.name === 'termDetail') {
        vm.startRepaymentCalc();
      } else if (from.name !== 'couponsList' && from.name !== 'uploadDocuments' || flag === 'refresh') {
        vm.userRepaymentObj.ncStatus = true;
        vm.getNcRepayment();
        vm.getPopAPPConfig();
      }
    },
    // 查询借款资格，如循环贷资格等
    queryLoanApplyAuthority() {
      return  new Promise((resolve, reject) => {
        this.$http(api.commonBlc.queryLoanApplyAuthority, {
          data:{
          },
          showErrorToast: false
        }).then(res => {
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    goToBanlance() {
      localStorage.setItem('transferNow', '1');
      this.transferNow = '1';
      gotoBalanceTab()
    },
    goToVerdictAnnouncement() {
      this.$router.push({
        path: '/showVerdictAnnouncement'
      });
    },
    hideAddPersonalInformation(type){
      if (type === 'confirm') {
        this.increaseCredit();
        this.repaymentReport('repay_reloan_addinfo_popup_submit_click');
      }
    },
    // 提交增信配置
    increaseCredit() {
      this.$loading()
      return  new Promise((resolve, reject) => {
        const uploadDocuments = this.uploadDocuments;
        this.$http(api.commonBlc.increaseCredit, {
          data: {
            salaryProcessUrl: uploadDocuments.salaryFlow[0] && uploadDocuments.salaryFlow[0].id, //工资发放流水照片url
            salaryPayslipUrl: uploadDocuments.salaryPayslip[0] && uploadDocuments.salaryPayslip[0].id, //工资单照片url
            employmentCertificateUrl: uploadDocuments.photoOfWorkBadge[0] && uploadDocuments.photoOfWorkBadge[0].id, //工作证明照片url
            bankAccountBalanceUrl: uploadDocuments.bankAccountBalance[0] && uploadDocuments.bankAccountBalance[0].id, //银行账户照片url
          }
        }).then(res => {
          // 重置初始状态
          this.showAddPersonalInformation = false;
          this.showIncreaseCredit = 'N';
          // 提交增信后，重新刷新页面
          this.$hideLoading();
          this.beforeRouteEnter(this.$parent.to, this.$parent.from, 'refresh');
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      });
    },
    showAddPersonalInformationBanner() {
      this.showAddPersonalInformation = true;
      this.repaymentReport('repay_reloan_addinfo_banner_click');
    }
  },
  mounted() {
    let vm = this;
    let ncInfofromKc = localStorage.getItem('ncInfofromKc');
    if (ncInfofromKc || vm.productSource === 'truemoneyios' || vm.productSource === 'creditwiseios') {
      vm.reloanControl = false;
    }
    this.androidVersion = getOsVersionCode();
    console.log('当前安卓版本(大于28支持选择文件)', this.androidVersion);
    vm.beforeRouteEnter(vm.$parent.to, vm.$parent.from);
    vm.transferNow = localStorage.getItem('transferNow');
    getCurrentAppVersionName().then(currentAppVersionName => {
      vm.currentAppVersionName = currentAppVersionName;
    });
    getCurrentAppVersion().then(appVersion => {
      vm.appVersion = appVersion;
    });
    vm.repaymentReport('repayment_page_view');
    vm.repaymentReport('feedback_activity');
  },
  created() {
    let vm = this;
    vm.goActivateTradeBigdata();
  }
};
