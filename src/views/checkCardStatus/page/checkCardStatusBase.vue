<template>
  <div class="check-card-status">
    <div class="tip top" v-show="count <= 5">The binding card is abnormal, please bind the card again to get cash</div>
    <div class="status">{{ count }}<em>s</em></div>
    <div class="tip">Your request has been submitted and the card addition proccessing will take a while.</div>
    <c-button name="Try again" @buttonClick="goBack"></c-button>
    <addAccount :cardType="cardType" @bindCard="updateBindCard" v-if="showBanner" />
  </div>
</template>

<script>
import publicMixns from '../checkCardStatusMixins.js'
export default {
    name: 'checkCardStatusBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
  .check-card-status{
    padding-top: 50px;
    .status{
      width: 90px;
      height: 90px;
      line-height: 90px;
      border: 4px solid $themeColor;
      color: $themeColor;
      border-radius: 50%;
      margin: 50px auto 0;
      font-size: 38px;
      em {
        font-size: 18px;
        color: #cccccc;
      }
    }
    .tip{
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      font-size: 12px;
      color: #1B3155;
      margin: 27px 37px 50px;
      &.top {
        font-weight: bold;
        font-size: 16px;
      }
    }
    .c-button {
      height: 42px;
      background: $themeColor;
      border-radius: 8px;
      font-size: 18px;
      width: 303px;
    }
    .add-account{
      margin-top: 20px;
    }
  }
</style>
