<template>
  <div class="check-card-status">
    <div class="tip top" v-show="count <= 5">The binding card is abnormal, please bind the card again to get cash</div>
    <div class="status">{{ count }}<em>s</em></div>
    <div class="tip">Your request has been submitted and the card addition proccessing will take a while.</div>
    <c-button name="Try again" @buttonClick="goBack"></c-button>
    <addAccount :cardType="cardType" @bindCard="updateBindCard" v-if="showBanner" />
  </div>
</template>

<script>
import publicMixns from '../checkCardStatusMixins.js'
export default {
    name: 'checkCardStatusWayacredit',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
  .check-card-status{
    width: 100%;
    height: 100%;
    background: #1E1E20;
    padding-top: 50px;
    .status{
      width: 90px;
      height: 90px;
      line-height: 90px;
      border: 4px solid $themeColor;
      color: $themeColor;
      border-radius: 50%;
      margin: 50px auto 0;
      font-size: 38px;
      em {
        font-size: 18px;
        color: #cccccc;
      }
    }
    .tip{
      font-family: Avenir, Avenir-Medium;
      font-weight: 500;
      text-align: left;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.60);
      margin: 27px 37px 50px;
      &.top {
        font-weight: bold;
        font-size: 16px;
        color: #fff;
      }
    }
    .c-button {
      width: calc(100% - 44px);
      height: 42px;
      border-radius: 10px;
      background: linear-gradient(180deg, #00FFAD 0%, #188436 100%);
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25), 0px 4px 4px 0px rgba(255, 255, 255, 0.50) inset;
      font-size: 18px;
      color: #282A30;
      text-align: center;
      font-family: Noto Sans;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
    }
    .add-account{
      margin-top: 20px;
    }
  }
</style>
