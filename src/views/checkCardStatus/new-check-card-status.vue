
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'checkCardStatus',
  components: {
    baseCheckCardStatus: () => import(/* webpackChunkName: "baseCheckCdst" */ './page/checkCardStatusBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "checkCardStatusWayacredit" */ './page/checkCardStatusWayacredit.vue'),
    checkCardStatusCommon1: () => import(/* webpackChunkName: "checkCardStatusCommon1" */ './page/checkCardStatusCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseCheckCardStatus',
        to: '',
        from: ''
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.checkCardStatus) {
      this.componentTag = globalConfig.pageStyleSetting.checkCardStatus
    }

  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
