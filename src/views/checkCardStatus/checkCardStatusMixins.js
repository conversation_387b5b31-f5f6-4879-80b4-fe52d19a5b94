import { gotoHomeActivity } from "@/assets/js/native";
import {mapState} from "vuex";
import api from "@/api/interface";
import addAccount from '@/components/add-account.vue';
export default {
  name: 'checkCardStatus',
  components: {
    addAccount
  },
  computed: {
    ...mapState([ 'productSource']),
  },
  data() {
    return {
      timer: null,
      bindStatus: '', // SUCCESS,FAILED,PENDING
      count: '',
      bindBankCardStatus: 'mark',
      cardType: '', // 绑卡类型： account为账户 其他情况没有注明则为绑卡
      showBanner: false, // 是否显示绑卡/绑账户banner
      skip: false, // 是否是绑卡跳过
    };
  },
  created() {
    this.report('bindcard_pending_activity', 'view');
  },
  mounted() {
    this.queryPlutusBindBankCardStatus();
    window.scrollTo(0, 0);
    this.setTime(20)
  },
  beforeDestroy() {
    clearTimeout(this.timer);
    clearTimeout(this.timer2);
    this.report('bindcard_pending_activity', 'leave');
  },
  methods: {
    goStatus(status, message) {
      this.$router.push({ path: "/cardStatus", query: {
        status: status,
        message: message,
        queryObj: JSON.stringify({
          showBanner: this.showBanner,
          cardType: this.cardType,
          skip: this.skip
        })
      }});
    },
    // 埋点
    report(eventName, eventType) {
      let self = this;
      self.$store.dispatch('reportEvent', {
        page: 'checkCardStatus',
        eventName: eventName,
        eventData: {
          event_time: Date.now(),
          event_type: eventType
        }
      });
    },
    // 查询绑卡状态
    queryPlutusBindBankCardStatus() {
      let self = this;
      // 从提现成功页发起的绑卡会存在statusDetail
      const statusDetail = localStorage.getItem('statusDetail');
      return new Promise((resolve, reject) => {
        self.$http(api.bankAccount.queryPlutusBindBankCardStatus, {
            data:{
              "reference": self.getQueryVariable()
            }
        }).then(res => {
          self.report('query_plutus_bind_bank_card_status_' + res.bindBankCardStatus);
          localStorage.removeItem('statusDetail');
          this.bindBankCardStatus = res.bindBankCardStatus;
          this.cardType = res.cardType;
          // 这里的skip是终态，不是每次绑卡的skip.
          this.skip = res.skip;
          // 显示banner的要求：
          // 不是已绑卡且已绑代扣账户以及开关打开的情况
          this.showBanner = !(res.isAccountFinish === 'Y' && res.isCardFinish === 'Y') && res.isShowBindCardBanner === 'Y';
          if (this.cardType === 'account') {
            this.report('bindaccount_pending_page_status_view');
            this.report('bindaccount_pending_tryagain_view');
            if (this.showBanner) {
              this.report('bindaccount_pending_banner_view');
            }
          } else {
            this.report('bindcard_pending_page_status_view');
            this.report('bindcard_pending_tryagain_view');
            if (this.showBanner) {
              this.report('bindcard_pending_banner_view');
            }
          }
          // 有绑卡成功、绑代扣账户成功、绑卡跳过，都可以提现。
          const bindCardType = localStorage.getItem('bindCardType');
          const canJumpWithdraw = res.isAccountFinish === 'Y' || res.isCardFinish === 'Y' || res.skip;
          console.log('canJumpWithdraw', canJumpWithdraw);
          // 通过banner绑卡/绑账户回来，若可以提现，则直接走提现逻辑，不关心绑定的状态
          if ((bindCardType === 'widthdrawAddBankCard,secondAddBankCard' || bindCardType === 'widthdrawAddBankCard,secondAddBankAccount') && canJumpWithdraw) {
            self.$router.push({ path: '/', query: {
              cardStatus: 'success'
            }}); // 跳转回首页
            return;
          }
          console.log('this.showBanner', this.showBanner);
          if (res.bindBankCardStatus === 'SUCCESS') {
            clearInterval(self.timer);
            self.timer = null;
            self.goStatus('SUCCESS');
          } else if (res.bindBankCardStatus === 'FAILED') {
            clearInterval(self.timer);
            self.timer = null;
            // 从提现成功页发起的绑卡失败才跳转回提现结果页
            self.goStatus('FAILED', res.message);
          } else if (res.bindBankCardStatus === 'PENDING') {
            if (statusDetail) {
              clearInterval(self.timer);
              gotoHomeActivity('checkCardStatus');
            } else {
              if (!self.timer) {
                self.timer = setInterval(function() {
                  self.queryPlutusBindBankCardStatus();
                }, 10000);
              }
            }
          }
          console.log(res);
          resolve();
        }).catch(e => {
          console.log(e);
          clearInterval(self.timer);
          self.timer = null;
          self.report('query_plutus_bind_bank_card_status_failed');
          reject();
        });
      });
    },
    getQueryVariable() {
      return this.$route.query.reference;
    },
    goBack() {
      this.report('try_again_click', 'click');
      if (this.cardType === 'account') {
        this.report('bindaccount_pending_tryagain_click');
      } else {
        this.report('bindcard_pending_tryagain_click');
      }
      this.routeRedirect()
    },
    routeRedirect () {
      let obj = {};
      if (this.productSource === 'palmcredit') {
        obj.businessChannel = 'palmcreditnew';
      }
      return  new Promise((resolve, reject) => {
        this.$http(api.commonBlc.routeRedirect, {
          "data":{
            "scene":"2",
            ...obj
          } // 目前是写死的
        }).then(res => {
          // 跳转绑卡中台的地址
          location.href = res.redirectUrl;
          resolve();
        }).catch(e => {
          reject(e);
        })
      })
    },
    setTime(val) {
      this.count = val
      this.timer2 = setInterval(() => {
        if (this.count > 0 && this.count <= val) {
          this.count--;
        } else {
          clearInterval(this.timer2);
          if (this.bindBankCardStatus) {
            this.routeRedirect()
          }
        }
      }, 1000);
    },
    updateBindCard(cardType) {
      if (this.cardType === 'account') {
        this.report('bindaccount_pending_banner_click');
      } else {
        this.report('bindcard_pending_banner_click');
      }
      let bindCardType = localStorage.getItem('bindCardType');
      // 这里cardType是banner的cardType。也就是是实际banner点击的类型值
      const newBindCardType = cardType === 'account' ? 'secondAddBankAccount' : 'secondAddBankCard';
      // 缓存第二次绑卡/绑账户跳转，用于回调判断
      if (bindCardType.includes('widthdrawStatusAddBankCard')) {
        bindCardType = `widthdrawStatusAddBankCard,${newBindCardType}`;
      }
      if (bindCardType.includes('widthdrawAddBankCard')) {
        bindCardType = `widthdrawAddBankCard,${newBindCardType}`;
      }
      localStorage.setItem('bindCardType', bindCardType);
    }
  }
};