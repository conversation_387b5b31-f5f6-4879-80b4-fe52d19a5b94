<template>
  <div v-if="value" class="popup">
    <div class="popup-content">
      <div class="title">Note</div>
      <div class="tips">You have finished uploading,do you need to submit?</div>
      <div class="action">
        <div class="no" @click="hidePopup('close')">No</div>
        <div class="yes" @click="hidePopup('confirm')">Yes</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
   props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  components: {
  },
  methods: {
    hidePopup(type) {
      this.$emit('hidePopup', type);
    }
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  .popup-content {
    width: 304px;
    height: 175px;
    background: #fff;
    border-radius: 24px;
    box-shadow: 0px 2px 20px 0px rgba(0, 54, 101, 0.06);
    .title{
      color: #000;
      font-family: Avenir;
      font-size: 16px;
      font-style: normal;
      font-weight: 800;
      line-height: 20px;
      text-align: left;
      margin: 20px 27px 10px;
    }
    .tips{
      color: #536887;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      margin: 0 27px 22px;
    }
    .action{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 16px;
      > div{
        width: 129px;
        height: 42px;
        border-radius: 16px;
        border: 1px solid $themeColor;
        color: $themeColor;
        text-align: center;
        font-family: Avenir;
        font-size: 18px;
        font-style: normal;
        font-weight: 800;
        line-height: 42px;
        &.yes{
          color: #FFF;
          background: $themeColor;
          font-size: 18px;
        }
      }
    }
  }

  .close-button {
    margin-top: 10px;
  }
}
</style>
