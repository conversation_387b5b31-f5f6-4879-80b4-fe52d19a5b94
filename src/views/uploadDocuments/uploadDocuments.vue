<template>
  <div class="upload-documents">
    <c-header :titleName="documentTypeObj.title" :backFun="myBackFun"></c-header>
    <div class="top">
      <div class="tips">We invite you to upload <span class="show-type" v-html="documentTypeObj.desShow"></span> information in order to provide you with a higher loan limit and longer loan term.</div>
      <ul class="sub-tips" v-if="documentTypeObj.subTipsList && documentTypeObj.subTipsList.length > 0">
        <li v-for="subTip in documentTypeObj.subTipsList" :key="subTip" v-text="subTip"></li>
      </ul>
      <img v-if="documentTypeObj.subTipsList && documentTypeObj.subTipsList.length > 0" src="@/assets/images/common/limit-icon.png" alt="">
    </div>
    <div class="upload" v-show="androidVersion > 28">
      <div class="title">Upload your <span v-html="documentTypeObj.desShow"></span></div>
      <div class="upload-p">Support JPG, PNG, PDF format, photo or file size of no more than 10M</div>
      <!-- 初始选择 -->
      <div class="upload-item" v-if="haveSimFiles.length === 0" @click="chooseFiles(-1, 'haveSimFiles')">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="40" cy="40" r="40" fill="#02B17B"/>
          <path d="M24.7051 51.7257H55.2933V55.2943H24.7051V51.7257ZM48.7729 32.856L41.7432 25.4425C41.3071 24.9961 40.6913 24.7061 39.9992 24.7061C39.3071 24.7061 38.6915 24.9961 38.2625 25.4471L31.2181 32.8507C30.8505 33.2499 30.6163 33.7655 30.6163 34.3373C30.6163 35.5646 31.667 36.5598 32.9621 36.5598C32.9802 36.5598 32.9978 36.5575 33.0156 36.557V36.571H36.1757V48.7874H43.8227V36.571H46.8971V36.5531C46.9435 36.5557 46.9891 36.5603 47.0365 36.5603C48.3319 36.5603 49.3824 35.5649 49.3824 34.3373C49.3824 33.7655 49.1479 33.2501 48.7729 32.856Z" fill="white"/>
        </svg>
      </div>
      <div class="upload-item" v-for="(file, index) in haveSimFiles" :key="index"  :style="{ 'background-image': `url(${file.fileType === 'pdf' ? uploadedPdf : file.img})`}" @click="goPre(haveSimFiles, index)">
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg" @click.stop="chooseFiles(index, 'haveSimFiles')">
          <circle cx="40" cy="40" r="40" fill="#02B17B"/>
          <path d="M24 26.6763C24.0004 26.4126 24.0795 26.1549 24.2272 25.9362C24.3748 25.7175 24.5843 25.5477 24.829 25.4484C25.0736 25.3491 25.3424 25.3249 25.6008 25.3788C25.8593 25.4327 26.0959 25.5623 26.2804 25.7511L28.9907 28.4253C31.9336 25.6515 35.9335 24 40 24C48.8266 24.0332 56 31.1992 56 40.0166C55.9967 44.2546 54.3099 48.3182 51.3101 51.3149C48.3102 54.3117 44.2425 55.9967 40 56C35.2066 56 30.7103 53.9191 27.6698 50.2199C27.4725 49.9232 27.4725 49.5601 27.7362 49.3278L30.6127 46.4544C30.7527 46.3411 30.9289 46.2822 31.109 46.2884C31.2752 46.3216 31.5057 46.388 31.6054 46.5519C32.5981 47.8413 33.8751 48.8846 35.3372 49.6006C36.7993 50.3167 38.407 50.6862 40.0353 50.6805C42.8663 50.6761 45.58 49.5507 47.5817 47.551C49.5835 45.5513 50.71 42.8404 50.7144 40.0125C50.7144 34.1348 45.8879 29.3444 40.0041 29.3444C37.2607 29.3444 34.6812 30.4004 32.731 32.2178L35.6075 35.0913C35.7941 35.2706 35.9222 35.5019 35.9752 35.7551C36.0282 36.0082 36.0036 36.2715 35.9045 36.5104C35.8066 36.7571 35.6387 36.9699 35.4214 37.1225C35.204 37.2751 34.9468 37.3609 34.6813 37.3693H25.3583C25.0096 37.3624 24.6771 37.2209 24.4305 36.9746C24.1839 36.7282 24.0423 36.3961 24.0354 36.0477L24.0001 26.6763H24Z" fill="white"/>
        </svg>
      </div>
    </div>
    <div class="upload" v-if="documentTypeObj.simpleList && documentTypeObj.simpleList.length > 0">
      <div class="title">Sample</div>
      <ul class="sample-list" :class="[documentType]">
        <li class="sample" v-for="(sam, index) in documentTypeObj.simpleList" :key="sam.id" @click="goPre(documentTypeObj.simpleList, index)">
          <div class="show-img" :style="{ 'background-image': `url(${sam.img})`}">
            <img v-if="sam.recommended" src="@/assets/images/common/recommended.png" alt="">
          </div>
          <div class="content" v-text="sam.content"></div>
        </li>
      </ul>
    </div>
    <c-button name="Submit" :class="{'submit': canSubmit}" @buttonClick="submitForm"></c-button>
    <backPopup v-model="showbackPopup" @hidePopup="hidePopup"></backPopup>
  </div>
</template>

<script>
// import MTN1 from '@/assets/images/common/MTN-1.jpg';
import shorturlPdf from '@/assets/images/increaseLimit/shorturl-pdf.png';
import uploadedPdf from '@/assets/images/increaseLimit/uploaded-pdf.png';
import bankAccountBalance1 from '@/assets/images/increaseLimit/bank-account-balance1.png';
import bankAccountBalance2 from '@/assets/images/increaseLimit/bank-account-balance2.png';
import photoOfWorkBadge1 from '@/assets/images/increaseLimit/photo-of-workbadge1.png';
import salaryPayslip1 from '@/assets/images/increaseLimit/salary-payslip1.png';
import {
  openFeedBackFile,
  getOsVersionCode,
  getCustId
} from "@/assets/js/native";
import { decode } from "js-base64";
import api from "@/api/interface";
import { mapState } from 'vuex';
import { ImagePreview } from 'vant';
// import { Popup } from 'vant';
import {judgeClient} from "@/assets/js/common";
import backPopup from './components/back-popup.vue';
export default {
  name: 'uploadDocuments',
  components: {
    backPopup
    // Popup
  },
  data() {
    return {
      uploadedPdf,
      documentType: '', // 当前上传的类型
      documentsObj: { // 上传的类型对象
        bankAccountBalance: { // 银行账户余额
          type: 'bankAccountBalance',
          title: 'Upload bank account balance',
          desShow: '<span>bank account balance</span>',
          subTipsList: [
            '1. Bank account balance.(recommended)',
            '2. Operator balance.'
          ],
          simpleList: [
            {
              id: 0,
              img: bankAccountBalance1,
              recommended: true,
              content: 'Bank account balance 1'
            },
            {
              id: 1,
              img: bankAccountBalance2,
              recommended: false,
              content: 'Bank account balance 2'
            }
          ],
        },
        salaryFlow: { // 近三个月银行账户工资发放流水
          type: 'salaryFlow',
          title: 'Upload salary flow',
          desShow: '<span>Salary Flow</span> (last three months)',
          subTipsList: [],
          simpleList: [],
        },
        photoOfWorkBadge: { // 工作证明
          type: 'photoOfWorkBadge',
          title: 'Upload photo of work badge',
          desShow: '<span>photo of work badge</span>',
          subTipsList: [],
          simpleList: [{
            id: 0,
            img: photoOfWorkBadge1,
            recommended: true,
            content: 'Photo of work badge'
          }],
        },
        salaryPayslip: { // 工资单
          type: 'salaryPayslip',
          title: 'Upload salary payslip',
          desShow: '<span>salary payslip</span>',
          subTipsList: [],
          simpleList: [{
            id: 0,
            img: salaryPayslip1,
            recommended: true,
            content: 'Salary Payslip'
          }],
        }
      },
      androidVersion: 0,
      acceptType: 'application/pdf|image/*',
      haveUploadFiles: [], // 已上传的附件
      uploadFilesList: [], // 未上传的附件
      fromName: '',
      haveImg: false,
      custId: '',
      chooseFilesIndex: -1,
      device: '',
      haveSimFiles: [],
      simFilesList: [],
      showbackPopup: false, // 返回展示
    };
  },
  computed: {
    ...mapState(['uid', 'productSource', 'uploadDocuments']),
    documentTypeObj() {
      if (this.documentType) {
        return this.documentsObj[this.documentType]
      } else {
        return {}
      }
    },
    canSubmit() {
      return this.haveSimFiles.length > 0;
    }
  },
  mounted() {
    this.documentType = this.$route.query.documentType;
    this.report(`upload_documents_${this.documentType}_view`);
    this.haveSimFiles = this.uploadDocuments[this.documentType];
    this.androidVersion = getOsVersionCode();
    getCustId().then(custId => {
      console.log('getCustId', custId);
      this.custId = custId;
    });
    this.device = judgeClient();
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.fromName = from;
    })
  },

  methods: {
    myBackFun() {
      if (this.canSubmit) {
        this.showbackPopup = true;
      } else {
        this.$router.go(-1);
      }
    },
    report(eventName) {
      let vm = this;
      vm.$store.dispatch('reportEvent', {
        page: 'uploadDocuments',
        eventName: eventName,
        eventData: {
          event_time: Date.now()
        }
      });
    },
    chooseFiles(index, list) {
      this.chooseFilesIndex = index;
      this.report(`upload_documents_${this.documentType}_upload_click`);
      openFeedBackFile('uploadFilesCallBack', this.acceptType, this.acceptType, true)
      window.uploadFilesCallBack = (res) => {
        const result = JSON.parse(decode(res))
        console.log(result)
        if (result.length > 0) {
          if (this.chooseFilesIndex === -1) {
            let fileCount = this[list].length;
            fileCount += result.length
            if (fileCount > 2) {
              this.$toast('The number of files cannot exceed 2.')
              return
            }
          } else {
            if (result.length > 1) {
              this.$toast('The chosen of files cannot exceed 1.')
              return
            }
          }
          let fileSize = 0
          result.forEach(item => {
            fileSize += parseFloat(item.fileSize)
          })
          if (fileSize > 10485760) {
            this.$toast('The file size cannot exceed 10M.')
            return
          }
          this.fileSize = fileSize;

          let uploadFilesList = ''
          list === 'haveSimFiles' ? uploadFilesList = 'simFilesList' : uploadFilesList = 'uploadFilesList'
          console.log('...resultbefore', ...result);
          // 目前只允许一个
          this[uploadFilesList] = [];
          // result是一个数组
          this[uploadFilesList].push(...result);
          console.log('...resultafter', this[uploadFilesList]);
          this.uploadFile(list, uploadFilesList);
          this.report(`upload_documents_${this.documentType}_choose_file_succ`);
        }
      }
    },
    uploadFile(list, uploadFilesList) {
      this.$loading();
      let ids = [];
      if (this[uploadFilesList].length > 0) {
        const queryArr = []
        this[uploadFilesList].forEach((item, index) => {
          console.log('index', index);
          const query = new Promise((resolve, reject) => {
            this.$http(api.bigDataUrl.uploadFile, {
              data: {
                filePath: item.filePath,
                type: 'documents',
                uid: this.uid
              },
              addHeader: {
                isUploadFile: true,
                'X-DX-Country': 'NG',
                requestType: 'uploadFile'
              }
            }).then((res) => {
              console.log('uploadFileSuccess', res);
              ids.push(res.data.id);
              resolve(res.data.id)
            }).catch((res) => {
              console.log('resError1', res);
              this.report(`upload_file_error_size_${item.fileSize}`);
              reject(new Error('Upload failed. Please try again later.'))
            })
          })
          queryArr.push(query)
        })
        Promise.all(queryArr).then(res => {
          console.log('resSuccess', res);
          this.getImgPath(res, list, uploadFilesList)
        }).catch(res => {
          this.$hideLoading();
          console.log('resError2', res);
          // 因为客户端存在bug，上传一张出现超时时，后面的上传都会报错。
          // 所以这里补充调用，若出现报错，若过程中有上传成功的，继续走后续逻辑。
          // 注意多张的时候，可能会影响正常的上传，后续若出现多张时，则要判断正常的数组长度。
          if (ids.length > 0) {
            this.getImgPath(ids, list, uploadFilesList)
          } else if (res.message) {
            this.$toast(res.message)
          } else {
            this.$toast(res)
          }
        })
      } else {
        this.$toast('Please upload this information!')
      }
    },
    // 获取预览路径（路径有效期为一天）
    getImgPath(ids, list, uploadFilesList) {
      console.log('getImgPathStart', ids, list, uploadFilesList);
      this.report(`upload_${this.documentType}_file_succ`);
      this.$http(api.bigDataUrl.getUrls, {
        data: {
          keys: ids
        },
        addHeader: {
          isBigdata: true,
          requestType: 'complaintUpload'
        }
      }).then((res) => {
        console.log('getImgPath', res.data);
        this.$hideLoading();
        console.log('getImgPath', 'chooseFilesIndex', this.chooseFilesIndex)
        if (this.chooseFilesIndex === -1) {
          ids.forEach((id, index) => {
            const fileType = this.getFileExtension(this[uploadFilesList][index].filePath);
            this[list].push({
              id: id,
              img: fileType !== 'pdf' ? res.data[index] : shorturlPdf,
              fileType: fileType,
              fileName: this[uploadFilesList][index].fileName
            })
          })
        } else { // 替换现有图片
          const fileType = this.getFileExtension(this[uploadFilesList][0].filePath);
          this[list][this.chooseFilesIndex] = {
            id: ids[0],
            img: fileType !== 'pdf' ? res.data[0] : shorturlPdf,
            fileType: fileType,
            fileName: this[uploadFilesList][0].fileName
          }
          console.log(res.data[0])
          this.$forceUpdate();
        }
        console.log('文件上传显示方法', this[list]);
        this.chooseFilesIndex = -1;
        this[uploadFilesList] = [];
      }).catch((e) => {
        console.log(e);
        if (e.msg) {
          this.$toast(e.msg);
        }
        this.$hideLoading();
      })
    },
    // 提交增信
    submitForm() {
      this.report(`upload_documents_${this.documentType}_submit_click`);
      if (this.fileCount < 1) {
        this.$toast('Please upload this information!');
        return;
      }
      // 提交时才把数据更新到缓存中
      console.log('this.simFilesList', this.simFilesList)
      this.$store.commit('SET_UPLOADDOCUMENTS', {
        key: this.documentType,
        value: this.haveSimFiles
      });
      this.$router.go(-1);
    },
    goPre(list, index) {
      let imgList = []
      list.forEach((item) => {
        imgList.push(item.img);
      });
      // pdf类型不支持预览
      if (imgList[index].fileType === 'pdf') return;
      console.log('imgList', imgList);
      if (imgList.length > 0) {
        ImagePreview({
          images: imgList,
          startPosition: index,
          onClose() {
          },
        });
      }
    },
    // 获取文件扩展名
    getFileExtension(filePath) {
      // 使用正则表达式来匹配文件扩展名
      if (filePath) {
        const match = filePath.match(/\.([0-9a-z]+)(?:[?#]|$)/i);
        // 如果有匹配项，则返回第一个捕获组（即文件扩展名）否则返回空字符串
        return match ? match[1] : '';
      } else {
        return ''
      }
    },
    hidePopup(type) {
      this.showbackPopup = false;
      if (type === 'confirm') {
        this.submitForm();
      } else {
        this.$router.go(-1);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.upload-documents{
  min-height: 100%;
  background: #F9FAFF;
  padding-bottom: 80px;
  .c-header{
    left: 0;
  }
  .top{
    padding: 68px 20px 20px 20px;
    background: #ffffff;
    position: relative;
    margin-bottom: 12px;
    .tips{
      color: #1B3155;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      .show-type{
        ::v-deep span{
          font-weight: 900;
        }
      }
    }
    .sub-tips{
      color: #919DB3;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      margin-top: 14px;
    }
    img{
      width: 45px;
      height: 45px;
      position: absolute;
      right: 0px;
      bottom: 20px;
    }
  }
  .upload{
    padding: 0 20px 20px 20px;
    background: #ffffff;
    .title{
      padding-top: 5px;
      color: #1B3155;
      font-family: Avenir;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
    }
    .upload-p {
      color: #919DB3;
      font-family: Avenir;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-align: left;
      margin: 5px 0 15px;
      span {
        color: #29ABE2;
      }
    }
    .upload-item{
      height: 95px;
      width: 320px;
      padding: 15px 12px;
      justify-content: space-between;
      align-items: center;
      border-radius: 6px;
      border: 1px solid $themeColor;
      margin-top: 10px;
      justify-content: center;
      align-items: center;
      display: flex;
      // background-position:center center;
      background-size:320px 95px;
      background-repeat: no-repeat;
      box-sizing: border-box;
      svg{
        width: 40px;
        height: 40px;
        circle{
          fill: $themeColor;
        }
      }
    }
    .upload-item-len {
      width: 162px;
      display: inline-flex;
      &:last-child {
        margin-left: 10px;
      }
    }
    .uploaded-img{
      width: 100px;
      width: 200px;
    }
    .sample-list{
      display: flex;
      margin-top: 12px;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 0 35px;
      .sample{
        width: 100px;
        .show-img{
          width: 100px;
          height: 73px;
          background-size: 100px 73px;
          position: relative;
          img{
            width: 64px;
            height: 18px;
            position: absolute;
            right: -4px;
            top: -14px;
          }
        }
        .content{
          color: #858585;
          text-align: center;
          font-family: Noto Sans;
          font-size: 12px;
          transform: scale(.84);
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-bottom: 5px;
          margin-top: 5px;
        }
      }
      &.bankAccountBalance{
        padding: 0 25px;
        li{
          width: 120px;
          &:nth-child(1){
            margin-top: 15px;
            img{
              right: 17px !important;
            }
            .show-img{
              height: 82px;
            }
          }
          &:nth-child(2){
            .show-img{
              width: 65px;
              height: 97px;
              margin: auto;
            }
          }
          .content{
            width: 142px;
            margin-left: -20px;
          }
        }
      }
      &.photoOfWorkBadge{
        justify-content: center;
        li{
          width: 120px;
          .show-img{
            width: 57px;
            height: 96px;
            margin: auto;
            background-size: 57px 96px;
          }
          .content{
            width: 142px;
            margin-left: -10px;
          }
        }
      }
      &.salaryPayslip{
        justify-content: center;
        li{
          width: 96px;
          .show-img{
            width: 96px;
            height: 106px;
            margin: auto;
            background-size: 96px 106px;
          }
          img{
            right: 15px !important;
          }
          .content{
            width: 114px;
            margin-left: -10px;
          }
        }
      }
    }
  }
  .c-button{
    position: fixed;
    bottom: 40px;
    background: #C5C5C5;
    &.submit{
      background: $themeColor;
    }
  }
}

</style>
