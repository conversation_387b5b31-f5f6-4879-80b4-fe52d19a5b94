<template>
  <div class="native-demo">
    <c-header :titleName="`nativeDemo-${appProduct}`" />
    <ul class="native-list">
      <li v-for="(fun, index) in nativeList" :key="index">
        <div class="title" v-text="`1. 原生方法名: ${index}`"></div>
        <div class="des" v-text="`2. 功能说明: ${fun.funtionDes}`"></div>
        <div class="action" @click="commonAction(fun, index)">
          <div>3. </div>
          <div class="click">点击调用</div>
        </div>
        <div class="result">
          <div>4. 返回结果:</div>
          <div class="res" v-if="fun.value" v-text="fun.value"></div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import {
  httpRequest,
  getCustId,
  getGpsInfo,
  getWifiList,
  getDeviceId,
  gotoHomeActivity,
  activateTradeBigdata,
  getCurrentAppVersion,
  getUid,
  applyAppsflyerData,
  gotoGooglePlay,
  uploadAdsEvent,
  startPage,
  endPage,
  logClickEvent,
  logViewEvent,
  openFeedBackFile,
  getOsVersionCode,
  getSupportedFeatures,
  getPhone,
  getNetwork,
  getAesString,
  existedPin,
  setExistedPin,
  gotoLoanList,
  getOsName,
  getDefaultTimezone,
  getOsVersion,
  getCurrentAppVersionName,
  gotoBindBankAccount,
  gotoBindBankCard,
  addAppsflyerData,
  callPhone,
  setCalendar,
  openAppStore,
  getChannelId,
  showLoading,
  hideJsLoading,
  log,
  h5Loading,
  h5HideLoading,
  getEnCustId,
  getNcInfo,
  gotoBalanceTab,
  openSystemContact,
  startFaceIdentify,
  showToast
} from '@/assets/js/native';
import api from "@/api/interface";
import { decode } from 'js-base64';
import { isIOS } from "@/assets/js/common";

export default {
  name: 'nativeDemo',
  data() {
    return {
      appProduct: process.env.VUE_APP_PRODUCT || 'palmcredit',
      productSource: localStorage.getItem('productSource') || '',
      acceptType: 'application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|application/vnd.ms-excel|application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|application/pdf|image/*',
      nativeList: {
        httpRequest: {
          funtionDes: '原生代理请求: 样例，请求业务popAPPConfig接口(所有包都支持)',
          value: ''
        },
        activateTradeBigdata: {
          funtionDes: '激活大数据埋点(所有包都支持)',
          value: ''
        },
        getUid: {
          funtionDes: '获取用户uid(所有包都支持)',
          action: getUid,
          value: ''
        },
        getCustId: {
          funtionDes: '获取用户custId(所有包都支持)',
          action: getCustId,
          value: ''
        },
        gotoHomeActivity: {
          funtionDes: '跳转到APP首页(所有包都支持)',
          action: gotoHomeActivity,
          value: ''
        },
        getGpsInfo: {
          funtionDes: '获取GPS信息(所有包都支持)',
          action: getGpsInfo,
          value: ''
        },
        getWifiList: {
          funtionDes: '获取wifi列表信息(所有包都支持)',
          action: getWifiList,
          value: ''
        },
        getDeviceId: {
          funtionDes: '获取设备id(所有包都支持)',
          action: getDeviceId,
          value: ''
        },
        applyAppsflyerData: {
          funtionDes: '获取appsflyer数据(接入AF的包都支持)',
          action: applyAppsflyerData,
          value: ''
        },
        getPhone: {
          funtionDes: '获取手机号(所有包都支持)',
          action: getPhone,
          value: ''
        },
        openFeedBackFile: {
          funtionDes: '选取手机文件(部分包支持)',
          action: openFeedBackFile,
          value: ''
        },
        getOsVersionCode: {
          funtionDes: '获取系统版本(部分包支持)',
          action: getOsVersionCode,
          value: ''
        },
        uploadAdsEvent: {
          funtionDes: 'IOS归因埋点上报功能(只有ios才需要)',
          action: uploadAdsEvent,
          value: ''
        },
        startPage: {
          funtionDes: '进入页面时调用此接口，pageName为页面名称(部分包支持)',
          action: startPage,
          value: ''
        },
        endPage: {
          funtionDes: '退出页面时调用此接口，pageName为页面名称(部分包支持)',
          action: endPage,
          value: ''
        },
        logClickEvent: {
          funtionDes: '点击接口，pageName为页面名称，eventName为事件名称(部分包支持)',
          action: logClickEvent,
          value: ''
        },
        logViewEvent: {
          funtionDes: '曝光接口，pageName为页面名称，eventName为事件名称(部分包支持)',
          action: logViewEvent,
          value: ''
        },
        getCurrentAppVersion: {
          funtionDes: '获取app版本号',
          action: getCurrentAppVersion,
          value: ''
        },
        gotoGooglePlay: {
          funtionDes: '跳转到googleplay应用市场',
          action: gotoGooglePlay,
          value: ''
        },
        getSupportedFeatures: {
          funtionDes: '获取客户端功能支持功能信息',
          action: getSupportedFeatures,
          value: ''
        },
        getNetwork: {
          funtionDes: '获取网络类型',
          action: getNetwork,
          value: ''
        },
        getAesString: {
          funtionDes: '获取AES字符串',
          action: getAesString,
          value: ''
        },
        existedPin: {
          funtionDes: '判断是否已设置PIN',
          action: existedPin,
          value: ''
        },
        setExistedPin: {
          funtionDes: '设置已存在的PIN',
          action: setExistedPin,
          value: ''
        },
        gotoLoanList: {
          funtionDes: '跳转到贷款列表',
          action: gotoLoanList,
          value: ''
        },
        getOsName: {
          funtionDes: '获取操作系统名称',
          action: getOsName,
          value: ''
        },
        getDefaultTimezone: {
          funtionDes: '获取默认时区',
          action: getDefaultTimezone,
          value: ''
        },
        getOsVersion: {
          funtionDes: '获取操作系统版本',
          action: getOsVersion,
          value: ''
        },
        getCurrentAppVersionName: {
          funtionDes: '获取当前应用版本名称',
          action: getCurrentAppVersionName,
          value: ''
        },
        gotoBindBankAccount: {
          funtionDes: '跳转到绑定银行账户页面',
          action: gotoBindBankAccount,
          value: ''
        },
        gotoBindBankCard: {
          funtionDes: '跳转到绑定银行卡页面',
          action: gotoBindBankCard,
          value: ''
        },
        addAppsflyerData: {
          funtionDes: '添加Appsflyer数据',
          action: addAppsflyerData,
          value: ''
        },
        callPhone: {
          funtionDes: '拨打电话',
          action: callPhone,
          value: ''
        },
        setCalendar: {
          funtionDes: '设置日历提醒',
          action: setCalendar,
          value: ''
        },
        openAppStore: {
          funtionDes: '打开应用商店',
          action: openAppStore,
          value: ''
        },
        getChannelId: {
          funtionDes: '获取渠道ID',
          action: getChannelId,
          value: ''
        },
        showLoading: {
          funtionDes: '显示加载中',
          action: showLoading,
          value: ''
        },
        hideJsLoading: {
          funtionDes: '隐藏JS加载',
          action: hideJsLoading,
          value: ''
        },
        log: {
          funtionDes: '记录日志',
          action: log,
          value: ''
        },
        h5Loading: {
          funtionDes: '显示H5加载',
          action: h5Loading,
          value: ''
        },
        h5HideLoading: {
          funtionDes: '隐藏H5加载',
          action: h5HideLoading,
          value: ''
        },
        getEnCustId: {
          funtionDes: '获取加密的客户ID',
          action: getEnCustId,
          value: ''
        },
        getNcInfo: {
          funtionDes: '获取NC信息',
          action: getNcInfo,
          value: ''
        },
        gotoBalanceTab: {
          funtionDes: '跳转到余额标签',
          action: gotoBalanceTab,
          value: ''
        },
        openSystemContact: {
          funtionDes: '打开系统联系人',
          action: openSystemContact,
          value: ''
        },
        startFaceIdentify: {
          funtionDes: '开始人脸识别',
          action: startFaceIdentify,
          value: ''
        }
      }
    }
  },
  mounted() {
    this.popAPPConfig();
  },
  methods: {
    popAPPConfig() {
      httpRequest(api.repayment.popAPPConfig, {})
    .then((res) => {
        console.log(res);
        this.nativeList.httpRequest.value = res.data
      })
          .catch((err) => {
            console.log(err)
          })
    },
    commonAction(fun, name) {
      if (name === 'httpRequest') {
        this.popAPPConfig();
        return
      }

      if (fun.action) {
        // 使用方法分类映射表来替代冗长的条件判断
        const methodTypes = {
          // 直接返回值的方法（在业务代码中直接使用返回值）
          directReturn: ['getPhone', 'getChannelId', 'getOsVersionCode'],

          // 返回Promise的方法（在业务代码中使用await或then处理）
          returnPromise: ['getAesString', 'existedPin', 'getCustId', 'getDeviceId', 'getNetwork', 'getOsName',
                        'getDefaultTimezone', 'getOsVersion', 'getCurrentAppVersionName', 'getCurrentAppVersion',
                        'getUid', 'getEnCustId', 'getSupportedFeatures', 'getWifiList', 'getNcInfo'],

          // 无返回值的方法（执行操作但不返回结果，或通过回调处理）
          noReturn: ['gotoLoanList', 'gotoHomeActivity', 'finish', 'applyAppsflyerData', 'addAppsflyerData',
                    'callPhone', 'startPage', 'endPage', 'logClickEvent', 'logViewEvent', 'setExistedPin',
                    'openAppStore', 'showLoading', 'hideJsLoading', 'log', 'h5Loading',
                    'h5HideLoading', 'gotoBalanceTab', 'setCalendar', 'uploadAdsEvent', 'getGpsInfo',
                    'openSystemContact', 'startFaceIdentify', 'openFeedBackFile', 'gotoBindBankAccount',
                    'gotoBindBankCard'],

          // 特殊处理的方法（需要特定参数或有特殊逻辑）
          special: ['activateTradeBigdata', 'httpRequest']
        };

        // 直接返回值的方法
        if (methodTypes.directReturn.includes(name)) {
          this.nativeList[name].value = fun.action();
          return;
        }

        // 返回Promise的方法
        if (methodTypes.returnPromise.includes(name)) {
          const res = fun.action();
          if (res && typeof res.then === 'function') {
            res.then(result => {
              this.nativeList[name].value = result;
            });
          } else {
            this.nativeList[name].value = res;
          }
          return;
        }

        // 无返回值的方法（包括通过回调处理的方法）
        if (methodTypes.noReturn.includes(name)) {
          // 需要回调函数的方法特殊处理
          if (name === 'getGpsInfo' || name === 'openSystemContact' || name === 'startFaceIdentify' || name === 'openFeedBackFile' ||
              name === 'gotoBindBankAccount' || name === 'gotoBindBankCard') {
            const callback = `${name}callback`;
            console.log('callback', callback);
            if (name === 'openFeedBackFile') {
              window.uploadFilesCallBack = (res) => {
                const result = JSON.parse(decode(res))
                console.log('openFeedBackFile result:', result)
                if (result.length > 0) {
                  // 在实际业务中会处理文件大小和数量限制
                  // 这里只显示结果
                  this.nativeList[name].value = `选择了 ${result.length} 个文件: ` +
                    result.map(item => `${item.fileName}(${(parseFloat(item.fileSize)/1024/1024).toFixed(2)}MB)`).join(', ')
                } else {
                  this.nativeList[name].value = '未选择文件'
                }
              }
              this.nativeList[name].action('uploadFilesCallBack', this.acceptType, this.acceptType, true)
              return
            } else {
              window[callback] = (res) => {
                console.log(name, res)
                try {
                  this.nativeList[name].value = JSON.parse(decode(res))
                } catch (err) {
                  this.nativeList[name].value = decode(res)
                }
              }
            }
            this.nativeList[name].action(callback)
          } else {
            // 无返回值的方法，根据不同方法的需要提供不同的参数
            switch(name) {
              case 'callPhone':
                this.nativeList[name].action('10000'); // 示例电话号码
                break;
              case 'setCalendar':
                // eslint-disable-next-line no-case-declarations
                const calendarParams = {
                  title: '还款提醒',
                  description: '请记得按时还款',
                  location: '',
                  startTime: new Date().getTime() + 86400000, // 明天
                  endTime: new Date().getTime() + 86400000 + 3600000, // 明天+1小时
                  allDay: false
                };
                this.nativeList[name].action(JSON.stringify(calendarParams));
                break;
              case 'log':
                this.nativeList[name].action('nativeDemo', 'test log message');
                break;
              case 'showLoading':
                this.nativeList[name].action();
                setTimeout(() => {
                  hideJsLoading();
                }, 2000);
                break;
              case 'h5Loading':
                this.nativeList[name].action();
                setTimeout(() => {
                  h5HideLoading();
                }, 2000);
                break;
              case 'startPage':
                this.nativeList[name].action('nativeDemo');
                break;
              case 'endPage':
                this.nativeList[name].action('nativeDemo');
                break;
              case 'logClickEvent':
                this.nativeList[name].action('nativeDemo', 'nativeDemo_click');
                break;
              case 'logViewEvent':
                this.nativeList[name].action('nativeDemo', 'nativeDemo_view');
                break;
              case 'uploadAdsEvent':
                if (isIOS()) {
                  this.nativeList[name].action('apply_withdraw');
                }
                break;
              case 'gotoGooglePlay':
                this.nativeList[name].action(this.$store.state.channelPackageName);
                break;
              case 'addAppsflyerData':
                this.nativeList[name].action('test_event', {'param1': 'value1', 'param2': 'value2'});
                break;
              default:
                // 其他无参数的方法直接调用
                this.nativeList[name].action();
            }
            this.nativeList[name].value = '操作成功'; // 无返回值的方法显示固定文本
          }
          return;
        } else if (methodTypes.special.includes(name)) {
          // 特殊处理的方法
          switch(name) {
            case 'activateTradeBigdata':
              if (!this.nativeList.getCustId.value) {
                showToast('请先获取getCustId');
                return;
              }
              window.setActivateTradeResult = (res) => {
                const result = JSON.parse(decode(res));
                console.log('setActivateTradeResult', result);
                this.nativeList.activateTradeBigdata.value = result.activateState;
              }
              activateTradeBigdata(this.nativeList.getCustId.value, 'setActivateTradeResult');
              break;
            case 'httpRequest':
              this.popAPPConfig();
              break;
          }
          return;
        } else {
          // 其他方法的默认处理
          const res = this.nativeList[name].action();
          if (res && typeof res.then === 'function') {
            res.then(res => {
              this.nativeList[name].value = res;
            });
          } else {
            this.nativeList[name].value = res;
          }
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.native-demo{
  height: calc(100vh - 56px);
  .native-list{
    margin-top: 56px;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 25px;
    height: 100%;
    overflow: scroll;
    li{
      padding: 15px;
      border: 1px solid #c4c3c3;
      border-radius: 14px;
      line-height: 20px;
      margin-bottom: 10px;
      text-align: left;
      div{
        font-size: 12px;
      }
      .action{
        display: flex;
        align-items: center;
        .click{
          padding: 2.5px;
          color: #fff;
          background-color: #07c160;
          border: 0.5px solid #07c160;
        }
      }
      .result{
        .res{
          border: 1px solid #ababab;
          border-radius: 5px;
          padding: 5px;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
