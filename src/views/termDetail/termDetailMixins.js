import unChecked from '@/assets/images/un-paid.png';
import pastChecked from '@/assets/images/past-paid.png';
import CMoney from '@/components/c-money.vue';
import CButton from '@/components/c-button.vue';
import singleTermDetail from './components/single-term-detail.vue';
import singleTermDetailWayacredit from './components/single-term-detail-wayacredit.vue';
import { mapState } from 'vuex';
export default {
  name: 'termDetail',
  components: {
    CMoney,
    CButton,
    singleTermDetail,
    singleTermDetailWayacredit
  },
  computed: {
  ...mapState(['productSource', 'repaySchduleList'])
  },
  data() {
    return {
      unChecked,
      pastChecked,
      show: false,
      termData: {}
    };
  },
  methods: {
    goContactUs() {
      this.$router.push({path: '/loadUrlPage', query: {
        url: process.env.VUE_APP_CONTACT_US_URL,
        title: 'Contact Us'
      }});
    },
    // 获取勾选的图片
    getCheckedImg(loan) {
      let vm = this;
      if (!loan.canCheck) {
        return vm.pastChecked;
      } else {
        return vm.unChecked;
      }
    },
    // 开始勾选
    startCheck(index, canCheck) {
      let vm = this;
      let checked = vm.repaySchduleList[index].checked;
      let isChecked = !checked;
      if (!canCheck) {
        return;
      } else {
        vm.repaySchduleList[index].checked = isChecked;
        if (isChecked) {
          // 勾选当前项，小于等于当前项的全部需要被勾选
          vm.repaySchduleList.forEach((item, itemIndex) => {
            if (item.termStatus !== 'F' && itemIndex <= index) {
              item.checked = isChecked;
            }
          });
        } else {
          // 需要取消勾选当前项，大于等于当前项的全部需要取消勾选
          vm.repaySchduleList.forEach((item, itemIndex) => {
            if (item.termStatus !== 'F' && itemIndex >= index) {
              item.checked = isChecked;
            }
          });
        }
      }
      vm.$store.commit('SET_REPAYSCHDULELIST', vm.repaySchduleList);
    },
    goBack() {
      let vm = this;
      let haveChecked = false;
      vm.repaySchduleList.forEach(item => {
        if (item.checked) {
          haveChecked = true;
        }
      });
      if (haveChecked) {
        vm.$router.go(-1);
      } else {
        vm.$toast({
          message: 'Please select term',
          className: 'toast-over-write'
        });
      }
    },
    showDetail(data) {
      let vm = this;
      vm.show = true;
      console.log('data', data);
      vm.termData = data;
    },
    changeShow() {
      this.show = false;
    }
  },
};
