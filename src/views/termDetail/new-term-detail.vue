
<template>
    <div class="container">
        <component ref="child" :is="componentTag"></component>
    </div>
</template>
<script>
import { globalConfig } from '@/api/config'
export default {
  name: 'termDetail',
  components: {
    baseTermDetail: () => import(/* webpackChunkName: "termDetailBase" */ './page/termDetailBase.vue'),
    wayacredit: () => import(/* webpackChunkName: "termDetailWayacredit" */ './page/termDetailWayacredit.vue'),
    termDetailBaseCommon1: () => import(/* webpackChunkName: "termDetailBaseCommon1" */ './page/termDetailBaseCommon1.vue'),
  },
  data() {
      return {
        componentTag: 'baseTermDetail',
        to: '',
        from: '',
      }
  },
  created() {
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.termDetail) {
      this.componentTag = globalConfig.pageStyleSetting.termDetail
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.to = to;
      vm.from = from;
      if (vm.$refs && vm.$refs.child && vm.$refs.child.beforeRouteEnter) {
        vm.$refs.child.beforeRouteEnter(to, from);
      }
    })
  }
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}
</style>
