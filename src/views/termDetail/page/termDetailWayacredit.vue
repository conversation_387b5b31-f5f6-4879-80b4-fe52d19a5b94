<template>
  <div class="term-detail-wayacredit">
    <c-header titleName="Repayment" :backFun="goBack" :styType="'b'">
    </c-header>
    <ul class="list">
      <li v-for="(repay, index) in repaySchduleList" :key="repay.loanId + repay.dueDate" @click="startCheck(index, repay.canCheck)">
        <div class="line-1">
          <img class="choose" :src="repay.checked ? activeCircleIconWayacredit : handleIconWayacredit(repay)" alt="">
          <div class="title">Repayment Amount</div>
        </div>
        <div class="repayment-detail">
          <div class="amount">
            <CMoney :currencyNum="`${repay.repaymentAmount}`" />
          </div>
          <div class="tips">
            <span>DueDay:</span> {{ repay.dueDate }} 
          </div>
        </div>
        <div class="detail" @click.stop="showDetail(repay)">Detail</div>
        <img class="status" :src="require(`@/assets/images/termStatus/${repay.termStatus}.png`)" alt="">
      </li>
    </ul>
    <CButton @buttonClick="goBack()" name='Confirm' />
    <singleTermDetailWayacredit :showTermDetail="show" :termDetail="termData" @close="changeShow"></singleTermDetailWayacredit>
  </div>
</template>

<script>
import publicMixns from '../termDetailMixins.js'
import disableCircleIconWayacredit from '@/assets/images/wayacredit/ic_circle_disable.png'
import activeCircleIconWayacredit from '@/assets/images/wayacredit/ic_circle_active.png'
import inactiveCircleIconWayacredit from '@/assets/images/wayacredit/ic_circle_inactive.png'
export default {
    name: 'termDetailWayacredit',
    mixins: [publicMixns],
    data(){
      return {
        disableCircleIconWayacredit,
        activeCircleIconWayacredit,
        inactiveCircleIconWayacredit
      }
    },
    mounted(){

    },
    methods: {
      // 勾选状态图片
      handleIconWayacredit(loan) {
        if (!loan.canCheck) {
          return this.disableCircleIconWayacredit;
        } else {
          return this.inactiveCircleIconWayacredit;
        }
      }
    }
}
</script>

<style lang="scss" scoped>
.term-detail-wayacredit{
  background: #1E1E20;
  height: 100%;
  .c-header{
      .contact-us{
        width: 53px;
        height: 40px;
        margin-right: 18px;
      }
  }
  .list{
    margin-top: 56px;
    padding-bottom: 106px;
    li{
      border-radius: 13px;
      padding: 18px 10px 18px 10px;
      position: relative;
      border-radius: 18px;
      border: 0.5px solid #FFF;
      background: rgba(40, 42, 48, 0.80);
      margin: 10px;
      .line-1{
        display: flex;
        align-items: center;
        .choose{
          width: 20px;
          height: 20px;
          margin-right: 9px;
        }
        .title{
          font-family: Avenir-Medium;
          font-size: 14px;
          color: #fff;
        }
      }
      .repayment-detail{
        padding-left: 40px;
        .amount{
          ::v-deep .c-money{
            margin-left: -10px;
            .monetary-unit{
              transform: none;
              font-size: 22px;
              color: #00FFAD;
            }
            .currency-num{
              font-size: 19px;
              color: #00FFAD;
              font-weight: 700;
            }
          }
        }
        .tips{
          font-size: 12px;
          color: #fff;
          line-height: 14px;
          font-weight: 500;
          text-align: left;
          transform: scale(.94);
          margin-left: -18px;
          span{
            color: #fff;
            font-size: 13px;
            transform: none;
          }
        }
      }
      .detail{
        position: absolute;
        top: 16px;
        right: 14px;
        height: 18px;
        font-family: Avenir-Heavy;
        font-size: 14px;
        color: #00FFAD;
        text-align: center;
        font-weight: 800;
      }
      .status{
        width: 126px;
        height: 70px;
        position: absolute;
        bottom: 0px;
        right: 0px;
      }
    }
  }
  .c-button{
    width: calc(100% - 44px);
    margin-top: 50px;
    position: fixed;
    bottom: 50px;
    height: 50px;
    left: 22px;
    border-radius: 10px;
    background: #00FFAD;
    color: #1E1E20;
  }
}
</style>