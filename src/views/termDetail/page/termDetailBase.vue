<template>
  <div class="term-detail">
    <c-header titleName="Repayment" :backFun="goBack">
      <template #right>
        <img class="contact-us" @click="goContactUs()" :src="require(`@/assets/images/${productSource}/contact-us.png`)" alt="">
      </template>
    </c-header>
    <ul class="list">
      <li v-for="(repay, index) in repaySchduleList" :key="repay.loanId + repay.dueDate" @click="startCheck(index, repay.canCheck)">
        <div class="line-1">
          <img class="choose" :src="repay.checked ?  require(`@/assets/images/${productSource}/index-paid.png`) : getCheckedImg(repay)" alt="">
          <div class="title">Repayment Amount</div>
        </div>
        <div class="repayment-detail">
          <div class="amount">
            <CMoney :currencyNum="`${repay.repaymentAmount}`" />
          </div>
          <div class="tips">
            <span>DueDay:</span> {{ repay.dueDate }} 
          </div>
        </div>
        <div class="detail" @click.stop="showDetail(repay)">Detail</div>
        <img class="status" :src="require(`@/assets/images/termStatus/${repay.termStatus}.png`)" alt="">
      </li>
    </ul>
    <CButton @buttonClick="goBack()" name='Confirm' />
    <singleTermDetail :showTermDetail="show" :termDetail="termData" @close="changeShow"></singleTermDetail>
  </div>
</template>

<script>
import publicMixns from '../termDetailMixins.js'
export default {
    name: 'termDetailBase',
    mixins: [publicMixns],
    data(){
      return {
      }
    },
    mounted(){
    }
}
</script>

<style lang="scss" scoped>
.term-detail{
  background: #F2F3F8;
  min-height: 100%;
  .c-header{
    background: #F2F3F8;
      .contact-us{
        width: 53px;
        height: 40px;
        margin-right: 18px;
      }
  }
  .list{
    margin-top: 56px;
    padding-bottom: 106px;
    li{
      border-radius: 13px;
      padding: 18px 10px 18px 10px;
      position: relative;
      background: #FFFFFF;
      margin: 10px;
      .line-1{
        display: flex;
        align-items: center;
        .choose{
          width: 20px;
          height: 20px;
          margin-right: 9px;
        }
        .title{
          font-family: Avenir-Medium;
          font-size: 14px;
          color: #919DB3;
        }
      }
      .repayment-detail{
        padding-left: 40px;
        .amount{
          ::v-deep .c-money{
            margin-left: -10px;
            .monetary-unit{
              transform: none;
              font-size: 22px;
              color: #536887;
            }
            .currency-num{
              font-size: 38px;
              color: #1B3155;
              font-weight: 700;
            }
          }
        }
        .tips{
          font-size: 12px;
          color: #919DB3;
          line-height: 14px;
          font-weight: 500;
          text-align: left;
          transform: scale(.94);
          margin-left: -18px;
          span{
            color: #1B3155;
            font-size: 13px;
            transform: none;
          }
        }
      }
      .detail{
        position: absolute;
        top: 16px;
        right: 14px;
        height: 18px;
        font-family: Avenir-Heavy;
        font-size: 14px;
        color: $color;
        text-align: center;
        font-weight: 800;
      }
      .status{
        width: 126px;
        height: 70px;
        position: absolute;
        bottom: 0px;
        right: 0px;
      }
    }
  }
  .c-button{
    margin-top: 50px;
    position: fixed;
    bottom: 50px;
    height: 60px;
  }
}
</style>