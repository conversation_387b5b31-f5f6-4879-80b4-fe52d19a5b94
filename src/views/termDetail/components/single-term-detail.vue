<template>
  <div class="single-term-detail">
    <van-dialog v-model="showDialog" class="detail" :showConfirmButton='false'>
      <ul class="list">
        <li>
          <div class="key">Due date</div>
          <div class="value" v-text="termDetail.dueDate"></div>
        </li>
        <li>
          <div class="key">Principal</div>
          <CMoney :currencyNum="common.thousandsToFixed(termDetail.principal)"></CMoney>
        </li>
        <li>
          <div class="key">Interest</div>
          <CMoney :currencyNum="common.thousandsToFixed(termDetail.interest)"></CMoney>
        </li>
        <li v-if="termDetail.vatAmount">
          <div class="key">VAT</div>
          <CMoney :currencyNum="common.thousandsToFixed(termDetail.vatAmount)"></CMoney>
        </li>
        <li v-if="termDetail.penaltyCharge > 0">
          <div class="key">Penalty Charge</div>
          <CMoney :currencyNum="common.thousandsToFixed(termDetail.penaltyCharge)"></CMoney>
        </li>
        <li v-if="termDetail.serviceCharge > 0">
          <div class="key">Service Charge</div>
          <CMoney :currencyNum="common.thousandsToFixed(termDetail.serviceCharge)"></CMoney>
        </li>
        <li v-if="termDetail.insuranceAmount > 0">
          <div class="key">Credit Protection Service</div>
          <CMoney :currencyNum="common.thousandsToFixed(termDetail.insuranceAmount)"></CMoney>
        </li>
        <li>
          <div class="key">Repayment Amount</div>
          <template v-if="termDetail.promiseRepaydate && termDetail.status !== 'F'">
            <div>
              <div class="up">
                <CMoney :currencyNum="common.thousandsToFixed(termDetail.repaymentAmount)"></CMoney>
                <div class="before" v-text="`₦${termDetail.reduceBeforRepaymentAmount}`"></div>
              </div>
              <div class="down" v-text="`vaild until:${termDetail.promiseRepaydate}`"></div>
            </div>
          </template>
          <CMoney v-else :currencyNum="common.thousandsToFixed(termDetail.repaymentAmount)"></CMoney>
        </li>
      </ul>
      <CButton :preventReClickTime="100" @buttonClick="close()" name='Close' />
    </van-dialog>
  </div>
</template>

<script>
import CMoney from '@/components/c-money.vue';
import CButton from '@/components/c-button.vue';
export default {
  name: 'singleTermDetail',
  components: {
    CMoney,
    CButton
  },
  props: {
    showTermDetail: {
      type: Boolean,
      default: false
    },
    termDetail: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      showDialog: false
    };
  },

  mounted() {

  },

  methods: {
    close() {
      this.$emit('close');
    }
  },
  watch: {
    'showTermDetail': function(value) {
      this.showDialog = value;
    }
  },
};
</script>

<style lang="scss" scoped>
.single-term-detail{
  .detail{
    .list{
      padding: 18px 18px 5px 18px;
      li{
        height: 54px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #E3E5E9;
        padding: 0 7px;
        font-size: 14px;
        &:last-of-type{
          border-bottom: none;
        }
        .key{
          color: #919DB3;
        }
        .value{
          color: #1B3155;
          font-weight: 700;
        }
        ::v-deep .c-money{
          .monetary-unit{
            transform: none;
            font-size: 12px;
            color: #1B3155;
          }
          .currency-num{
            font-size: 13px;
            margin-left: 0px;
            color: #1B3155;
            margin-left: 1px;
          }
        }
        .up{
          font-size: 12px;
          display: flex;
          align-items: center;
          .c-money{
            margin-right: 5px;
          }
          .before{
            text-decoration: line-through;
            color: #FF454F;
          }
        }
        .down{
          font-size: 12px;
          transform: scale(.84);
          color: #C4CAD5;
        }
      }
    }
    .c-button{
      width: 270px;
      height: 45px;
      margin-bottom: 22px;
    }
  }
}
</style>
