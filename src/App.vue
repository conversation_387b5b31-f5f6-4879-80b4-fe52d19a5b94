<template>
  <div id="app">
    <!--include 是需要缓存的组件,一般列表页需要缓存-->
    <!-- <keep-alive :include="['approved-review', 'bankStatement']"> -->
    <template v-if="load">
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive"></router-view>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive"></router-view>
    </template>
  </div>
</template>

<script>
  import { getUid, getCustId, getCurrentAppVersion, getChannelId, nativeObj, nativeObjType, getNcInfo, getSupportedFeatures } from "@/assets/js/native";
  import { getQueryString } from "@/assets/js/common";
  import { updatePathConfig } from '@/api/interface';
  import { getTheme } from "@/assets/themes";
  import { ThemeManager } from '@/utils/themeManager';

  export default {
      name: "App",
      data() {
        return {
          params: {},
          load: false
        };
      },
      computed: {
        productSource() {
          return process.env.VUE_APP_PRODUCT || 'palmcredit';
        }
      },
      methods: {
        setHeader(obj) {
          let self = this;
          console.log('obj', obj);
          localStorage.setItem('xcorssHeader', JSON.stringify({
            'X-APP-ID': 'Xcross',
            'X-CID': obj.cid,
            'X-UID': obj.uid,
            'X-REQUEST-ID': '',
            'X-APP-VERSION': obj.appVersion,
            'X-CHANNEL': getChannelId(),
            'X-Afmobi-RequestId': ''
          }));
          self.load = true;
        },
        initTheme() {
          const theme = getTheme(this.productSource);
          const themeManager = new ThemeManager(theme);

          themeManager.applyTheme(this.productSource);
          this.$store.commit('SET_THEMECOLOR', theme.themeColor);
        }
      },
      beforeCreate() {
        let self = this;
        // 不能影响主流程
        getSupportedFeatures().then(res => {
          let features = {}
          if (typeof res === 'string') {
            features = JSON.parse(res);
          }
          console.log('features', features)
          self.$store.commit('SET_SUPPORTEDFEATURES', features);
          updatePathConfig();
        }).catch(() => {
          console.log('getSupportedFeaturesError');
        })
        // flutter版本
        window.addEventListener("flutterInAppWebViewPlatformReady", function() {
          self.load = true;
          getUid().then(uid => {
            self.$store.commit('SET_UID', uid);
          });
        })
      },
      created() {
        console.log('============当前页面地址============: ', location.href);
        let self = this;
        if (process.env.VUE_APP_ENV === 'development') {
          self.load = true;
        }
        self.params = getQueryString();
        if (self.params.uid) { // 只有来源于ac的会带uid
          self.$store.commit('SET_ACWITHDRAWSUBMITDATA', location.href.split('?')[1]);
          localStorage.setItem('userUid', self.params.uid);
        } else {
          self.params.uid = localStorage.getItem('userUid');
        }

        this.initTheme();
        window.productSource = this.productSource;
        localStorage.setItem('productSource', this.productSource);

        let deviceType = '';
        // self.queryStageInfo();
        if (window.dopplerLib && window.dopplerLib.log) {
          self.load = true;
          deviceType = 'AC'; // AC来源
          self.$store.commit('SET_ADDHEADER', {
            source: self.params.source,
            channel: self.params.channel
          });
          self.$store.commit('SET_UID', self.params.uid);
        } else {
          if (nativeObj) {
              getUid().then(uid => {
                self.$store.commit('SET_UID', uid);
                if (window.productSource === 'palmcredit') { // pcx需要设置对应的请求头。
                  let obj = {
                    currentAppVersionName: '',
                    appVersion: '',
                    cid: '',
                    uid: ''
                  }
                  Promise.all([
                    getCustId().then(cid => {
                      obj.cid = cid;
                    }),
                    getUid().then(uid => {
                      obj.uid = uid;
                    }),
                    getCurrentAppVersion().then(appVersion => {
                      obj.appVersion = appVersion;
                    }),
                  ]).then(() => {
                    self.setHeader(obj);
                  }).catch(() => {
                    self.setHeader(obj);
                  })
                } else {
                  if (nativeObjType !== 'flutter') {
                    // 无论何种情况都需要保证能够页面能够加载
                    getNcInfo().then(res => {
                      console.log('getNcInfo', res);
                      if (res) {
                        localStorage.setItem('ncInfofromKc', JSON.stringify(res))
                        // 更新为nc的uid
                        self.$store.commit('SET_UID', res.uid);
                      }
                      self.load = true;
                    }).catch(() => {
                      self.load = true;
                    })
                  }
                }
            });
          }
        }
        console.log('deviceType', deviceType);
        self.$store.commit('SET_PRODUCTSOURCE', this.productSource);
        self.$store.commit('SET_DEVICETYPE', deviceType);
      },
      mounted() {
        console.log('process.env.NODE_ENV', process.env.NODE_ENV);
        console.log('process.env.VUE_APP_ENV', process.env.VUE_APP_ENV);
      }
  }
</script>

<style lang="scss">
html, body {
  height: 100%;
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  height: 100%;
}

nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
</style>
