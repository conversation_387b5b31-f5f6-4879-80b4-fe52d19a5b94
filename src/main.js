import Vue from 'vue'
import App from './App.vue'
import router from './router/index.js'
import store from './store/index.js'
import  'amfe-flexible/index.js'
import '@/assets/js/directive.js'
import "@/validators"
import cssVars from 'css-vars-ponyfill'
// import request from '@/assets/js/request.js'
import { singleToThousands, thousandsToFixed, floatAdd, floatMinus, singleToThousandsWithoutCarryBit } from '@/assets/js/common.js'
import global from '@/components/index';
import { httpRequest, showLoading, hideJsLoading, endPage, log } from "@/assets/js/native";
import { Dialog, Toast, PullRefresh, Loading, List, Search, Cell, Overlay, Popup } from 'vant';
import { decode } from 'js-base64';
// import './mock/withdraw.js';// withdraw模拟数据
// import './mock/repayment.js';// repayment模拟数据

import { Locale } from 'vant';
import enUS from 'vant/lib/locale/lang/en-US';
Locale.use('en-US', enUS);
console.log(process.env.VUE_APP_TITLE);

Vue.config.productionTip = false;
Vue.prototype.$http = httpRequest;
Vue.prototype.$dialog = Dialog;
Vue.prototype.$toast = Toast;
Vue.prototype.$newToast = (message) => {
  Toast({
    message: `<div style="width: 220px; word-break: keep-all;">${message}</div>`,
    type: 'html'
  })
};
Vue.prototype.$decode = decode;
Vue.prototype.common = {
  singleToThousandsWithoutCarryBit,
  singleToThousands,
  thousandsToFixed,
  floatAdd,
  floatMinus
};
Vue.prototype.$loading = showLoading;
Vue.prototype.$hideLoading = hideJsLoading;
Vue.prototype.$log = log;
Vue.use(PullRefresh);
Vue.use(Loading);
Vue.use(List);
Vue.use(Search);
Vue.use(Cell);
Vue.use(Dialog);
Vue.use(Overlay);
Vue.use(Loading);
Vue.use(Popup);
Vue.use(global);

// 控制标题
router.beforeEach((to, from, next) => {
  const productSource = localStorage.getItem('productSource');
  window.document.title = to.meta.title == undefined ? productSource : to.meta.title;
  // 路由退出时，也要埋点。
  if (from.name) {
    endPage(from.name)
  }
  next();
});

cssVars({})

//注册全局货币过滤器
Vue.filter('singleToThousands', function (value) {
  return singleToThousands(value);
});

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
