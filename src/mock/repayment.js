import Mock from 'mockjs';

Mock.mock('/repayment/index', 'post', function () {
  return {
      "traceId": "e903b78bc2ed6d59",
      "status": {
        "code": 0,
        "msg": "OK"
      },
      "data": {
        loanAmount: 11000,
        status: false,
        statusName: 'Under Review', // Delinquent
        loanDetail: {
          contractID: '*************',
          loanAmount: 1000,
          ForHowLong: 7,
          bankDetail: {
            bankType: 'Disbursement Account',
            bankName: 'UBA',
            cardNum: '(4325)'
          },
          applicationDate: '2021-5-11'
        },
        repaymentScheduleList: [{
          dueTime: '19/02/2021',
          all: 2,
          index: 1,
          repaymentAmount: 30000.67,
          status: 0,
          statusName: 'Paid',
        }, {
          dueTime: '19/02/2022',
          all: 6,
          index: 2,
          repaymentAmount: 30000.67,
          status: 1,
          statusName: 'Delinquent',
        }, {
          dueTime: '19/03/2022',
          all: 6,
          index: 2,
          repaymentAmount: 30000.67,
          status: 2,
          statusName: 'Outstanding',
        }]
      }
    };
})

Mock.mock('/repayment/repayment', 'post', function () {
  return {
    "traceId": "e903b78bc2ed6d59",
    "status": {
      "code": 0,
      "msg": "OK"
    },
    "data": {
      loanAmount: 2000,
      coupons: 0,
      loanDetail: {
        status: 2,
        statusName: 'Delinquent',
        principal: 1100,
        interest: 1100,
        defaultCharge: 1100,
        amountDue: 1100
      }
    }
  };
})