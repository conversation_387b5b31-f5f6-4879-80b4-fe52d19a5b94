import Mock from 'mockjs';

Mock.mock('/withdraw/index', 'post', function () {
  return Mock.mock({
    "traceId": "e903b78bc2ed6d59",
    "status": {
      "code": 0,
      "msg": "OK"
    },
    "data": {
      loanableLimit: 50000,
      repayment: {
        interestNum: 2400,
        interestRate: '16%',
        plan: 1400,
        dueTime: '19/02/2021'
      },
      repaymentDetailList: [{
        dueTime: '19/02/2021',
        all: 2,
        index: 1,
        repaymentAmount: 30000.67
      }, {
        dueTime: '19/02/2022',
        all: 6,
        index: 2,
        repaymentAmount: 30000.67
      }],
      bankDetail: {
        bankType: 'Disbursement Account',
        bankName: 'UBA',
        cardNum: '(4325)'
      },
      longList: [{
          typeName: '7',
      }, {
          typeName: '14',
      }, {
          typeName: '21',
      }, {
          typeName: '2*7',
      }, {
          typeName: '2*14',
      }],
      purposeList: [{
        reason: 'PuyCar1'
      },{
        reason: 'BuyCar2'
      },{
        reason: 'BuyCar3'
      },{
        reason: 'BuyCar4'
      },{
        reason: 'BuyCar5'
      },{
        reason: 'BuyCar6'
      },{
        reason: 'BuyCar7'
      },{
        reason: 'BuyCar8'
      },{
        reason: 'BuyCar9'
      },{
        reason: 'BuyCar10'
      },{
        reason: 'BuyCar11'
      },{
        reason: 'BuyCar12'
      },{
        reason: 'BuyCar13'
      }]
    }
  });
});

Mock.mock('/withdraw/status', 'post', function () {
  return Mock.mock({
    "traceId": "e903b78bc2ed6d59",
    "status": {
      "code": 0,
      "msg": "OK"
    },
    "data": {
      status: true,
      applicationTime: '13/07/2018',
      loanAmount: 21800,
      bankDetail: {
        bankType: 'Disbursement Account',
        bankName: 'UBA',
        cardNum: '(4325)'
      }
    }
  });
});