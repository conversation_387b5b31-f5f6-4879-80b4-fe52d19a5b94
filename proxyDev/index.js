// 基础代理配置（所有产品共用的配置）
const baseProxy = {
  // '/blc-service' 配置已移动到各产品特定文件中
  '/ims-service|/cfk-service|/bfs-service|/aad-service': {
    target: 'https://palmcredit-xcorss.dexintec.cn/'
  },
  '/dc': {
    target: 'https://formoney-bigdata-api.dexintec.cn/'
  },
  '/lts': {
    target: 'https://bigdata-common.dexintec.cn/'
  },
  '/app': {
    target: 'https://loanstation-bigdata-api.dexintec.cn/'
  }
};

// 导出代理配置生成函数
module.exports = function getProxyConfig() {
  // 获取当前产品
  const product = process.env.VUE_APP_PRODUCT || '';

  let productProxy = {};

  // 尝试加载产品特定的代理配置
  if (product) {
    try {
      productProxy = require(`./all/${product}`) || {};
      console.log(`已加载 ${product} 产品的代理配置`);
    } catch (err) {
      console.log('未找到产品特定代理配置，仅使用基础配置');
    }
  }

  // 合并基础配置和产品特定配置
  return { ...baseProxy, ...productProxy };
};
