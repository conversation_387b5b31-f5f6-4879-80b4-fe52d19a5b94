{"name": "hello-world", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "dev:wayacredit": "vue-cli-service serve --mode wayacredit-uat --no-module", "build:wayacredit-uat": "vue-cli-service build --mode wayacredit-uat --no-module", "build:wayacredit": "vue-cli-service build --mode wayacredit --no-module", "build:ponykash-uat": "vue-cli-service build --no-module --mode ponykash-uat", "build:ponykash": "vue-cli-service build --no-module --mode ponykash", "build:truemoneyios": "vue-cli-service build --mode truemoneyios --no-module", "build:truemoneyios-uat": "vue-cli-service build --mode truemoneyios-uat", "build:creditwiseios": "vue-cli-service build --mode creditwiseios --no-module", "build:creditwiseios-uat": "vue-cli-service build --mode creditwiseios-uat --no-module", "build:newcredit": "vue-cli-service build --mode newcredit --no-module", "build:newcredit-uat": "vue-cli-service build --mode newcredit-uat --no-module", "build:palmcreditpro": "vue-cli-service build --mode palmcreditpro --no-module", "build:palmcreditpro-uat": "vue-cli-service build --mode palmcreditpro-uat --no-module", "build:correctloan": "vue-cli-service build --mode correctloan --no-module", "build:correctloan-uat": "vue-cli-service build --mode correctloan-uat --no-module", "build:formoney": "vue-cli-service build --mode formoney --no-module", "build:formoney-uat": "vue-cli-service build --mode formoney-uat --no-module", "build:nairaloans": "vue-cli-service build --mode nairaloans --no-module", "build:nairaloans-uat": "vue-cli-service build --mode nairaloans-uat --no-module", "build:realcredit": "vue-cli-service build --mode realcredit --no-module", "build:realcredit-uat": "vue-cli-service build --mode realcredit-uat --no-module", "build:xcash": "vue-cli-service build --mode xcash --no-module", "build:xcash-uat": "vue-cli-service build --mode xcash-uat --no-module", "build:xcrosscash": "vue-cli-service build --mode xcrosscash --no-module", "build:xcrosscash-uat": "vue-cli-service build --mode xcrosscash-uat --no-module", "build:palmcredit": "vue-cli-service build --mode palmcredit --no-module", "build:palmcredit-uat": "vue-cli-service build --mode palmcredit-uat --no-module", "lint": "vue-cli-service lint"}, "dependencies": {"amfe-flexible": "^2.2.1", "big.js": "^6.2.1", "core-js": "^3.8.3", "css-vars-ponyfill": "^2.4.8", "js-base64": "^3.7.2", "lottie-web": "^5.12.2", "underscore": "^1.13.1", "vant": "^2.11.2", "vee-validate": "^3.4.15", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "axios": "^0.21.4", "babel-plugin-import": "^1.13.5", "clipboard": "^2.0.11", "compression-webpack-plugin": "^10.0.0", "crypto-js": "^4.1.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "html-webpack-plugin": "^5.5.0", "javascript-obfuscator": "^4.1.0", "mockjs": "^1.1.0", "postcss-pxtorem": "^6.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vconsole-webpack-plugin": "^1.7.3", "vue-template-compiler": "^2.6.14", "webpack-obfuscator": "^3.5.1"}}